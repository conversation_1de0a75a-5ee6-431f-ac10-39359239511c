﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Title>PurchaseManager Shared</Title>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Blazored.FluentValidation" Version="2.2.0" />
        <PackageReference Include="Breeze.Sharp.Standard.Fork" Version="0.10.3" />
        <PackageReference Include="ClosedXML" Version="0.102.3" />
        <PackageReference Include="Humanizer" Version="2.14.1" />
        <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="8.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.6" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor" Version="2.2.0" />
        <PackageReference Include="Microsoft.AspNetCore.Razor" Version="2.2.0" />
        <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="8.0.6" />
        <PackageReference Include="NetTopologySuite" Version="2.5.0" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="ObjectCloner.Extensions" Version="2.0.1" />
        <PackageReference Include="SourceGenerators.AutoNotify" Version="1.3.0" />
        <PackageReference Include="System.Net.Http" Version="4.3.4" />
        <PackageReference Include="System.Net.Http.Json" Version="8.0.0" />
        <PackageReference Include="System.Security.Cryptography.Xml" Version="8.0.1" />
        <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\Utils\PurchaseManager.SourceGenerator\PurchaseManager.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
        <ProjectReference Include="..\PurchaseManager.Constants\PurchaseManager.Constants.csproj" />
        <ProjectReference Include="..\PurchaseManager.Infrastructure.AuthorizationDefinitions\PurchaseManager.Infrastructure.AuthorizationDefinitions.csproj" />
        <ProjectReference Include="..\PurchaseManager.Shared.DataInterfaces\PurchaseManager.Shared.DataInterfaces.csproj" />
        <ProjectReference Include="..\PurchaseManager.Shared.Localizer\PurchaseManager.Shared.Localizer.csproj" />
    </ItemGroup>
    <ItemGroup>
        <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
    </ItemGroup>

    <ItemGroup>
        <AdditionalFiles Include="EntityGeneratorConfig.json" />
    </ItemGroup>
</Project>
