﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Aop;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Storage;
using static Microsoft.AspNetCore.Http.StatusCodes;
using TS.GRPC;
using GetVendorDto=PurchaseManager.Shared.Dto.Vendor.GetVendorDto;

namespace PurchaseManager.Server.Managers;

[ApiResponseException]
public class VendorManager : IVendorManager
{
    private readonly IMapper _autoMapper;
    private readonly ApplicationPersistenceManager _dbContext;
    private readonly ApplicationDbContext _applicationDbContext;

    private readonly ILogger<VendorManager> _logger;
    private readonly IStringLocalizer<Global> L;
    private readonly IMapper _mapper;
    private readonly ApplicationPersistenceManager _persistenceManager;


    public VendorManager(ILogger<VendorManager> logger,
        IStringLocalizer<Global> l, ApplicationPersistenceManager persistenceManager, IMapper autoMapper, IMapper mapper,
        ApplicationDbContext applicationDbContext)
    {
        _logger = logger;
        L = l;
        _persistenceManager = persistenceManager;
        _dbContext = persistenceManager;
        _autoMapper = autoMapper;
        _mapper = mapper;
        _applicationDbContext = applicationDbContext;

    }

    public string Metadata()
        => _dbContext.Metadata();

    public async Task<ApiResponse> BuildViewModel(string number)
    {
        var vendor = await _dbContext.Context.Vendors.FindAsync(number);

        if (vendor is null)
        {
            return new ApiResponse(Status404NotFound, L["Not Found"]);
        }

        var result = _autoMapper.Map<Vendor, Shared.Models.GetVendorDto>(vendor);

        return new ApiResponse(Status200OK, L["Operation Successful"], result);
    }

    public async Task<ApiResponse> UpdateVendor(Shared.Models.GetVendorDto vendorViewModel)
    {
        var vendor = await _dbContext.Context.Vendors.SingleOrDefaultAsync(v => v.Number == vendorViewModel.Number);

        if (vendor == null)
        {
            _logger.LogInformation(L["The vendor {0} doesn't exist", vendorViewModel.Number]);
            return new ApiResponse(Status404NotFound, L["The Vendor doesn't exist"]);
        }

        _autoMapper.Map(vendorViewModel, vendor);

        try
        {
            await _dbContext.Context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError($"Updating Vendor exception: {ex.GetBaseException().Message}");
            return new ApiResponse(Status400BadRequest, L["Operation Failed"]);
        }

        return new ApiResponse(Status200OK, L["Operation Successful"]);
    }

    public async Task<ApiResponse> CreateVendor(Shared.Models.GetVendorDto vendorViewModel)
    {
        var vendor = await _dbContext.Context.Vendors.SingleOrDefaultAsync(v => v.Number == vendorViewModel.Number);

        if (vendor != null)
        {
            _logger.LogInformation(L["The vendor {0} does exist", vendorViewModel.Number]);
            return new ApiResponse(Status404NotFound, L["The Vendor does exist"]);
        }
        _autoMapper.Map(vendorViewModel, (Vendor)null);

        try
        {
            await _dbContext.Context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError($"Creating Vendor exception: {ex.GetBaseException().Message}");
            return new ApiResponse(Status400BadRequest, L["Operation Failed"]);
        }

        return new ApiResponse(Status200OK, L["Operation Successful"]);
    }

    public async Task<Vendor> GetVendorByNumber(string number) => await _dbContext.Context.Vendors.FindAsync(number);

    public async Task<ApiResponse> GetVendor(string number)
    {
        var vendor = await GetVendorByNumber(number);
        return vendor != null ?
            new ApiResponse(Status200OK, L["Operation Successful"], _autoMapper.Map<GetVendorDto>(vendor)) :
            new ApiResponse(Status404NotFound, L["Operation Failed"]);
    }

    public IQueryable<Vendor> GetVendors(VendorFilter filter, string query)
    {
        IQueryable<Vendor> vendorQueryable = _persistenceManager.GetEntities<Vendor>().AsNoTracking()
            .Where(i =>
                (filter.Query == null || i.Name.Contains(filter.Query)) &&
                (filter.Phone == null || i.Phone.Contains(filter.Phone)) &&
                (filter.Number == null || i.Number.Contains(filter.Number)) &&
                (filter.Name == null || i.Name.Contains(filter.Name) ||
                 filter.Name == null || i.SearchName.Contains(filter.Name)))
            .OrderByDescending(i => i.RowId);

        if (!query!.Contains("take"))
        {
            vendorQueryable = vendorQueryable.Take(10);
        }

        if (!query!.Contains("skip"))
        {
            vendorQueryable = vendorQueryable.Skip(0);
        }

        return vendorQueryable;
        //return _autoMapper.ProjectTo<VendorList>(vendorQueryable);
    }
    public async Task<ApiResponse> SearchAutocomplete(string number, string name, string queryString,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _dbContext.Context.Vendors.AsNoTracking();

            switch (string.IsNullOrEmpty(number))
            {
                case false when !string.IsNullOrEmpty(name):
                    query = query.Where(item => item.Number.Contains(number) || item.Name.Contains(name));
                    break;
                case false:
                    query = query.Where(item => item.Number.Contains(number) || item.Name.Contains(number));
                    break;
                default:
                    {
                        if (!string.IsNullOrEmpty(name))
                        {
                            query = query.Where(item => item.Name.Contains(name) || item.Number.Contains(name));
                        }
                        break;
                    }
            }

            if (!queryString!.Contains("take"))
            {
                query = query.Take(100);
            }

            if (!queryString!.Contains("skip"))
            {
                query = query.Skip(0);
            }

            var result = await query
                .ToListAsync(cancellationToken);

            var data = _mapper.Map<List<Shared.Models.GetVendorDto>>(result);
            return new ApiResponse(Status200OK, L["Search Vendor successfully"], data);
        }
        catch
        {
            return new ApiResponse(Status404NotFound, L["Error searching Vendor"]);
        }
    }

    public async Task<ApiResponse> SyncVendor(List<SyncVendor> listVendor)
    {
        try
        {
            Console.WriteLine("Vendors are syncing...");
            if (listVendor.Count == 0)
            {
                return new ApiResponse(Status404NotFound, L["Data is invalid"]);
            }
            var listVendorMapped = new List<Vendor>();
            listVendor.ForEach(x =>
            {
                //convert sync vendor to vendor entity
                listVendorMapped.Add(ToEntity(x));
            });

            var vendorNumbers = listVendorMapped.AsParallel().Select(item => item.Number.Trim()).ToList();
            var allVendor = _applicationDbContext.Vendors.AsNoTracking().AsParallel().ToList();

            var notExistedVendors = vendorNumbers.Except(allVendor.Select(v => v.Number)).ToList();

            var vendorToAdd = listVendorMapped.Where(x => notExistedVendors.Contains(x.Number));
            var vendorToUpdate = listVendorMapped.Where(x => !notExistedVendors.Contains(x.Number));
            Console.WriteLine("vendor updating...");

            var vendors = vendorToUpdate.ToList();
            if (vendors.Count > 0)
            {
                vendors.ForEach(x =>
                {
                    _applicationDbContext.Vendors.Update(x);
                    //ignore update row id
                    _applicationDbContext.Entry(x).Property(v => v.RowId).IsModified = false;
                }
                );
            }

            var toAdd = vendorToAdd.ToList();
            if (toAdd.ToList().Count > 0)
            {
                toAdd.ForEach(x =>
                {
                    _applicationDbContext.Vendors.Add(x);
                }
                );
            }
            await _applicationDbContext.SaveChangesAsync();
            Console.WriteLine("Vendors were synced...");

            return new ApiResponse(Status200OK, L["Update: {0} vendor, add {1} vendor", vendors.Count(), toAdd.Count()]);
        }
        catch (Exception ex)
        {
            return new ApiResponse(Status500InternalServerError, L["Error while sync Vendor: "] + ex.Message);

        }
    }

    private static Vendor ToEntity(SyncVendor syncVendor)
    {
        var vendor = new Vendor()
        {
            Number = syncVendor.Number,
            Name = syncVendor.Name,
            SearchName = syncVendor.SearchName,
            Address = syncVendor.Address,
            City = syncVendor.City,
            Contact = syncVendor.Contact,
            Phone = syncVendor.Phone,
            Telex = syncVendor.Telex,
            VendorPostingGroup = syncVendor.VendorPostingGroup,
            CurrencyCode = syncVendor.CurrencyCode,
            PurchaserCode = syncVendor.PurchaserCode,
            ShipmentMethodCode = syncVendor.ShipmentMethodCode,
            ShippingAgentCode = syncVendor.ShippingAgentCode,
            CountryCode = syncVendor.CountryCode,
            Blocked = (Constants.VendorBlockedType)syncVendor.Blocked,
            PayToVendorNumber = syncVendor.PayToVendorNumber,
            PaymentMethodCode = syncVendor.PaymentMethodCode,
            LastDateModified = syncVendor.LastDateModified.ToDateTime(),
            Fax = syncVendor.Fax,
            VatregistrationNumber = syncVendor.VatregistrationNumber,
            PostCode = syncVendor.PostCode,
            County = syncVendor.County,
            Email = syncVendor.Email,
            HomePage = syncVendor.HomePage,
            VatbusinessPostingGroup = syncVendor.VatbusinessPostingGroup,
            VendorGroup = syncVendor.VendorGroup,
            Internal = syncVendor.Internal,
            Status = (Constants.VendorStatusType)syncVendor.Status
        };
        if (vendor.LastUserModified is not null)
        {
            vendor.LastUserModified = syncVendor.LastUserModified;
        }
        if (vendor.CertificateOfRegistration is not null)
        {
            vendor.CertificateOfRegistration = syncVendor.CertificateOfRegistration;
        }
        if (vendor.LicenseDate is not null)
        {
            vendor.LicenseDate = syncVendor.LicenseDate.ToDateTime();
        }
        if (vendor.LastModifiedTime is not null)
        {
            vendor.LastModifiedTime = vendor.LastModifiedTime.Value;
        }
        if (vendor.ExprireLicenseDate is not null)
        {
            vendor.ExprireLicenseDate = vendor.ExprireLicenseDate.Value;
        }
        if (vendor.CreateDate is not null)
        {
            vendor.CreateDate = vendor.CreateDate.Value;
        }
        if (vendor.RegistrationOfExpirationDate is not null)
        {
            vendor.RegistrationOfExpirationDate = vendor.RegistrationOfExpirationDate.Value;
        }
        return vendor;
    }

    public async Task<ApiResponse> ToggleVendorStatus(string vendorNumber)
    {
        try
        {
            var foundVendor = _dbContext.Context.Vendors.FirstOrDefault(x => x.Number.Trim() == vendorNumber);
            if (foundVendor is null)
            {
                return new ApiResponse(Status404NotFound, L["Operation Failed"], false);
            }
            //toggle block status
            var vendorStatusAfterUpdate = foundVendor.Blocked == Constants.VendorBlockedType.Blocked
                ? Constants.VendorBlockedType.NoBlock : Constants.VendorBlockedType.Blocked;
            foundVendor.Blocked = vendorStatusAfterUpdate;
            await _dbContext.Context.SaveChangesAsync();
            return new ApiResponse(Status200OK, L["Operation Successfully"],
            vendorStatusAfterUpdate == Constants.VendorBlockedType.Blocked);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Toggle Vendor Status has exception: {ex.GetBaseException().Message}");
            return new ApiResponse(Status400BadRequest, L["Operation Failed"], false);
        }
    }

}
