{
    "Serilog": {
        "MinimumLevel": {
            "Default": "Debug"
        }
    },
    "ConnectionStrings": {
        "PostgresConnection": "host=localhost;database=PurchaseManager;user id=postgres;password=password123",
        // Azure Hosting: Use format below.
        // "DefaultConnection": "Data Source=tcp:<SQL_SERVER_NAME>.database.windows.net,1433;Initial Catalog=<DATABASE_NAME>;Persist Security Info=False;User ID=<ADMIN_USER>;Password=<ADMIN_PASSWORD>;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;MultipleActiveResultSets=True;"
        "DefaultConnection": "Server=**************;Database=PurchaseManager;User Id=trungit;Password=************;Trusted_Connection=False;Encrypt=False;MultipleActiveResultSets=true;"
        // "DefaultConnection": "Server=************;Database=PurchaseManager;User Id=trungit;Password=************;Trusted_Connection=False;Encrypt=False;MultipleActiveResultSets=true;"
    }
}