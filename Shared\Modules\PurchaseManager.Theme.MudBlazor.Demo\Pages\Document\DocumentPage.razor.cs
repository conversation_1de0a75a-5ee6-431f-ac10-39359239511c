﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using MudBlazor;
using MudBlazor.Extensions.Components;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Models.PO;
using PurchaseManager.Shared.Providers;
using PurchaseManager.Theme.Material.Demo.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Pages.Document;

public class DocumentPageBase : ComponentBase
{
    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; }
    [Inject]
    private AuthenticationStateProvider AuthStateProvider { get; set; }
    [Inject]
    protected IStringLocalizer<Global> L { get; set; }
    [Inject]
    protected HttpClient HttpClient { get; set; }
    [Inject]
    protected IViewNotifier ViewNotifier { get; set; }
    [Inject]
    protected NavigationManager Navigation { get; set; }
    [Inject]
    protected IPurchaseOrderApiClient PurchaseOrderApiClient { get; set; }
    [Inject]
    protected IVendorApiClient VendorApiClient { get; set; }
    protected UserViewModel UserViewModel = new UserViewModel();
    protected ListFilePage ListFilePageReceiveRef { get; set; } = new ListFilePage();
    protected ListFilePage ListFilePageBillRef { get; set; } = new ListFilePage();
    protected MudDateRangePicker FromToDatePickerRef { get; set; } = new MudDateRangePicker();
    protected MudAutocomplete<GetVendorDto> AutoComplete { get; set; } = new MudAutocomplete<GetVendorDto>();
    protected List<POHeaderGetDto> PurchaseHeaders { get; set; } = [];
    protected DateRange DateRange { get; set; } = new DateRange();
    protected MudExList<POHeaderGetDto> MudExListRef { get; set; }
    protected GetVendorDto SelectedVendor { get; set; }
    private int PageSize { get; set; } = 10;
    private int PageIndex { get; set; }
    // private int TotalItemsCount { get; set; }
    protected string DocumentNo { get; set; }

    protected async Task<IEnumerable<GetVendorDto>> ItemSearch(string value, CancellationToken token)
    {
        var apiResponse = await VendorApiClient.GetVendorsByNumber(value, token);

        return apiResponse.IsSuccessStatusCode ? apiResponse.Result : [];
    }

    protected override async Task OnInitializedAsync()
    {
        UserViewModel = null;
        var user = (await AuthenticationStateTask).User;

        if (user.Identity is { IsAuthenticated: true })
        {
            UserViewModel = await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
        }

        var isVendor = UserViewModel != null && UserViewModel.Roles.Any(x => x.Contains("IsVendor"));

        if (isVendor) { await LoadData(); }
        await base.OnInitializedAsync();
    }
    protected override void OnInitialized()
    {
        var today = DateTime.Today;
        var month = new DateTime(today.Year, today.Month, 1);
        // the last day of the current month
        var last = month.AddMonths(1).AddDays(-1);
        //fromToDatePickerRef.DateRange = new DateRange(first, last);
        DateRange = new DateRange(month, last);
        base.OnInitialized();
    }


    protected async Task LoadData()
    {
        try
        {
            PurchaseHeaders = [];
            var vendorId = UserViewModel.Roles.Any(x => x.Contains("IsVendor"))
                ? UserViewModel.VendorCode
                : AutoComplete.Value.Number;

            var filters = new PurchaseOrderFilter
            {
                FromDate = DateRange.Start,
                ToDate = DateRange.End,
                PageIndex = PageIndex,
                PageSize = PageSize,
                Vendor = vendorId
            };

            var response = await PurchaseOrderApiClient.GetPoHeadersAsync(filters);

            if (!response.IsSuccessStatusCode)
            {
                ViewNotifier.Show(L[response.Message], ViewNotifierType.Error, L["Operation Failed"]);
            }
            else if (response.Result is not null)
            {
                PurchaseHeaders = new List<POHeaderGetDto>(response.Result.Data.ToList());
                ViewNotifier.Show(L["One item found"], ViewNotifierType.Success, L["Operation Successful"]);
            }
            else
            {
                ViewNotifier.Show(L["No item found"], ViewNotifierType.Warning, L["Operation Successful"]);
            }

        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }


    protected async Task ShowPOByVendorNumber(GetVendorDto vendor)
    {
        SelectedVendor = vendor;
        if (vendor is null || vendor.Number.Length < 5)
        {
            await Task.CompletedTask;
        }
        else
        {
            await LoadData();
        }
    }

    protected async Task FilterByDate()
    {

        if (FromToDatePickerRef.DateRange is null)
        {
            return;
        }
        DateRange = FromToDatePickerRef.DateRange;
        await FromToDatePickerRef.CloseAsync();
        await LoadData();
    }

}
