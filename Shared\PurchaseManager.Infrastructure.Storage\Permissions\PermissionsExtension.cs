﻿using System.ComponentModel.DataAnnotations;
namespace PurchaseManager.Infrastructure.Storage.Permissions;

public static partial  class Permissions
{
    public static class Item
    {

        [Display(Name = "CreateItemPermission")]
        public const string Create = "Item.Create";
        [Display(Name = "CreateOrUpdateItemPermission")]
        public const string CreateOrUpdate = "Item.CreateOrUpdate";

        [Display(Name = "UpdateItemPermission")]
        public const string Update = "Item.Update";

        [Display(Name = "ReadItemPermission")]
        public const string Read = "Item.Read";

        [Display(Name = "DeleteItemPermission")]
        public const string Delete = "Item.Delete";
    }
    public static class VendorItem
    {

        [Display(Name = "CreateVendorItemPermission")]
        public const string Create = "VendorItem.Create";

        [Display(Name = "UpdateVendorItemPermission")]
        public const string Update = "VendorItem.Update";

        [Display(Name = "ReadVendorItemPermission")]
        public const string Read = "VendorItem.Read";

        [Display(Name = "DeleteVendorItemPermission")]
        public const string Delete = "VendorItem.Delete";
    }
    public static class UnitOfMeasure
    { 
        [Display(Name = "CreateUnitOfMeasurePermission")]
        public const string Create = "UnitOfMeasure.Create";

        [Display(Name = "UpdateUnitOfMeasurePermission")]
        public const string Update = "UnitOfMeasure.Update";

        [Display(Name = "ReadUnitOfMeasurePermission")]
        public const string Read = "UnitOfMeasure.Read";

        [Display(Name = "DeleteUnitOfMeasurePermission")]
        public const string Delete = "UnitOfMeasure.Delete";
    }
    public static class SalesPrice
    {
        [Display(Name = "CreateSalesPricePermission")]
        public const string Create = "SalesPrice.Create";

        [Display(Name = "UpdateSalesPricePermission")]
        public const string Update = "SalesPrice.Update";

        [Display(Name = "ReadSalesPricePermission")]
        public const string Read = "SalesPrice.Read";

        [Display(Name = "DeleteSalesPricePermission")]
        public const string Delete = "SalesPrice.Delete";
    }
}
