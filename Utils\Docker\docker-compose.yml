version: '3.5'

services:
  purchasemanager:
    build:
      context: ../../../
      dockerfile: ./src/Utils/Docker/Dockerfile
    #image: localhost:32000/purchasemanager:v1.1
    ports:
      - 53415:80
      - 53443:443
    depends_on:
      - sqlserver
    environment:
      - ASPNETCORE_ENVIRONMENT=Development #Consider changing this in Production
      - Serilog__MinimumLevel__Default=Debug #Consider changing this in Production
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=blazor_boilerplate;Trusted_Connection=True;MultipleActiveResultSets=true;User=sa;Password=yourVeryStrong(!)Password;Integrated Security=false
      - PurchaseManager__UseSqlServer=true
      - PurchaseManager__ApplicationUrl=purchasemanager
      - PurchaseManager__IS4ApplicationUrl=purchasemanager
      - PurchaseManager__CertificatePassword=Admin123
      - ASPNETCORE_URLS=https://+:443;http://+80
      - ASPNETCORE_Kestrel__Certificates__Default__Password=Admin123
      - ASPNETCORE_Kestrel__Certificates__Default__Path=aspnetapp.pfx
    networks:
      - bb
    restart: on-failure

  sqlserver:
    image: mcr.microsoft.com/mssql/server
    volumes:
      - dbdata:/var/opt/mssql
    environment:
      - SA_PASSWORD=yourVeryStrong(!)Password
      - ACCEPT_EULA=Y
    ports:
      - 1533:1433 #expose port, so can connect to it using host: 'localhost,1533' | user: sa, password: yourVeryStrong(!)Password
    networks:
      - bb

volumes:
  dbdata:

networks:
  bb:
    name: bb_network
    ipam:
      driver: default
