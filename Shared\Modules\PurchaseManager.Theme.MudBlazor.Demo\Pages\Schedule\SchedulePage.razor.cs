﻿using Breeze.Sharp.Core;
using Heron.MudCalendar;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using MudBlazor;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
namespace PurchaseManager.Theme.Material.Demo.Pages.Schedule
{
    public partial class SchedulePage : ComponentBase
    {

        [Inject] public IStringLocalizer<Global> L { get; set; }
        [Inject] protected IViewNotifier viewNotifier { get; set; }
        [Inject] public IApiClient apiClient { get; set; }
        [CascadingParameter]
        private Task<AuthenticationState> AuthenticationStateTask { get; set; }
        protected List<CalendarItem> _events { get; set; } = new List<CalendarItem>();

        protected bool IsLoading { get; set; }
        protected bool IsPurchaseUser { get; set; }
        protected bool IsMKT { get; set; }

        protected override async Task OnInitializedAsync()
        {

            IsLoading = true;
            var authState = await AuthenticationStateTask;
            var user = authState.User;
            IsPurchaseUser = user.IsPurchaseUser();
            IsMKT = user.IsMKT();
            await base.OnInitializedAsync();
            IsLoading = false;
        }
        protected async Task LoadSchedule(ScheduleFilter filter)
        {
            try
            {
                if (IsMKT)
                {
                    filter.IsMKT = true;
                }
                var resp = await apiClient.GetSchedules(filter);

                if (resp.Any())
                {
                    resp.ForEach(s =>
                        {
                            _events.Add(
                                new CalendarItem
                                {
                                    Start = s.DueDate,
                                    End = s.DueDate.AddHours(1),
                                    Text = s.Number,
                                });
                        });
                }
            }
            catch (Exception ex)
            {
                viewNotifier.Show(L["Data not available."] + ex.Message, ViewNotifierType.Info);

            }
        }
        protected async Task DateRangeChanged(DateRange dateRange)
        {
            // Load events from service
            var startDate = dateRange.Start;
            var endDate = dateRange.End;
            _events.Clear();

            await LoadSchedule(new ScheduleFilter()
            {
                From = startDate,
                To = endDate
            });
        }

    }
}



