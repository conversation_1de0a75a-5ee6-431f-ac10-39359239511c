﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>PurchaseManager.Theme.Material.Admin</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
      <Content Remove="Pages\Admin\ReasonDemand.razor" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Blazored.TextEditor" Version="1.1.0" />
        <PackageReference Include="System.Net.Http" Version="4.3.4" />
        <PackageReference Include="System.Net.Http.Json" Version="8.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\PurchaseManager.UI.Base\PurchaseManager.UI.Base.csproj" />
        <ProjectReference Include="..\PurchaseManager.Theme.MudBlazor\PurchaseManager.Theme.MudBlazor.csproj" />
    </ItemGroup>

    <Target Name="PostBuild" AfterTargets="PostBuildEvent">
        <Copy SourceFiles="$(TargetPath)" DestinationFolder="$(SolutionDir)Server\PurchaseManager.Server\Themes\MudBlazor\" />
    </Target>

    <ItemGroup>
      <UpToDateCheckInput Remove="Pages\Admin\ReasonDemand.razor" />
    </ItemGroup>

    <ItemGroup>
      <_ContentIncludedByDefault Remove="Pages\Admin\ReasonDemand.razor" />
    </ItemGroup>

    <ItemGroup>
      <None Include="Pages\Admin\ReasonDemand.razor" />
    </ItemGroup>
</Project>
