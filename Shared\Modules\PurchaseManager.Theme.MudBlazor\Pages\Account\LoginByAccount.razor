@using HorizontalAlignment=MudBlazor.HorizontalAlignment
@inherits LoginVendorPage
@page "/account-login"

@layout LoginLayout

<AuthorizeView Context="_">
    <Authorized>
        <LoadingBackground>
            <label>@L["Loading"]</label>
        </LoadingBackground>
    </Authorized>
    <NotAuthorized>
        @if (loginViewModel == null)
        {
            <LoadingBackground />
        }
        else
        {
            @if (loginViewModel.EnableLocalLogin)
            {

                <MudStack Justify="Justify.Center" Style="height: 98vh; min-width: 400px;">
                    @if(isShowAlertForgetPassword)
                    {
                        <MudAlert Severity="Severity.Info" ShowCloseIcon ContentAlignment="HorizontalAlignment.Center"
                                  CloseIconClicked="@(() => { isShowAlertForgetPassword = false; })">
                            <strong>@L["Please contact admin system to reset your password"]</strong>
                        </MudAlert>
                    }
                    <EditForm Model="@loginViewModel" OnValidSubmit="@SubmitLogin">
                    <MudCard Elevation="2" Class="pa-4" >
                        <MudCardHeader>
                            <CardHeaderContent>
                                <div class="logo">
                                    <MudStack Row="true" Justify="Justify.Center" AlignItems="AlignItems.Center">
                                        <a href="/" title="@appState.AppName Home"><img src=@($"{Module.ContentPath}/images/logo-trungson.png") style="width:96px;" title="@appState.AppName Home" alt="@appState.AppName"/></a>
                                        @* <a href="/" title="@appState.AppName Home"><img src=@($"{Module.ContentPath}/images/logo-dongwha.png") style="width:96px;" title="@appState.AppName Home" alt="@appState.AppName"/></a> *@
                                    </MudStack>
                                    <br/>
                                </div>
                                <MudText Typo="Typo.h5" Align="Align.Center">@L["Login"]</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <FluentValidationValidator />
                            <MudValidationSummary />
                            <MudTextField AutoFocus="true" @bind-Value="@loginViewModel.UserName" Label=@L["UserName"] AdornmentIcon="@Icons.Material.Filled.Person" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>

                            <MudTextField @bind-Value="@loginViewModel.Password" Label=@L["Password"] AdornmentIcon="@Icons.Material.Outlined.Lock" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"] InputType="InputType.Password"></MudTextField>

                            <MudStack Row>
                                @if (loginViewModel.AllowRememberLogin)
                                {<MudCheckBox  T="bool" @bind-Value="@loginViewModel.RememberMe" Class="ml-n2">@L["Keep me logged in"]</MudCheckBox>}
                                <div class="pt-3" style="text-align: right; width: 100%;"><MudLink OnClick="@(()  => { isShowAlertForgetPassword = true;})">@L["Forget password"]</MudLink></div>
                            </MudStack>
                            <MudStack Justify="Justify.Center" AlignItems="AlignItems.Center" Class="mt-3">
                                <MudButton ButtonType="ButtonType.Submit" Variant="Variant.Filled" Color="Color.Primary" Class="" Style="width: 80%;">@L["Login"]</MudButton>
                            </MudStack>
                        </MudCardContent>
                        <MudStack Row>
                        </MudStack>
                        <MudCardActions>
                            <div style="text-align: left; width: 100%;">
                                <MudLink
                                    Href="/account/register">@L["Create vendor account."]</MudLink>
                            </div>
                        </MudCardActions>
                    </MudCard>
                </EditForm>
                </MudStack>
                <MudExpansionPanels Elevation="2" Class="my-4">
                    <MudExpansionPanel @bind-Expanded="@forgotPasswordToggle">
                        <TitleContent>
                            <MudText Typo="Typo.h6">
                                <MudIcon Icon="@Icons.Material.Filled.Lock" Class="mr-3 mb-n1"/>
                                @L["Forgot your password?"]
                            </MudText>
                        </TitleContent>
                        <ChildContent>
                            <EditForm class="pa-4" Model="@forgotPasswordViewModel" OnValidSubmit="@ForgotPassword">
                                <FluentValidationValidator/>
                                <MudValidationSummary/>
                                <MudTextField @bind-Value="@forgotPasswordViewModel.Email" Label=@L["Email"] AdornmentIcon="@Icons.Material.Outlined.Mail"
                                              Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>

                                <MudButton ButtonType="ButtonType.Submit" Variant="Variant.Filled" Color="Color.Primary" Class="my-4"
                                           Style="float: right">@L["Submit"]</MudButton>
                            </EditForm>
                        </ChildContent>
                    </MudExpansionPanel>
                </MudExpansionPanels>
            }
            @if (loginViewModel.VisibleExternalProviders.Any())
            {
                <br />
                <MudCard Elevation="2">
                    <MudCardHeader>
                        <CardHeaderContent>
                            @if (!loginViewModel.EnableLocalLogin)
                            {
                                <MudIconButton Icon="@Icons.Material.Filled.Home" Class="ml-auto" Href="/" />
                                <div class="logo">
                                    <a href="/" title="@appState.AppName Home"><img src=@($"{Module.ContentPath}/images/logo.svg") style="width:100px;" title="@appState.AppName Home" alt="@appState.AppName" /><br />@appState.AppName</a>
                                    <br />
                                </div>
                            }
                            <MudText Typo="Typo.h5" Align="Align.Center">@L["Sign in with"]</MudText>
                        </CardHeaderContent>
                    </MudCardHeader>
                    <MudCardContent Class="d-flex align-center flex-column">
                        @foreach (var provider in loginViewModel.VisibleExternalProviders)
                        {
                            @switch (provider.AuthenticationScheme)
                            {
                                case "Google":
                                case "Facebook":
                                    <MudButton Class="signInWithButton" Variant="Variant.Filled" Color="Color.Primary" OnClick="@(() => SignInWith(provider))"><img height="18" src="/images/@(provider.AuthenticationScheme.ToLower()).svg" /></MudButton>
                                    break;
                                case "Twitter":
                                case "Microsoft":
                                case "Apple":
                                    <MudButton Class="signInWithButton" Variant="Variant.Filled" Color="Color.Primary" OnClick="@(() => SignInWith(provider))"><img height="18" src="/images/@(provider.AuthenticationScheme.ToLower()).svg" />&nbsp;@provider.DisplayName</MudButton>
                                    break;
                                default:
                                    <MudButton Class="signInWithButton" Variant="Variant.Filled" Color="Color.Primary" OnClick="@(() => SignInWith(provider))">@provider.DisplayName</MudButton>
                                    break;
                            }
                        }
                    </MudCardContent>
                </MudCard>}
            @if (!loginViewModel.EnableLocalLogin && !loginViewModel.VisibleExternalProviders.Any())
            {
                <MudAlert Severity="Severity.Warning">
                    <strong>Invalid login request</strong>
                    There are no login schemes configured for this client.
                </MudAlert>
            }
        }
    </NotAuthorized>
</AuthorizeView>
@code {

}
