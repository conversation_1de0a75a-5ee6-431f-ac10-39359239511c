update i set 
i.Blocked = itemUpdate.Blocked,
i.[Status] = itemUpdate.[Status],
i.Name = itemUpdate.Name,
i.[PurchaseUnitOfMeasure] = itemUpdate.[Purch_ Unit of Measure] ,
i.[SalesUnitOfMeasure] = itemupdate.[Sales Unit of Measure],
i.UnitCost = itemUpdate.[Unit Cost],
i.VATProductPostingGroup = itemUpdate.[VAT Prod_ Posting Group],
i.LastDateModified = itemUpdate.[Last Date Modified]
from [************].PurchaseManager.dbo.Item i,
(
	select ix.[No_],
	ix.[Blocked],
	ix.[Status],
	ix.Name,
	ix.	[Purch_ Unit of Measure] ,
	ix.	[Sales Unit of Measure] ,
	ix.[Unit Cost] ,
	ix.[VAT Prod_ Posting Group],
	ix.[Last Date Modified]
	from Item ix WHERE 
	convert(date, ix.[Last Date Modified] ) ='2024-07-18'	
)itemUpdate
where i.Number = itemUpdate.[No_]
							
INSERT INTO Item
(Number, Name, Description, BaseUnitOfMeasure, InventoryPostingGroup, ItemCategoryCode, VATProductPostingGroup, ItemDiscountGroup, ManufacturerCode, CountryOfOriginCode, SalaryGroup, TaxGroupCode, CommissionGroup, StatisticsGroup, LotSize, AllowInvoiceDiscount, PriceProfitCalculation, ProfitPercent, CostingMethod, UnitPrice, UnitCost, StandardCost, LeadTimeCalculation, ReorderPoint, MaximumInventory, ReorderQuantity, AlternativeItemNo_, GrossWeight, NetWeight, UnitsPerParcel, UnitVolume, VendorNumber, VendorItemNumber, FreightType, TariffNumber, BudgetQuantity, BudgetedAmount, BudgetProfit, Blocked, LastDateModified, MinimumOrderQuantity, MaximumOrderQuantity, SafetyStockQuantity, OrderMultiple, SafetyLeadTime, SalesUnitOfMeasure, PurchaseUnitOfMeasure, ManufacturingPolicy, ExpirationCalculation, Nonstock, QuotaNumber, QuotaQuantity, QuotaAddedNumber, QuotaAddedQuantity, ImportLicenseNumber, ImportLicenseQuantity, QuotaRequest, Name2, Name3, VisaIssuedDate, ExpirationDate, Standard, VendorAuthorizationNumber, CompanyRegistration, QualityMeasureCode, SearchName, VisaIssuedNumber, PlaceOfOriginCode, LoginId, LastUserModified, BinCode, Status, [Type], MinimumInventory, CategoryNo, CategoryTypeNo, CategoryGroupNo, Importer, Visa, Label, ItemLocation, UnitPriceRegis, MainIngredient, RegistrationNo, Visible, Hidden, PostingDate)

SELECT i.No_ 
FROM Item i
WHERE 
i.No_  like '9%' and  len(i.No_) = 6
and not EXISTS 
(
	select ix.Number from [************].PurchaseManager.dbo.Item ix
	where ix.Number = i.No_ 
)
and i.[Posting Date]  = '2024-07-28'

--unit
SELECT * FROM [Item Unit of Measure] iuom , Item i
WHERE  i.No_  = iuom.[Item No_] 
and i.[Posting Date]  ='2024-07-28'

--price
SELECT * FROM [Sales Price] sp , Item i
WHERE  i.No_  = sp.[Item No_] 
and i.[Posting Date]  ='2024-07-28'

