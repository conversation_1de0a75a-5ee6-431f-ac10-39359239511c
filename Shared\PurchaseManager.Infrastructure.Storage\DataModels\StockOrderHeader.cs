using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Table("StockOrderHeader")]
public class StockOrderHeader : FullTrackingEntity
{
    [Required]
    [MaxLength(100)]
    public string POHeaderNumber { get; set; } = null!;

    [Required]
    public int DocumentType { get; set; }

    [Required]
    public int Status { get; set; } = 1;// 1: Draft, 2: Saved

    public bool IsERPSynced { get; set; }

    [MaxLength(500)]
    public string Note { get; set; } = string.Empty;

    // Navigation properties
    public PurchaseOrderHeader POHeaderNumberNavigation { get; set; }
    public ICollection<StockOrderLine> StockOrderLines { get; set; } = new List<StockOrderLine>();
}
