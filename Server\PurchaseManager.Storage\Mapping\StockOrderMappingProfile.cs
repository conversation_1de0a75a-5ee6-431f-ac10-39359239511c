using AutoMapper;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.StockOrder;
namespace PurchaseManager.Storage.Mapping;

public class StockOrderMappingProfile : Profile
{
    public StockOrderMappingProfile()
    {
        // Legacy mappings
        CreateMap<StockOrder, GetStockOrderDto>().ReverseMap();
        CreateMap<GetStockOrderDto, UpdateStockOrderDto>();
        CreateMap<UpdateStockOrderDto, StockOrder>();
        CreateMap<CreateStockOrderDto, StockOrder>()
            .ForMember(
            destinationMember: dest => dest.CreateAt,
            memberOptions: opt => opt.MapFrom(src => DateTime.UtcNow))
            .ForMember(
            destinationMember: dest => dest.RowId,
            memberOptions: opt => opt.Ignore())
            .ForMember(
            destinationMember: dest => dest.HeaderNumberNavigation,
            memberOptions: opt => opt.Ignore())
            .ForMember(
            destinationMember: dest => dest.PurchaseOrderLine,
            memberOptions: opt => opt.Ignore());

        // New Header/Line mappings
        CreateMap<StockOrderHeader, GetStockOrderHeaderDto>()
            .ForMember(dest => dest.Lines, opt => opt.MapFrom(src => src.StockOrderLines));

        CreateMap<StockOrderLine, GetStockOrderLineDto>();

        CreateMap<CreateStockOrderHeaderDto, StockOrderHeader>()
            .ForMember(dest => dest.Number, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => 1))
            .ForMember(dest => dest.IsERPSynced, opt => opt.MapFrom(src => false))
            .ForMember(dest => dest.POHeaderNumberNavigation, opt => opt.Ignore())
            .ForMember(dest => dest.StockOrderLines, opt => opt.Ignore());

        CreateMap<CreateStockOrderLineDto, StockOrderLine>()
            .ForMember(dest => dest.StockOrderNumber, opt => opt.Ignore())
            .ForMember(dest => dest.LineNumber, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.StockOrderHeader, opt => opt.Ignore());

        CreateMap<SaveStockOrderDto, StockOrderHeader>()
            .ForMember(dest => dest.Number, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.Status, opt => opt.Ignore())
            .ForMember(dest => dest.IsERPSynced, opt => opt.Ignore())
            .ForMember(dest => dest.POHeaderNumberNavigation, opt => opt.Ignore())
            .ForMember(dest => dest.StockOrderLines, opt => opt.Ignore());

        CreateMap<SaveStockOrderLineDto, StockOrderLine>()
            .ForMember(dest => dest.StockOrderNumber, opt => opt.Ignore())
            .ForMember(dest => dest.LineNumber, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedBy, opt => opt.Ignore())
            .ForMember(dest => dest.StockOrderHeader, opt => opt.Ignore());
    }
}
