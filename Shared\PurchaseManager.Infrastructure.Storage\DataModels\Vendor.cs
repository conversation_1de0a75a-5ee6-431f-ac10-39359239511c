﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Constants;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Table("Vendor")]
public class Vendor
{
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }

    [Key] [StringLength(100)] public string Number { get; set; } = null!;

    [Required] [StringLength(160)] public string Name { get; set; } = null!;

    [StringLength(160)] public string SearchName { get; set; } = null!;

    [StringLength(160)] public string Address { get; set; } = null!;

    [StringLength(160)] public string City { get; set; } = null!;

    [StringLength(160)] public string Contact { get; set; } = null!;

    [StringLength(160)] public string Phone { get; set; } = null!;

    [StringLength(100)] public string Telex { get; set; } = null!;

    [StringLength(100)] public string VendorPostingGroup { get; set; } = null!;

    [StringLength(100)] public string CurrencyCode { get; set; } = null!;

    [StringLength(100)] public string PurchaserCode { get; set; } = null!;

    [StringLength(100)] public string ShipmentMethodCode { get; set; } = null!;

    [StringLength(100)] public string ShippingAgentCode { get; set; } = null!;

    [StringLength(100)] public string CountryCode { get; set; } = null!;

    public VendorBlockedType Blocked { get; set; }

    [StringLength(100)] public string PayToVendorNumber { get; set; } = null!;

    [StringLength(100)] public string PaymentMethodCode { get; set; } = null!;

    [Column(TypeName = "datetime")] public DateTime LastDateModified { get; set; }

    [StringLength(160)] public string Fax { get; set; } = null!;

    [Column("VATRegistrationNumber")]
    [StringLength(100)]
    public string VatregistrationNumber { get; set; } = null!;

    [StringLength(100)] public string PostCode { get; set; } = null!;

    [StringLength(160)] public string County { get; set; } = null!;

    [Column("EMail")] [StringLength(160)] public string Email { get; set; } = null!;

    [StringLength(160)] public string HomePage { get; set; } = null!;

    [Column("VATBusinessPostingGroup")]
    [StringLength(100)]
    public string VatbusinessPostingGroup { get; set; } = null!;

    [StringLength(50)] public string? LoginId { get; set; }

    public int? VendorGroup { get; set; }

    [NotMapped]
    public DateOnly? CreateDate { get; set; }

    [StringLength(50)] public string? ImportLicense { get; set; }

    [StringLength(50)] public string? CertificateOfRegistration { get; set; }

    [NotMapped]
    public DateOnly? RegistrationOfExpirationDate { get; set; }

    [StringLength(100)] public string? LicenseNumber { get; set; }

    [Column(TypeName = "datetime")] public DateTime? LicenseDate { get; set; }

    [Column(TypeName = "datetime")] public DateTime? ExprireLicenseDate { get; set; }

    [StringLength(50)] public string? LastUserModified { get; set; }

    public int? Internal { get; set; }

    [StringLength(400)] public string? Description { get; set; }

    public VendorStatusType Status { get; set; }

    [Column(TypeName = "datetime")] public DateTime? LastModifiedTime { get; set; }

    [InverseProperty("VendorNumberNavigation")]
    public ICollection<Contact> Contacts { get; set; } = new List<Contact>();
    public int? PaymentDays { get; set; }

    [InverseProperty("VendorNumberNavigation")]
    public ICollection<VendorItem> VendorItems { get; set; } = new List<VendorItem>();

    [InverseProperty("VendorNumberNavigation")]
    public ICollection<PurchasePrice> PurchasePrices { get; set; } = new List<PurchasePrice>();

}
