﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PurchaseManager.Shared.Helpers
{
    public static class POHandlingShare
    {
        public static string Dtos(DateTime date)
        {
            return date.Year.ToString() + "-" + date.Month.ToString() + "-" + date.Day.ToString();
        }

        public static string lay_ngay(string lay_ngay_thang)
        {
            int vt1 = lay_ngay_thang.IndexOf("/");//vtri / đầu tiền
            if (vt1 > 0)
            {
                string[] mang;
                mang = lay_ngay_thang.Split('/');
                mang[2].ToString();
                mang[2].ToString().Substring(0, 4);
                return mang[2].ToString().Substring(0, 4) + "-" + mang[1] + "-" + mang[0];
            }
            else
            {
                string[] mang;
                mang = lay_ngay_thang.Split('-');
                return mang[2] + "-" + mang[1] + "-" + mang[0];
            }
        }

        public static string lay_ngay_order(string lay_ngay_thang)
        {
            int vt1 = lay_ngay_thang.IndexOf("/");//vtri / đầu tiền
            if (vt1 > 0)
            {
                string[] mang;
                mang = lay_ngay_thang.Split('/');
                return mang[2].ToString().Substring(0, 4) + mang[1] + mang[0];
            }
            else
            {
                string[] mang;
                mang = lay_ngay_thang.Split('-');
                return mang[2] + "-" + mang[1] + "-" + mang[0];
            }
        }
       
        public static string RemoveSpecialChar(string _value)
        {
            if (_value is not null)
            {
                string new_value = _value;
                string specialChar = @"/\|!#$&()=?»«@£§€{};'<>_,*";
                foreach (var item in specialChar)
                {
                    if (_value.ToString().Contains(item))
                    {
                        new_value = _value.Remove(_value.LastIndexOf(item));
                    }
                }
                return new_value;
            }
            return "";
        }

        public static string RemoveSpecialCharLotNo(string _value)
        {
            if (_value is not null)
            {
                string new_value = _value;
                string specialChar = @"\|!#$%&()=?»«@£§€{};'<>_,* ";
                foreach (var item in specialChar)
                {
                    if (_value.ToString().Contains(item))
                    {
                        new_value = _value.Remove(_value.LastIndexOf(item));
                    }
                }
                return new_value;
            }
            return "";
        }

    }
}
