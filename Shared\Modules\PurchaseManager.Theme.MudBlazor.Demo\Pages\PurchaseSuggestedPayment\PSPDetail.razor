@page "/po/purchase-suggest-payment/{PSPNumber}/detail"
@inherits PurchaseManager.Theme.Material.Demo.Pages.PurchaseSuggestedPayment.PSPDetailBase
@attribute [Authorize]
<PageTitle>Purchase Suggested Payment Detail</PageTitle>
@if (isLoading)
{
    <MudProgressLinear Indeterminate="@isLoading" />
}
@if (pSPHeader is not null)
{
    <div class="mb-2">
        <MudText Typo="Typo.h4" Align="Align.Center" Inline="false">
            @L["Purchase Suggested Payment Detail"]
        </MudText>
    </div>
    <MudToolBar Gutters="false" Dense="true">

        <MudIconButton Color="Color.Default" Icon="@Icons.Material.Filled.ArrowBack"
            OnClick="@(() => { navigationManager.NavigateTo("/po/purchase-suggest-payment"); })" Variant="Variant.Text">
        </MudIconButton>
        <MudStack Row="true" AlignItems="AlignItems.Center" Justify="Justify.Center">
            <MudText Typo="Typo.h5">@pSPHeader.Number -
                <MudChip T="Wrap" Text="@pSPHeader.SourceCode" Color="Color.Primary" Variant="Variant.Text" />
            </MudText>
        </MudStack>
        @if (!isBusy)
        {
            LoadTagsFilter();
        }
        
        <MudSpacer />
        <MudSpacer />
        <MudSpacer />
        <MudSpacer />
        <MudSpacer />
        <MudSpacer />
        <MudSpacer />
        <MudSpacer />
        <MudSpacer />
        <MudSelect Dense Margin="Margin.Dense" Style="width: 150px;" Class="mr-3" T="string" Label="Tags"
            Clearable 
            FullWidth="false"
            ShrinkLabel="false"
            Variant="Variant.Outlined"
            MultiSelection="true" 
            Value="@selectedTagsString" 
            TextChanged="@(async value => await OnFilterByTagsChanged(value))">
            @foreach (var state in tagsToSelect)
            {
                <MudSelectItem T="string" Value="@state">@state</MudSelectItem>
            }
        </MudSelect>
        @if (pSPHeader.Status !=
       (int)PurchaseManager.Constants.PurchaseSuggestedPayment.PurchaseSuggestedPaymentEnum.Approve)
        {
            <MudButton Size="Size.Small" Class="mr-6" Variant="Variant.Filled"
                OnClick="@(async _ =>  await ToggleHeaderStatus())" Color="Color.Primary">
                @if (isEditing == true)
                {
                    @L["Save"]
                }
                else
                {
                    @("Open")
                }
            </MudButton>
            <AuthorizeView Policy="@Policies.IsPurchaseManager">
                <Authorized>
                    <MudButton Size="Size.Small" Class="mr-6" OnClick="@Approve" Variant="Variant.Filled" Color="Color.Success">
                        Approve
                    </MudButton>
                </Authorized>
            </AuthorizeView>
        }
    </MudToolBar>
    @if (!isProcessing)
    {
        <MudTable @ref="table" Striped="false" Bordered="false" Outlined="false" Dense="true" Hover="true" Class="mt-3"
            ServerData="@(new Func<TableState, CancellationToken, Task<TableData<PurchaseManager.Shared.Dto.PurchaseSuggestedPayment.DetailPurchaseSuggestedPaymentLineDto>>>(ServerReload))"
            T="PurchaseManager.Shared.Dto.PurchaseSuggestedPayment.DetailPurchaseSuggestedPaymentLineDto" CanCancelEdit="true"
            EditTrigger="TableEditTrigger.EditButton" Height="500px" ShowMenuIcon="true" FixedHeader="true" FixedFooter="true"
            IsEditRowSwitchingBlocked="true" CustomFooter="true" ReadOnly="@(!isEditing)" RowEditPreview="BackupItem"
            RowEditCommit="@(async _ => await ItemHasBeenCommitted(_))" RowEditCancel="ResetItemToOriginalValues"
            EditButtonPosition="TableEditButtonPosition.Start" ApplyButtonPosition="TableApplyButtonPosition.Start">
            <HeaderContent>
                <MudTh>Item Number</MudTh>
                <MudTh>Item Name</MudTh>
                <MudTh>Unit</MudTh>
                @if (isPurchaser)
                {
                    <MudTh>Request Quantity</MudTh>
                }
                <MudTh>Qty</MudTh>
                <MudTh>Rate(%)</MudTh>
                @if (isPurchaseManager)
                {
                    <MudTh>Confirm Quantity</MudTh>
                    <MudTh>Reason</MudTh>
                }
                <MudTh>SKU (VAT)</MudTh>
                <MudTh>Tags</MudTh>
            </HeaderContent>
            <RowTemplate Context="OrderRow">
                <MudTd>@OrderRow.Number</MudTd>
                <MudTd>@OrderRow.ItemName</MudTd>
                <MudTd>@OrderRow.UnitOfMeasure</MudTd>
                <MudTd>
                    <MudText>@OrderRow.RequestQuantity</MudText>
                </MudTd>
                <MudTd>
                    <MudText>@OrderRow.Quantity</MudText>
                </MudTd>
                <MudTd>
                    @if (string.IsNullOrEmpty(OrderRow.Rate))
                    {
                        <MudText>0</MudText>
                    }
                    else
                    {
                        <MudText>@OrderRow.Rate</MudText>
                    }
                </MudTd>
                @if (isPurchaseManager)
                {
                    <MudTd>
                        <MudText>@OrderRow.ConfirmQuantity</MudText>
                    </MudTd>
                    <MudTd>@OrderRow.ReasonName</MudTd>
                }
                <MudTd>@OrderRow.Vat</MudTd>
                <MudTd DataLabel="QtyDemand">@foreach (var tag in @OrderRow.Tags.Split(",").ToList())
                    {
                        <MudChip Color="Color.Primary" Variant="Variant.Text" Text="@tag" T="string" />
                    }
                </MudTd>
            </RowTemplate>
            <RowEditingTemplate Context="OrderRowEdit">
                <MudTd DataLabel="Number" Style="width: 160px;">
                    @OrderRowEdit.Number
                </MudTd>
                <MudTd DataLabel="ItemName" Style="width: 160px;">
                    @OrderRowEdit.ItemName
                </MudTd>
                <MudTd DataLabel="Unit">
                    @OrderRowEdit.UnitOfMeasure
                </MudTd>
                @if (isPurchaser)
                {
                    <MudTd DataLabel="Quantity">
                        <MudNumericField @ref="requestQuantityRef" Min="0" T="int" @bind-Value="@OrderRowEdit.RequestQuantity" ReadOnly="isPurchaseManager"
                            HideSpinButtons Margin="Margin.Dense" Format="N0"
                            TextChanged="@(_ => UpdateRate(OrderRowEdit.Number, OrderRowEdit.RequestQuantity, OrderRowEdit.Quantity))"
                            Required="true" />
                    </MudTd>
                }
                <MudTd>
                    <MudText>@OrderRowEdit.Quantity</MudText>
                </MudTd>
                <MudTd>
                    <MudText>@OrderRowEdit.Rate</MudText>
                </MudTd>
                @if (isPurchaseManager)
                {
                    <MudTd DataLabel="ConfirmQuantity">
                        <MudNumericField Min="0" T="int" @bind-Value="@OrderRowEdit.ConfirmQuantity" HideSpinButtons
                            Margin="Margin.Dense" Format="N0" Required="true" />
                    </MudTd>
                    <MudTd DataLabel="Reason">
                        <MudSelect T="string" @bind-Value="@OrderRowEdit.ReasonCode" Required="@(OrderRowEdit.ConfirmQuantity > OrderRowEdit.Quantity)" Label="Reason"
                            AnchorOrigin="Origin.BottomCenter">
                            @foreach (var o in listDetailDemandReasonDto)
                            {
                                <MudSelectItem Value="@o.ReasonCode">@o.ReasonName</MudSelectItem>
                            }
                        </MudSelect>
                    </MudTd>
                }
                <MudTd DataLabel="VAT">
                    @OrderRowEdit.Vat
                </MudTd>
                <MudTd DataLabel="QtyDemand">@foreach (var tag in @OrderRowEdit.Tags.Split(",").ToList())
                    {
                        <MudChip Color="Color.Primary" Variant="Variant.Text" Text="@tag" T="string" />
                    }
                </MudTd>
            </RowEditingTemplate>
            <NoRecordsContent>
                No Data
            </NoRecordsContent>
        </MudTable>
        <MudDialog @bind-Visible="@isWarningQuantityDialogOpen">
            <TitleContent>
                <MudText Typo="Typo.h6">
                    <MudIcon Color="Color.Warning" Icon="@Icons.Material.Filled.Warning" Class="mr-3 mb-n1" />
                    WARNING
                </MudText>
            </TitleContent>
            <DialogContent>
                <MudAlert Severity="Severity.Warning">@L["Your input quantity is greater than default quantity. Do you want to continue?"]</MudAlert>
            </DialogContent>
            <DialogActions>
                <MudButton OnClick="@(e => { isWarningQuantityDialogOpen = false; })">@L["Cancel"]</MudButton>    
                <MudButton OnClick="@(async _ => { isWarningQuantityDialogOpen = false; await requestQuantityRef.FocusAsync(); })" Variant="Variant.Filled"
                    Color="Color.Primary">@L["Continue"]
                </MudButton>
            </DialogActions>
        </MudDialog>
    }
    else
    {
        <MudText Align="Align.Center">Loading...</MudText>
    }
}
