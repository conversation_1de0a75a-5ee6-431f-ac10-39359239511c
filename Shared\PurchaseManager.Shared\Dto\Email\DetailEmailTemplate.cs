﻿namespace PurchaseManager.Shared.Dto.Email;

public sealed class DetailEmailTemplate
{
    public int EmailTemplatesId { get; set; }
    public string TemplateName { get; set; } = null!;
    public string Subject { get; set; } = null!;
    public string Body { get; set; } = null!;
    public ICollection<DetailEmailAfterDetailEmailTemplateDto> Emails { get; set; }
}
public class DetailEmailTemplateAfterDetailEmailDto
{
    public string TemplateName { get; set; } = null!;
    public string Subject { get; set; } = null!;
}
