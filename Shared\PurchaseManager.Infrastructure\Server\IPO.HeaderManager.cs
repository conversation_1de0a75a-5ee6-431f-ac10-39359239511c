﻿using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Models.PO;
namespace PurchaseManager.Infrastructure.Server;

public interface IPOHeaderManager
{
    /// <summary>
    /// Kiểm tra header number
    /// </summary>
    /// <param name="code"></param>
    /// <param name="action"></param>
    /// <returns></returns>
    Task<ApiResponse> HeaderNumberValidAsync(string code, string action);
    /// <summary>
    /// Kiểm tra trạng thái vendor trước khi tạo PO
    /// </summary>
    /// <param name="code"></param>
    /// <returns></returns>
    Task<ApiResponse> ValidateVendorBeforePOCreationAsync(string code);
    Task<ApiResponse> CreateHeaderAsync(CreatePOHeaderDto createPOHeader);
    Task<ApiResponse> GetHeaderAsync(string documentNumber);
    Task<ApiResponse> GetPOHeadersByFilterAsync(PurchaseOrderFilter filter);
    Task<ApiResponse> UpdateHeaderAsync(UpdatePOHeaderDto updatePOHeader);
    Task<ApiResponse> UserIdValidAsync(string code);
    /// <summary>
    /// Kiểm tra trạng thái Header
    /// </summary>
    /// <param name="documentNumber"></param>
    /// <returns></returns>
    Task<ApiResponse> IsDocumentLockedByAnotherUserAsync(string documentNumber);
    /// <summary>
    /// Kiểm tra trạng thái Line
    /// </summary>
    /// <param name="code"></param>
    /// <returns></returns>
    Task<ApiResponse> IsLineLockedByAnotherUserAsync(string code);
    /// <summary>
    /// Mở chứng từ
    /// </summary>
    /// <param name="documentNumber"></param>
    /// <param name="isStockOrder"></param>
    /// <returns></returns>
    Task<ApiResponse> OpenDocumentAsync(string documentNumber, bool isStockOrder);
    /// <summary>
    /// Đóng chứng từ
    /// </summary>
    /// <param name="documentNumber"></param>
    /// <returns></returns>
    Task<ApiResponse> CloseDocumentAsync(string documentNumber);
    /// <summary>
    /// Xác nhận chứng từ => Status = 2
    /// </summary>
    /// <param name="documentNumber"></param>
    /// <param name="status"></param>
    /// <returns></returns>
    Task<ApiResponse> ChangeDocumentStatusAsync(string documentNumber, PurchaseOrderEnum status);
    /// <summary>
    /// Xóa mềm List Header 
    /// </summary>
    /// <param name="documentNumber"></param>
    /// <returns></returns>
    Task<ApiResponse> DeleteMultipleHeaderAsync(List<string> documentNumber);
    /// <summary>
    /// Kiểm tra xem Vendor này có được Order gần đây không
    /// </summary>
    /// <param name="listVendorNumber"></param>
    /// <returns></returns>
    Task<ApiResponse> IsRecentOrderFromVendorAsync(List<string> listVendorNumber);
    /// <summary>
    /// Từ List Item Number Query lấy VendorNumber => check xem có vendor nào được Order gần đây không
    /// </summary>
    /// <param name="listItemNumber"></param>
    /// <returns></returns>
    Task<ApiResponse> IsRecentOrderFromItemAsync(List<string> listItemNumber);
    /// <summary>
    /// Từ List Purchase Suggested Payment Lines Query lấy VendorNumber => check xem có vendor nào được Order gần đây không
    /// </summary>
    /// <param name="listSuggestDocumentNumber"></param>
    /// <returns></returns>
    Task<ApiResponse> IsRecentOrderFromSuggestDocumentAsync(List<string> listSuggestDocumentNumber);
    /// <summary>
    /// Lấy giá mua gần nhất của những đơn PO cũ
    /// </summary>
    /// <param name="itemCode"></param>
    /// <param name="unit"></param>
    /// <param name="vendorCode"></param>
    /// <returns></returns>
    Task<ApiResponse<decimal>> GetLastPurchasePriceAsync(string itemCode, string unit, string vendorCode);
    Task<ApiResponse<GetLatestPriceDto>> GetLatestPriceBySku(string itemCode, string unit, string vendorCode);
    Task<ApiResponse> GetUserCreatedPOByPONumberAsync(string poNumber);
    /// <summary>
    /// Auto create PO
    /// </summary>
    /// <param name="loginId"></param>
    /// <returns></returns>
    Task<ApiResponse> PoAutoCreateAsync(string loginId);
    Task<byte[]> ViewPDF(string code);
    Task<ApiResponse> UploadFile(POFileData objFile);
}
