﻿using AutoMapper;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.Email;
namespace PurchaseManager.Storage.Mapping;

public class EmailMappingProfile : Profile 
{
    public EmailMappingProfile()
    {
        CreateMap<EmailTemplates, DetailEmailTemplateAfterDetailEmailDto>().ReverseMap();
        CreateMap<EmailTemplates, DetailEmailTemplate>().ReverseMap();
        CreateMap<QueuedEmail, DetailEmailDto>();
        CreateMap<EmailTemplates, CreateEmailTemplateDto>().ReverseMap();
        CreateMap<EmailTemplates, UpdateEmailTemplateDto>().ReverseMap();
        CreateMap<SentEmailDto, SentEmail>().ReverseMap();
    }
}
