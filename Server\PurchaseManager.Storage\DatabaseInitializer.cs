﻿using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.AuthorizationDefinitions;
using PurchaseManager.Infrastructure.Storage;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Infrastructure.Storage.Permissions;
using PurchaseManager.Shared.Localizer;
using Finbuckle.MultiTenant;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Security.Claims;
using ApiLogItem = PurchaseManager.Infrastructure.Storage.DataModels.ApiLogItem;
using UserProfile = PurchaseManager.Infrastructure.Storage.DataModels.UserProfile;

namespace PurchaseManager.Storage
{
    public class DatabaseInitializer : IDatabaseInitializer
    {
        private readonly LocalizationDbContext _localizationDbContext;
        private readonly ApplicationDbContext _context;
        private readonly TenantStoreDbContext _tenantStoreDbContext;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<ApplicationRole> _roleManager;
        private readonly EntityPermissions _entityPermissions;
        private readonly ILocalizationProvider _localizationProvider;
        private readonly ILogger _logger;

        public DatabaseInitializer(
            TenantStoreDbContext tenantStoreDbContext,
            LocalizationDbContext localizationDbContext,
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<ApplicationRole> roleManager,
            EntityPermissions entityPermissions,
            ILocalizationProvider localizationProvider,
            ILogger<DatabaseInitializer> logger)
        {
            _tenantStoreDbContext = tenantStoreDbContext;
            _localizationDbContext = localizationDbContext;
            _context = context;
            _userManager = userManager;
            _roleManager = roleManager;
            _entityPermissions = entityPermissions;
            _localizationProvider = localizationProvider;
            _logger = logger;
        }

        public virtual async Task SeedAsync()
        {
            //Apply EF Core migration
            await MigrateAsync();

            await ImportTranslations();

            await EnsureAdminIdentitiesAsync();

            //Seed sample data
            await SeedDemoDataAsync();

            _context.Database.ExecuteSqlRaw("SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;");
        }

        private async Task MigrateAsync()
        {
            await _tenantStoreDbContext.Database.MigrateAsync();
            await _localizationDbContext.Database.MigrateAsync();
            await _context.Database.MigrateAsync();
        }

        private async Task ImportTranslations()
        {
            try
            {
                if (!await _localizationDbContext.LocalizationRecords.AnyAsync())
                    await ((StorageLocalizationProvider)_localizationProvider).InitDbFromPoFiles(_localizationDbContext);
            }
            catch (Exception ex)
            {
                _logger.LogError("Importing PO files in db error: {0}", ex.GetBaseException().Message);
            }
        }

        private async Task SeedDemoDataAsync()
        {
            if ((await _userManager.FindByNameAsync(DefaultUserNames.User)) == null)
            {
                await CreateUserAsync(DefaultUserNames.User, "user123", "User", "Blazor", "<EMAIL>", "+1 (123) 456-7890");
            }

            if (_tenantStoreDbContext.TenantInfo.Count() < 2)
            {
                _tenantStoreDbContext.TenantInfo.Add(new TenantInfo() { Id = "tenant1", Identifier = "tenant1.local", Name = "Microsoft Inc." });
                _tenantStoreDbContext.TenantInfo.Add(new TenantInfo() { Id = "tenant2", Identifier = "tenant2.local", Name = "Contoso Corp." });

                await _tenantStoreDbContext.SaveChangesAsync();
            }

            ApplicationUser user = await _userManager.FindByNameAsync(DefaultUserNames.User);

            if (!_context.UserProfiles.Any())
                if (user != null)
                {
                    _context.UserProfiles.Add(new UserProfile
                    {
                        UserId = user.Id,
                        ApplicationUser = user,
                        Count = 2,
                        IsNavOpen = true,
                        LastPageVisited = "/dashboard",
                        IsNavMinified = false,
                        LastUpdatedDate = DateTime.Now
                    });
                }


            if (!_context.ApiLogs.Any())
            {
                if (user != null)
                {
                    _context.ApiLogs.AddRange(
                    new ApiLogItem
                    {
                        RequestTime = DateTime.Now,
                        ResponseMillis = 30,
                        StatusCode = 200,
                        Method = "Get",
                        Path = "/api/seed",
                        QueryString = "",
                        RequestBody = "",
                        ResponseBody = "",
                        IPAddress = "::1",
                        ApplicationUserId = user.Id
                    },
                    new ApiLogItem
                    {
                        RequestTime = DateTime.Now,
                        ResponseMillis = 30,
                        StatusCode = 200,
                        Method = "Get",
                        Path = "/api/seed",
                        QueryString = "",
                        RequestBody = "",
                        ResponseBody = "",
                        IPAddress = "::1",
                        ApplicationUserId = user.Id
                    }
                    );
                }
            }

            await _context.SaveChangesAsync();
        }

        public async Task EnsureAdminIdentitiesAsync()
        {
            await EnsureRoleAsync(DefaultRoleNames.Administrator, _entityPermissions.GetAllPermissionValues());
            await EnsureRoleAsync(DefaultRoleNames.PurchaseManager, null);
            await EnsureRoleAsync(DefaultRoleNames.PurchaseUser, null);
            await EnsureRoleAsync(DefaultRoleNames.WarehouseUser, null);
            await EnsureRoleAsync(DefaultRoleNames.WarehouseManager, null);
            await EnsureRoleAsync(DefaultRoleNames.Vendor, null);
            await EnsureRoleAsync(DefaultRoleNames.VendorContact, null);
            await EnsureRoleAsync(DefaultRoleNames.MKT, null);

            await CreateUserAsync(DefaultUserNames.Administrator, "admin123", "Admin", "Blazor", "<EMAIL>",
            "+1 (123) 456-7890",
            [DefaultRoleNames.Administrator]);

            ApplicationRole adminRole = await _roleManager.FindByNameAsync(DefaultRoleNames.Administrator);
            var allClaims = _entityPermissions.GetAllPermissionValues().Distinct();
            if (adminRole != null)
            {
                var roleClaims = (await _roleManager.GetClaimsAsync(adminRole)).Select(c => c.Value).ToList();
                var claims = allClaims.ToList();
                var newClaims = claims.Except(roleClaims);

                foreach (var claim in newClaims)
                {
                    await _roleManager.AddClaimAsync(adminRole, new Claim(ApplicationClaimTypes.Permission, claim));
                }

                var deprecatedClaims = roleClaims.Except(claims);
                var roles = await _roleManager.Roles.ToListAsync();

                foreach (var claim in deprecatedClaims)
                {
                    foreach (var role in roles)
                    {
                        await _roleManager.RemoveClaimAsync(role, new Claim(ApplicationClaimTypes.Permission, claim));
                    }
                }
            }

            _logger.LogInformation("Inbuilt account generation completed");
        }

        private async Task EnsureRoleAsync(string roleName, string[] claims)
        {
            if ((await _roleManager.FindByNameAsync(roleName)) == null)
            {
                claims ??= [];

                string[] invalidClaims = claims.Where(c => _entityPermissions.GetPermissionByValue(c) == null).ToArray();
                if (invalidClaims.Length != 0)
                    throw new Exception("The following claim types are invalid: " + string.Join(", ", invalidClaims));

                var applicationRole = new ApplicationRole(roleName);

                var result = await _roleManager.CreateAsync(applicationRole);

                if (applicationRole.Name != null)
                {
                    var role = await _roleManager.FindByNameAsync(applicationRole.Name);

                    foreach (var claim in claims.Distinct())
                    {
                        if (role == null)
                        {
                            continue;
                        }
                        result = await _roleManager.AddClaimAsync(role,
                        new Claim(ApplicationClaimTypes.Permission, _entityPermissions.GetPermissionByValue(claim)));

                        if (!result.Succeeded)
                        {
                            await _roleManager.DeleteAsync(role);
                        }
                    }
                }
            }
        }

        private async Task CreateUserAsync(string userName, string password, string firstName, string lastName, string email,
            string phoneNumber, string[] roles = null)
        {
            var applicationUser = _userManager.FindByNameAsync(userName).Result;

            if (applicationUser == null)
            {
                applicationUser = new ApplicationUser
                {
                    UserName = userName,
                    Email = email,
                    PhoneNumber = phoneNumber,
                    FirstName = firstName,
                    LastName = lastName,
                    EmailConfirmed = true
                };

                var result = _userManager.CreateAsync(applicationUser, password).Result;

                if (!result.Succeeded)
                    throw new Exception(result.Errors.First().Description);

                result = _userManager.AddClaimsAsync(applicationUser, [
                    new Claim(ClaimTypes.Name, userName),
                        new Claim(ClaimTypes.GivenName, firstName),
                        new Claim(ClaimTypes.Surname, lastName),
                        new Claim(ClaimTypes.Email, email),
                    new Claim(ApplicationClaimTypes.EmailVerified, ClaimValues.TrueString, ClaimValueTypes.Boolean),
                        new Claim(ClaimTypes.HomePhone, phoneNumber)
                ]).Result;

                if (!result.Succeeded)
                    throw new Exception(result.Errors.First().Description);

                //add claims version of roles
                if (roles != null)
                {
                    foreach (var role in roles.Distinct())
                    {
                        await _userManager.AddClaimAsync(applicationUser, new Claim($"Is{role}", ClaimValues.TrueString));
                    }

                    ApplicationUser user = await _userManager.FindByNameAsync(applicationUser.UserName);

                    try
                    {
                        if (user != null)
                        {
                            result = await _userManager.AddToRolesAsync(user, roles.Distinct());
                        }
                    }
                    catch
                    {
                        if (user != null)
                        {
                            await _userManager.DeleteAsync(user);
                        }
                        throw;
                    }

                    if (!result.Succeeded)
                    {
                        if (user != null)
                        {
                            await _userManager.DeleteAsync(user);
                        }
                    }
                }
            }

        }
    }
}
