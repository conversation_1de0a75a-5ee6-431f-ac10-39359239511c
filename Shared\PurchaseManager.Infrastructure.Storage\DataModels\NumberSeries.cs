﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

public partial class NumberSeries
{
    [Key]
    [StringLength(100)]
    public string Code { get; set; } = null!;

    [StringLength(200)]
    public string Description { get; set; } = null!;

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }

    [InverseProperty("NumberSeriesCodeNavigation")]
    public virtual ICollection<NumberSeriesLine> NumberSeriesLines { get; set; } = new List<NumberSeriesLine>();
}
