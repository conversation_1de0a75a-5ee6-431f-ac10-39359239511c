﻿@page "/schedule"
@attribute [Authorize]

<MudText Align="Align.Center" Class="mb-4" Typo="Typo.h4">@L["Schedule"]</MudText>


@if (IsLoading)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    <Heron.MudCalendar.MudCalendar DateRangeChanged="DateRangeChanged" ShowDay="false" ShowWeek="false" Items="_events">
        <MonthTemplate>
            <div class="d-flex gap-1">
                <MudIcon Icon="@Icons.Material.Filled.Circle" Color="Color.Secondary" Size="Size.Small" />
                @if (IsPurchaseUser)
                {
                    <MudLink Href="@("PO/" + context.Text + "/detail")">@context.Text</MudLink>
                }
                else
                {
                    <MudLink Href="@("stock/" + context.Text + "/detail")">@context.Text</MudLink>
                }
            </div>
        </MonthTemplate>
    </Heron.MudCalendar.MudCalendar>
}