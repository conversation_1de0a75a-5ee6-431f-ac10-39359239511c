﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Index("Code", Name = "Employees_UNIQUE", IsUnique = true)]
[Table("Employees")]
public class Employee
{
    [Key]
    public long Id { get; set; }

    [StringLength(400)]
    public string Avatar { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string Code { get; set; }

    [StringLength(10)]
    public string FirstName { get; set; } = string.Empty;

    [StringLength(10)]
    public string LastName { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string Mail { get; set; } = string.Empty;

    [Required]
    [StringLength(20)]
    public string PhoneNumber { get; set; } = string.Empty;

    public DateTime Birthday { get; set; } = DateTime.Now;

    public bool Gender { get; set; }

    public DateTime HireDate { get; set; } = DateTime.Now;

    public DateTime? QuitDate { get; set; }

    [StringLength(20)]
    public string EmergencyPhoneNumber { get; set; } = string.Empty;

    [StringLength(2)]
    public string Marriage { get; set; } = string.Empty;

    [Column("IDnumber")]
    [StringLength(20)]
    public string Idnumber { get; set; } = string.Empty;

    [Column("IDdate")]
    public DateTime Iddate { get; set; } = DateTime.Now;

    [Column("IDplace")]
    [StringLength(100)]
    public string Idplace { get; set; } = string.Empty;

    [StringLength(10)]
    public string CurrentWorkTypeCode { get; set; } = string.Empty;

    public string Address { get; set; } = string.Empty;

    [StringLength(15)]
    public string BfoAccount { get; set; } = string.Empty;

    public int DayOffInMonth { get; set; }

    [StringLength(200)]
    public string Note { get; set; } = string.Empty;

    [StringLength(300)]
    public string QuitNote { get; set; } = string.Empty;

    /// <summary>
    ///     1:online, 2: tam nghi, 3 thaisan, 4:off
    /// </summary>
    [StringLength(5)]
    public string Status { get; set; } = string.Empty;

    [StringLength(5)]
    public string OnlineStatus { get; set; } = string.Empty;

    [StringLength(5)]
    public string AttendanceStatus { get; set; } = string.Empty;

    public bool IsVerified { get; set; }

    public bool IsPregnant { get; set; }

    public bool IsQuit { get; set; }

    public bool IsSuspend { get; set; }

    public bool IsDraft { get; set; }

    public bool HasReturnProperty { get; set; }

    [StringLength(100)]
    public string AttachmentLink { get; set; } = string.Empty;

    public DateTime CreatedAt { get; set; }

    public DateTime UpdatedAt { get; set; }

    [StringLength(10)]
    public string CreatedBy { get; set; } = string.Empty;

    [StringLength(10)]
    public string UpdatedBy { get; set; } = string.Empty;

    public float WorkDay { get; set; }

    public DateTime OfficialContractDate { get; set; }

    public int? Seniority { get; set; }

    public int? SeniorityOrigin { get; set; }

    [StringLength(255)]
    public string DomicileAddress { get; set; } = string.Empty;

    public bool? NotAccept { get; set; }

    [Precision(0)]
    public DateTime? SuspendDate { get; set; }

    [StringLength(10)]
    public string CurrentTeamCode { get; set; }

    public bool? InReport { get; set; }

    public int? AllowedStatistic { get; set; }
}
