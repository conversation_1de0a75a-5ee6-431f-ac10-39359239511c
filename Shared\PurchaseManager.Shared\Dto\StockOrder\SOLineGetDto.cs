using System.ComponentModel.DataAnnotations.Schema;

namespace PurchaseManager.Shared.Dto.StockOrder;

public class SOLineGetDto
{
    public string DocumentNumber { get; set; } = string.Empty;
    public int LineNumber { get; set; }
    public int DocumentType { get; set; }
    public string ItemNumber { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public string UnitOfMeasure { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public decimal QuantityReceived { get; set; }
    public decimal QuantityToReceive { get; set; }
    public decimal QuantityInvoiced { get; set; }
    public string LotNo { get; set; } = string.Empty;
    public DateTime? ExpirationDate { get; set; }

    [NotMapped]
    public string ConversionRateDisplay { get; set; } = string.Empty;

    [NotMapped]
    public int TempReceive { get; set; }
}
