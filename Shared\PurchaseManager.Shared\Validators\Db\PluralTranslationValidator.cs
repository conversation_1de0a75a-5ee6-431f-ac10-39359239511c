﻿using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Localizer;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace PurchaseManager.Shared.Validators.Db
{
    public class PluralTranslationValidator : LocalizedAbstractValidator<PluralTranslation>
    {
        public PluralTranslationValidator(IStringLocalizer<Global> l) : base(l)
        {
            RuleFor(p => p.Translation)
                .NotEmpty().WithName(L["Translation"]);
        }
    }
}
