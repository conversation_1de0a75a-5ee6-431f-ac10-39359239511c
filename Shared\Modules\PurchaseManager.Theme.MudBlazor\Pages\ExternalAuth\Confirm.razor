﻿@page "/externalauth/confirm"

@inject AppState appState
@inject NavigationManager navigationManager
@inject HttpClient Http
@inject AuthenticationStateProvider authStateProvider
@inject IStringLocalizer<Global> L
@inject IViewNotifier viewNotifier
<MudCard>
    <div class="logo" style="padding: 50px;">
        <img src=@($"{Module.ContentPath}/images/logo.svg") style="width:100px;" /><br />PurchaseManager
        <br />
        <h4>External authentication success - check your email inbox for confirmation email and login again</h4>
        <MudButton Href="/">Home</MudButton>
    </div>
</MudCard>

@code {

}
