﻿@page "/admin/settings/numberseries"
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using PurchaseManager.Shared.Models
@layout AdminLayout

@inject IApiClient apiClient
@inject IViewNotifier viewNotifier
@inject IStringLocalizer<Global> L

<div style="margin: 20px; font-weight: bold; font-size: 18px">NumberSeries</div>

<MudGrid Style="font-weight:bold;background-color:#ddd;margin:0" Class="align-center">
    <MudItem xs="2">
        <MudButton StartIcon="@Icons.Material.Filled.PlaylistAdd" OnClick="@((e) => OpenDialog())" Variant="Variant.Filled" Color="Color.Primary">New</MudButton>
    </MudItem>
    <MudItem xs="2">Code</MudItem>
    <MudItem xs="2">Description</MudItem>
</MudGrid>
<div style="height:360px;overflow-y:auto;overflow-x:hidden;width:calc(100% + 24px);">
    <Virtualize @ref="asyncVirtualize" Context="item" ItemsProvider="LoadNumberSeries" ItemSize="72" OverscanCount="20">
        <ItemContent>
            <MudGrid Class="align-center" @key="item.Code" Style="border-bottom: 1px solid #eee;">
                <MudItem xs="2"><MudIconButton Icon="@Icons.Material.Filled.Delete" OnClick="@(() => OpenDeleteDialog(item))"></MudIconButton></MudItem>
                <MudItem xs="2">@item.Code</MudItem>
                <MudItem xs="2">@item.Description</MudItem>
            </MudGrid>
        </ItemContent>
        <Placeholder>
            <div style="border-bottom: 1px solid #eee;">
                <MudProgressCircular Color="Color.Default" Indeterminate="true" />
            </div>
        </Placeholder>
    </Virtualize>
</div>

@if (currentNumberSeries != null)
{
    <MudDialog @bind-Visible="@dialogIsOpen">
        <TitleContent>
            <MudText Typo="Typo.h6">
                <MudIcon Icon="@Icons.Material.Filled.Add" Class="mr-3 mb-n1" />
                Create
            </MudText>
        </TitleContent>
        <DialogContent>
            <EditForm id="newNumberSeries" Model="@currentNumberSeries" OnValidSubmit="@NewEntity">
                <FluentValidationValidator />
                <MudValidationSummary />
                <MudTextField @bind-Value="@currentNumberSeries.Code" Label="Code" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
                <MudTextField @bind-Value="@currentNumberSeries.Description" Label="Description" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
            </EditForm>
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="@(e => { dialogIsOpen = false; })">@L["Cancel"]</MudButton>
            <MudButton ButtonType="ButtonType.Submit" form="newNumberSeries" Variant="Variant.Filled" Color="Color.Primary">Create</MudButton>
        </DialogActions>
    </MudDialog>

    <MudDialog @bind-Visible="@deleteDialogOpen" Style="z-index:100">
        <TitleContent>
            <MudText Typo="Typo.h6">
                <MudIcon Icon="@Icons.Material.Filled.DeleteForever" Class="mr-3 mb-n1" />
                @L["Confirm Delete"]
            </MudText>
        </TitleContent>
        <DialogContent>
            @L["Are you sure you want to delete {0}?", currentNumberSeries.Description]
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="@(e => { deleteDialogOpen = false; })">@L["Cancel"]</MudButton>
            <MudButton OnClick="@Delete" Variant="Variant.Filled" Color="Color.Error">@L["Delete"]</MudButton>
        </DialogActions>
    </MudDialog>
}

@code {
    private Virtualize<NumberSeries> asyncVirtualize;
    private NumberSeries currentNumberSeries = new();

    private int startIndex;
    private int pageSize;
    private int itemCount;
    private bool deleteDialogOpen = false;
    private bool dialogIsOpen = false;

    private async ValueTask<ItemsProviderResult<NumberSeries>> LoadNumberSeries(ItemsProviderRequest request)
    {
        startIndex = request.StartIndex;
        pageSize = request.Count;

        var items = await apiClient.GetNumberSeries(request.Count, request.StartIndex);

        itemCount = (int)items.InlineCount;

        if (request.StartIndex == 0)
            StateHasChanged();

        StateHasChanged();
        return new ItemsProviderResult<NumberSeries>(items, itemCount);
    }

    public void OpenDialog()
    {
        currentNumberSeries = new NumberSeries();
        dialogIsOpen = true;
    }

    public void OpenDeleteDialog(NumberSeries number)
    {
        currentNumberSeries = number;
        deleteDialogOpen = true;
    }

    public async Task NewEntity()
    {
        dialogIsOpen = false;

        try
        {
            apiClient.AddEntity(currentNumberSeries);

            await apiClient.SaveChanges();

            apiClient.ClearEntitiesCache();
            await asyncVirtualize.RefreshDataAsync();

            StateHasChanged();
        }
        catch (Exception ex)
        {
            apiClient.CancelChanges();
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }

    public async Task Delete()
    {
        try
        {
            apiClient.RemoveEntity(currentNumberSeries);
            await apiClient.SaveChanges();

            apiClient.ClearEntitiesCache();
            await asyncVirtualize.RefreshDataAsync();

            viewNotifier.Show($"{currentNumberSeries.Description} deleted", ViewNotifierType.Success, L["Operation Successful"]);
        }
        catch (Exception ex)
        {
            apiClient.CancelChanges();
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }

        currentNumberSeries = new NumberSeries();

        deleteDialogOpen = false;
    }
}

