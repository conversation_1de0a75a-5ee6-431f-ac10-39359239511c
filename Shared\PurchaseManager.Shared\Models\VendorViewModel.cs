﻿using System.ComponentModel.DataAnnotations;

namespace PurchaseManager.Shared.Models;

public class GetVendorDto
{
    public int RowId { get; set; }

    [StringLength(100)]
    public string Number { get; set; } = null!;

    [Required]
    [StringLength(160)]
    public string Name { get; set; } = null!;

    [StringLength(160)]
    public string SearchName { get; set; } = null!;

    [StringLength(160)]
    public string Address { get; set; } = null!;

    [StringLength(160)]
    public string Contact { get; set; } = null!;

    [StringLength(160)]
    public string Phone { get; set; } = null!;

    [StringLength(100)]
    public string Telex { get; set; } = null!;

    public int Blocked { get; set; }

    [StringLength(160)]
    public string Fax { get; set; } = null!;

    [StringLength(100)]
    public string VatRegistrationNumber { get; set; } = null!;

    [StringLength(160)]
    public string Email { get; set; } = null!;

    [StringLength(400)]
    public string? Description { get; set; }

    public int? Status { get; set; }

    public DateTime? LastModifiedTime { get; set; }
    public int? PaymentDays { get; set; }
}
