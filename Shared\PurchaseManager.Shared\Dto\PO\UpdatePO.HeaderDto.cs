﻿using System.ComponentModel.DataAnnotations;
using System.Globalization;
namespace PurchaseManager.Shared.Dto.PO;

public class UpdatePOHeaderDto
{
    [Required]
    public string Number { get; set; } = null!;
    [Required]
    public string VendorNo { get; set; } = null!;
    /// <summary>
    ///     không sử dụng trên API
    /// </summary>
    [Required]
    public DateTime OrderDate { get; set; }
    [Required]
    public DateTime DueDate { get; set; } 
    public string PurchaseUser { get; set; } = string.Empty;
    public string PostingDescription { get; set; } = string.Empty;
    public string VendorApprovalBy { get; set; } = string.Empty;
    public string PurchaserApprovalBy { get; set; } = string.Empty;
    public int DocNoOccurrence { get; set; }
    public string YourReference { get; set; } = string.Empty;
}
