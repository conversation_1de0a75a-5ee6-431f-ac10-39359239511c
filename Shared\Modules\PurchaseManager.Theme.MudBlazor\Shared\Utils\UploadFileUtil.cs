﻿using Microsoft.AspNetCore.Components.Forms;

using MudBlazor;

using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Interfaces;

using System.Net.Http.Headers;

namespace PurchaseManager.Theme.Material.Shared.Utils;

public class UploadFileUtil
{
    public async Task<ApiResponseDto> UploadFiles(MudFileUpload<IReadOnlyList<IBrowserFile>> files, IApiClient apiClient, string documentNumber, string prefix)
    {
        if (files.Files is not { Count: > 0 })
        {
            return new ApiResponseDto
            {
                Message = "No files to upload."
            };
        }
        var statusList = new List<int>();
        try
        {
            foreach (var file in files.Files)
            {
                var formData = new MultipartFormDataContent();
                var streamContent = new StreamContent(file.OpenReadStream(209715200));
                streamContent.Headers.ContentType = new MediaTypeHeaderValue(file.ContentType);
                formData.Add(streamContent, "file", file.Name);
                formData.Add(new StringContent(prefix), "prefix");
                formData.Add(new StringContent(documentNumber), "documentNo");
                var result = await apiClient.UploadFiles(formData);
                statusList.Add(result.StatusCode);
            }

            if (statusList.All(status => status == 201))
            {
                return new ApiResponseDto
                {
                    StatusCode = 201,
                    Message = "All files uploaded successfully"
                };
            }
            return new ApiResponseDto
            {
                StatusCode = statusList.FirstOrDefault(status => status != 201),
                Message = "Some files failed to upload"
            };
        }
        catch (Exception e)
        {
            return new ApiResponseDto
            {
                StatusCode = statusList.FirstOrDefault(status => status != 201),
                Message = "Has error while upload files. Error: " + e.Message
            };
        }
    }
}