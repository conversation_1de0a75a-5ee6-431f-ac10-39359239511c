﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Providers;
namespace PurchaseManager.Theme.Material.Demo.Pages.Vendors;
public partial class EditVendor : ComponentBase
{
    [Inject]
    private AuthenticationStateProvider AuthStateProvider { get; set; }
    [Inject] IViewNotifier viewNotifier { get; set; }
    [Inject] IMasterDataApiClient apiClient { get; set; }
    [Inject] protected IStringLocalizer<Global> L { get; set; }
    [Parameter]
    public string VendorNumberParam { get; set; }
    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; }
    private GetVendorDto _vendor { get; set; }
    private bool isLoad { get; set; }
    private bool isPurchaseUser { get; set; }
    private bool isPurchaseManager { get; set; }
    private bool isVendor { get; set; }
    private bool isVendorContact { get; set; }
    private bool isAdmin { get; set; }

    private bool isAuthorized { get; set; }
    private UserViewModel userViewModel { get; set; } = new UserViewModel();
    protected override async Task OnInitializedAsync()
    {
        isLoad = true;
        await GetUserInfoAsync();
        await GetUserRolesAsync();
        AuthorizeUser();
        if (isAuthorized) await LoadDataAsync();
        await base.OnInitializedAsync();
        isLoad = false;
    }
    private void AuthorizeUser()
    {
        var userVendorCode = userViewModel.VendorCode;

        if (VendorNumberParam.Equals(userVendorCode, StringComparison.OrdinalIgnoreCase) && !isVendorContact)
        {
            isAuthorized = true;
        }
        if (isAdmin || isPurchaseUser || isPurchaseManager)
            isAuthorized = true;

    }
    protected async Task OnToggleStatusCallback(bool isSuccess)
    {
        if (isSuccess)
        {
            await LoadDataAsync();
        }
    }
    protected async Task LoadDataAsync()
    {
        try
        {
            var result = await apiClient.BuildVendorViewModel(VendorNumberParam);
            if (!result.IsSuccessStatusCode)
                viewNotifier.Show(result.Message, ViewNotifierType.Error, L["Operation Failed"]);
            _vendor = result.Result;
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }

    }
    private async Task GetUserInfoAsync()
    {
        userViewModel = await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
    }
    private async Task GetUserRolesAsync()
    {
        var authState = await AuthenticationStateTask;
        var user = authState.User;

        isPurchaseUser = user.IsAdmin();
        isPurchaseManager = user.IsPurchaseManager();
        isVendor = user.IsVendor();
        isVendorContact = user.IsVendorContact();
        isAdmin = user.IsAdmin();
    }
}
