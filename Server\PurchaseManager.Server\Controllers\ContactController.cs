using Breeze.AspNetCore;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Aop;
using PurchaseManager.Shared.Dto.Contact;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Contact;
namespace PurchaseManager.Server.Controllers;

using static StatusCodes;

[Route("api/data")]
[ApiController]
// [Authorize]
[ApiResponseException]

public class ContactController : ControllerBase
{
    private readonly IContactManager _contactManager;
    private readonly IStringLocalizer<Global> L;

    public ContactController(IContactManager contactManager, IStringLocalizer<Global> l)
    {
        L = l;
        _contactManager = contactManager;
    }
    
    [HttpGet("contacts")]
    [BreezeQueryFilter]
    public IQueryable<GetContactDto> GetAllContacts([FromQuery] ContactFilter filter)
    {
        return _contactManager.GetAllContacts(filter);
    }
    
    [HttpGet("contacts/{number}")]
    public async Task<ApiResponse<GetContactDto>> GetContactByNumber(string number)
    {
        return await _contactManager.GetContactByNumberAsync(number);
    }
    
    [HttpGet("contacts/vendor/{vendorNumber}")]
    public ApiResponse GetAllContactsByVendorNumber(string vendorNumber)
    {
        return _contactManager.GetAllContactsByVendorNumber(vendorNumber);
    }

    [HttpGet("Search")]
    public async Task<ApiResponse> Search(string number, string name)
    {
        var queryString = HttpContext.Request.QueryString.ToString();
        return await _contactManager.SearchAutocomplete(number, name, queryString);
    }
    
    [HttpPost("contact")]
    public async Task<ApiResponse> CreateContactAsync([FromBody] CreateContactDto dto)
    {
        return ModelState.IsValid ? await _contactManager.CreateContactAsync(dto)
            : new ApiResponse(Status400BadRequest, L["InvalidData"],
            ModelState.Select(x => x.Value.Errors.Select(y => y.ErrorMessage)));
    }
    
    [HttpPost("contact/{number}/block-unblock")]
    public async Task<ApiResponse> BlockOrUnblockContactAsync(string number)
    {
        return await _contactManager.BlockOrUnblockContactAsync(number);
    }
    
    [HttpPut("contact")]
    public async Task<ApiResponse> UpdateContactAsync([FromBody] UpdateContactDto dto)
    {
        return ModelState.IsValid
            ? await _contactManager.UpdateContactAsync(dto)
            : new ApiResponse(Status400BadRequest, L["InvalidData"], ModelState);
    }
}
