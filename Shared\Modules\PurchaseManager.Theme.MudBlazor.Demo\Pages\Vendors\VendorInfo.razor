@page "/vendor/info"
@attribute [Authorize]

<PageTitle>@L["Vendor Info"]</PageTitle>
<MudStack Row Justify="Justify.SpaceAround" Class="mt-6">
    <MudPaper Height="500px" Width="500px" Class="d-flex flex-column justify-content-center align-items-center">
        <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.Center" Style="width: 100%; height: 100%">
            <MudText Color="Color.Primary">@L["VendorInformation"]</MudText>
            <MudIconButton Icon="@Icons.Material.Filled.ArrowOutward" OnClick="@NavigateToVendorInfoUrl"
                Variant="Variant.Text" Color="Color.Primary" />
        </MudStack>
    </MudPaper>
    <MudPaper Height="500px" Width="500px" Class="d-flex flex-column justify-content-center align-items-center">
        <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.Center" Style="width: 100%; height: 100%">
            <MudText Color="Color.Tertiary">@L["ListOfVendorContact"]</MudText>
            <MudIconButton Icon="@Icons.Material.Filled.ArrowOutward" OnClick="@NavigateToVendorContactInfoUrl"
                Variant="Variant.Text" Color="Color.Tertiary" />
        </MudStack>
    </MudPaper>
</MudStack>