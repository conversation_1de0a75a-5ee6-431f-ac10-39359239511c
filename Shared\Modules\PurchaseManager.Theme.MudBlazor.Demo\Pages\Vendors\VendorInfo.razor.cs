using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Providers;
namespace PurchaseManager.Theme.Material.Demo.Pages.Vendors;
public partial class VendorInfo : ComponentBase
{
    [Inject]
    private IStringLocalizer<Global> L { get; set; }
    [Inject]
    private AuthenticationStateProvider AuthStateProvider { get; set; }
    [Inject]
    protected NavigationManager NavigationManager { get; set; }
    private UserViewModel UserViewModel { get; set; } = new UserViewModel();
    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; }
    private bool IsAdmin { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await GetUserRolesAsync();
        await GetUserInfoAsync();
        await base.OnInitializedAsync();
    }

    private async Task GetUserInfoAsync()
        => UserViewModel = await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();

    private async Task GetUserRolesAsync()
    {
        var authState = await AuthenticationStateTask;
        var user = authState.User;

        IsAdmin = user.IsAdmin();
    }
    protected void NavigateToVendorInfoUrl()
    {
        if (string.IsNullOrEmpty(UserViewModel.VendorCode))
        {
            NavigationManager.NavigateTo("vendor/info");
        }
        if (!IsAdmin)
        {
            NavigationManager.NavigateTo($"vendors/{UserViewModel.VendorCode}/detail");
        }
        NavigationManager.NavigateTo("vendors");
    }
    protected void NavigateToVendorContactInfoUrl()
    {
        if (string.IsNullOrEmpty(UserViewModel.VendorCode))
        {
            NavigationManager.NavigateTo("vendor/info");
        }
        if (!IsAdmin)
        {
            NavigationManager.NavigateTo($"vendors/{UserViewModel.VendorCode}/contacts");
        }
        NavigationManager.NavigateTo("admin/contacts");
    }
}
