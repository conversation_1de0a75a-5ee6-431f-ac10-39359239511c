{
  "ConnectionStrings": {
    "PostgresConnection": "host=localhost;database=PurchaseManager;user id=postgres;password=password123",
    // Azure Hosting: Use format below.
    // "DefaultConnection": "Data Source=tcp:<SQL_SERVER_NAME>.database.windows.net,1433;Initial Catalog=<DATABASE_NAME>;Persist Security Info=False;User ID=<ADMIN_USER>;Password=<ADMIN_PASSWORD>;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;MultipleActiveResultSets=True;"
    //    "DefaultConnection": "Server=**************;Database=PurchaseManager;User Id=trungit;Password=************;Trusted_Connection=False;Encrypt=False;MultipleActiveResultSets=true;"
      "DefaultConnection": "Server=************;Database=PurchaseManager;User Id=trungit;Password=************;Trusted_Connection=False;Encrypt=False;MultipleActiveResultSets=true;"
  },
  // Azure Hosting: Read the setup documentation at https://blazor-boilerplate.readthedocs.io/
  "HostingOnAzure": {
    "RunsOnAzure": false,
    "RunningAsAppService": true,
    "RunningAsDocker": false,
    // not implemented yet
    "AzureKeyVault": {
      "UsingKeyVault": true,
      "UseManagedAppIdentity": true,
      "AppKey": "",
      // not implemented yet
      "AppSecret": "",
      "KeyVaultUri": "https://<YOUR_VAULTNAME_HERE>.vault.azure.net/",
      "CertificateIdentifier": "https://<YOUR_VAULTNAME_HERE>.vault.azure.net/certificates/BBAUTH/<HEX_VERSION_STRING_HERE>",
      "CertificateName": "BBAUTH",
      "StorageAccountBlobBaseUrl": "https://<YOUR_STORAGE_ACCOUNT_NAME_HERE>.blob.core.windows.net",
      "ContainerName": "blazor-boilererplate-keys",
      "KeysBlobName": "keys.xml"
    }
  },
  "ExternalAuthProviders": {
    "Google": {
      "Enabled": false,
      "ClientId": "xxx",
      "ClientSecret": "xxx"
    },
    "Facebook": {
      "Enabled": false,
      "AppId": "xxx",
      "AppSecret": "xxx"
    },
    "Twitter": {
      "Enabled": false,
      "ConsumerKey": "xxx",
      "ConsumerSecret": "xxx"
    },
    "Apple": {
      "Enabled": false,
      "ClientId": "xxx",
      "KeyId": "xxx",
      "TeamId": "xxx"
    },
    "Microsoft": {
      "Enabled": false,
      "ClientId": "xxx",
      "ClientSecret": "xxx"
    }
  },
  "PurchaseManager": {
    "ApplicationUrl": "https://www.purchasemanager.com",
    "RequireConfirmedEmail": true,
    "API": {
      "Logging": {
        "Enabled": false,
        "IgnorePaths": [
          "/api/account",
          "/api/admin",
          "/api/apilog"
        ]
      },
      "Doc": {
        "Enabled": true
      }
    },
    "UsePostgresServer": false,
    "IS4ApplicationUrl": "https://www.purchasemanager.com",
    "UseLocalCertStore": false,
    "CertificateThumbprint": "PutYourSSLThumbPrintHere",
    // Azure Hosting: X.509 SHA-1 Thumbprint (in hex) from Azure Key Vault in Azure Portal.
    "CookieExpireTimeSpanDays": 30
  },
  "Modules": {
    "PurchaseManager.GoogleAnalytics": {
      "TrackingId": "UA-XXXXXXXX-X"
    }
  },
  "Serilog": {
    "Using": [
      "Serilog.Sinks.Console",
      "Serilog.Sinks.File",
      "Serilog.Sinks.MSSQLServer"
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId"
    ],
    "MinimumLevel": {
      "Default": "Warning",
      // Azure Hosting: Can be useful to set to 'Debug' during setup
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning",
        "Finbuckle.MultiTenant": "Warning"
      }
    },
    "WriteTo": [
      {
        // If you're not using mssql as a backend db either remove this section or just make sure that the target connection string is not blank
        // Does not throw exception if connection string is invalid, only if it doesn't exist
        "Name": "MSSqlServer",
        // see https://github.com/serilog/serilog-sinks-mssqlserver/blob/dev/README.md for additional config options
        "Args": {
          "connectionString": "DefaultConnection",
          "tableName": "Logs",
          // Table Defined in PurchaseManager.Shared/DataModels/Logs.cs
          "autoCreateSqlTable": false,
          "restrictedToMinimumLevel": "Warning"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs\\log-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 5
        }
      },
      {
        "Name": "File",
        "Args": {
          "RestrictedToMinimumLevel": "Warning",
          "path": "Logs\\log-warning-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 5
        }
      }
      //,{"Name": "Console"} //Helpful when Debugging
    ]
  },
  "AllowedHosts": "*"
}
