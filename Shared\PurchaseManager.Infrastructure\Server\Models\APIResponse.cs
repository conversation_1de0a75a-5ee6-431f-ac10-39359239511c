﻿using System.Reflection;
using System.Runtime.Serialization;
using Newtonsoft.Json;
namespace PurchaseManager.Infrastructure.Server.Models
{
    [Serializable]
    [DataContract]
    public class ApiResponse<T>
    {
        [DataMember]
        public string Version { get; set; }

        [DataMember]
        public int StatusCode { get; set; }

        public bool IsSuccessStatusCode => StatusCode >= 200 && StatusCode < 300;

        [DataMember]
        public string Message { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public T Result { get; set; }

        [JsonConstructor]
        public ApiResponse(int statusCode, string message = "", T result = default)
        {
            StatusCode = statusCode;
            Message = message;
            Result = result;

            Version = Assembly.GetExecutingAssembly().GetName().Version.ToString();

            if (string.IsNullOrWhiteSpace(message))
                Message = IsSuccessStatusCode ? "Operation Successful" : "Operation Failed";
        }

        static public implicit operator ApiResponse<T>(ApiResponse r)
        {
            return new ApiResponse<T>(r.StatusCode, r.Message, r.Result is T ? (T)r.Result : default);
        }
    }

    [Serializable]
    [DataContract]
    public class ApiResponse : ApiResponse<object>
    {
        [JsonConstructor]
        public ApiResponse(int statusCode, string message = "", object result = null) : base(statusCode, message)
        {
            StatusCode = statusCode;
            Message = message;
            Result = result;
        }

        public ApiResponse(int statusCode) : base(statusCode, "")
        {
            StatusCode = statusCode;
        }
        public static ApiResponse S200(string message = "Operation Successful", object result = null)
            => new ApiResponse(200, message, result);

        public static ApiResponse S500(string message = "Operation Failed", object result = null)
            => new ApiResponse(500, message, result);

        public static ApiResponse BadRequest(string message = "Bad Request", object result = null)
            => new ApiResponse(400, message, result);

        public static ApiResponse Unauthorized(string message = "Unauthorized")
            => new ApiResponse(401, message);

        public static ApiResponse S404(string message = "Not Found")
            => new ApiResponse(404, message);

        public static ApiResponse S400(string message = "Bad Request")
        {
            return new ApiResponse(400, message);
        }

    }
}
