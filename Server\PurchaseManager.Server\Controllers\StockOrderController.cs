﻿using Microsoft.AspNetCore.Mvc;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Models.StockOrder;
namespace PurchaseManager.Server.Controllers;

[SecurityHeaders]
[Route("api/[controller]")]
[ApiController]
public class StockOrderController : ControllerBase
{
    private readonly IStockOrderManager _stockOrderManager;

    public StockOrderController(IStockOrderManager stockOrderManager)
    {
        _stockOrderManager = stockOrderManager;
    }

    /// <summary>
    /// Get PO lines with conversion rate display for Stock Order context
    /// </summary>
    /// <param name="number">Purchase Order number</param>
    /// <returns>List of PO lines with conversion rate information</returns>
    [HttpGet("stock-order-lines/{number}")]
    public async Task<ApiResponse> GetLinesForStockOrder(string number)
    {
        return await _stockOrderManager.GetLinesStockOrderAsync(number);
    }

    [HttpGet("Gets")]
    public async Task<ApiResponse> GetStockOrderByPoHeader([FromQuery] StockOrderFilter filter)
        => await _stockOrderManager.GetReceiveLotsByPoHeaderAsync(filter);

    [HttpPost("Create")]
    public async Task<ApiResponse> CreateStockOrder([FromBody] CreateStockOrderDto stockOrder)
        => await _stockOrderManager.CreateReceiveLotsAsync(stockOrder);
    [HttpPut("update/{number}")]
    public async Task<ApiResponse> UpdateStockOrder(string number, [FromBody] UpdateStockOrderDto stockOrder)
        => await _stockOrderManager.UpdateReceiveLotsAsync(number, stockOrder);

    /// <summary>
    /// Update multiple StockOrders status in batch
    /// </summary>
    [HttpPut("batch-update-status")]
    public async Task<ApiResponse> BatchUpdateStockOrderStatus([FromBody] BatchUpdateStockOrderStatusDto dto)
        => await _stockOrderManager.BatchUpdateStockOrderStatusAsync(dto);

    [HttpPut("save-drafts/{headerNumber}")]
    public async Task<ApiResponse> SaveDraftStockOrders(string headerNumber)
        => await _stockOrderManager.SaveDraftStockOrdersAsync(headerNumber);

    #region New Header/Line Endpoints
    /// <summary>
    /// Get Stock Order Headers with pagination
    /// </summary>0
    [HttpGet("headers")]
    public async Task<ApiResponse> GetStockOrderHeaders([FromQuery] StockOrderFilter filter)
    {
        return await _stockOrderManager.GetStockOrderHeadersAsync(filter);
    }

    /// <summary>
    /// Get specific Stock Order Header by number
    /// </summary>
    [HttpGet("headers/{number}")]
    public async Task<ApiResponse> GetStockOrderHeader(string number)
    {
        return await _stockOrderManager.GetStockOrderHeaderByNumberAsync(number);
    }

    /// <summary>
    /// Create a new draft Stock Order (doesn't update PO lines until finalized)
    /// </summary>
    [HttpPost("headers/draft")]
    public async Task<ApiResponse> CreateDraftStockOrder([FromBody] CreateStockOrderHeaderDto createDto)
    {
        return await _stockOrderManager.CreateDraftStockOrderAsync(createDto);
    }

    /// <summary>
    /// Update a draft Stock Order (only allowed for draft status)
    /// </summary>
    [HttpPut("headers/{stockOrderNumber}/draft")]
    public async Task<ApiResponse> UpdateDraftStockOrder(string stockOrderNumber, [FromBody] SaveStockOrderDto updateDto)
    {
        return await _stockOrderManager.UpdateDraftStockOrderAsync(stockOrderNumber, updateDto);
    }

    /// <summary>
    /// Finalize a Stock Order - this saves the data and updates PO line quantities
    /// </summary>
    [HttpPost("headers/{stockOrderNumber}/finalize")]
    public async Task<ApiResponse> FinalizeStockOrder(string stockOrderNumber, [FromBody] SaveStockOrderDto finalizeDto)
    {
        return await _stockOrderManager.FinalizeStockOrderAsync(stockOrderNumber, finalizeDto);
    }

    /// <summary>
    /// Create final Stock Order directly (Header + Lines without draft state)
    /// </summary>
    [HttpPost("headers/final")]
    public async Task<ApiResponse> CreateFinalStockOrder([FromBody] CreateStockOrderHeaderDto createDto)
    {
        return await _stockOrderManager.CreateFinalStockOrderAsync(createDto);
    }
    #endregion
}
