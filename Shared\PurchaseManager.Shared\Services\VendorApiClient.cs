using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models;
namespace PurchaseManager.Shared.Services;
public class VendorApiClient : BaseApiClient, IVendorApiClient
{

    public VendorApiClient(HttpClient httpClient, ILogger<ApiClient> logger, IJSRuntime jsRuntime) : base(httpClient, logger)
    {
    }
    public async Task<ApiResponseDto<List<GetVendorDto>>> GetVendorsByNumber(string number, CancellationToken token = default)
    {
        var result = await httpClient.GetFromJsonAsync<ApiResponseDto<List<GetVendorDto>>>($"api/vendor/Search?number={number}", token);
        return result;
    }
    public async Task<ApiResponseDto<GetVendorDto>> GetSingleVendor(string vendorNumber)
        => await httpClient.GetFromJsonAsync<ApiResponseDto<GetVendorDto>>(
        $"/api/Vendor/SingleVendor?number={vendorNumber}");
}
