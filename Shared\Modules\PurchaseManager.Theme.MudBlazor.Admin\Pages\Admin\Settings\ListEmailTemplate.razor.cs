﻿using Microsoft.AspNetCore.Components;

using PurchaseManager.Shared.Dto.Email;
using PurchaseManager.Theme.Material.Shared.Components;

namespace PurchaseManager.Theme.Material.Admin.Pages.Admin.Settings
{
    public partial class ListEmailTemplatePage : ItemsTableBase<DetailEmailTemplate>
    {
        protected bool isLoading { get; set; }
        [Inject] protected NavigationManager navigation { get; set; }
        protected List<DetailEmailTemplate> emailTemplates { get; set; } = new List<DetailEmailTemplate>();

        protected override void OnInitialized()
        {
            isLoading = true;
            from = "GetEmailTemplates";
            isLoading = false;
            base.OnInitialized();
        }


    }
}
