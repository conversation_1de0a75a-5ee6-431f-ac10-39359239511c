﻿using System.ComponentModel.DataAnnotations;
namespace PurchaseManager.Shared.Models.Account;

public class RegisterVendorViewModel
{
    public string TaxCode { get; set; }
    public string? EmployeeCode { get; set; }// If employee of Trung Son
    public string UserName { get; set; }

    [DataType(DataType.Password)]
    public string Password { get; set; }
    public string PasswordConfirm { get; set; }
}
