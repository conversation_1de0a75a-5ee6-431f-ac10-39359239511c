@inherits UnitOfMeasurePage
@page "/unit-of-measures"
@attribute [Authorize(Policies.IsAdmin)]

<PageTitle>List Unit Of Measure</PageTitle>
@if (isLoading)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    <MudCard Elevation="0">
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h5">Unit Of Measure</MudText>
                <MudText Typo="Typo.caption">List Unit Of Measure</MudText>
            </CardHeaderContent>
            <CardHeaderActions>
                <MudButton StartIcon="@Icons.Material.Filled.Add"
                           OnClick="@(() => {navigation.NavigateTo("/unit-of-measure/create");})"
                           ButtonType="ButtonType.Button" Variant="Variant.Filled" 
                           Color="Color.Primary" 
                           Class="">
                    @L["Add"]
                </MudButton>
                <MudIconButton Icon="@Icons.Material.Filled.Refresh" OnClick="@(() => Reload())" Size="Size.Medium" Class="ma-2" />
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent>
            <MudTable ServerData="@(new Func<TableState, CancellationToken, Task<TableData<UnitOfMeasureDto>>>(ServerReload))"
                      Striped
                      Bordered
                      Dense
                      Outlined
                      Hover
                      Class="pa-2"
                      LoadingProgressColor="Color.Info" 
                      @ref="table">
                <ToolBarContent>

                    <MudSpacer />
                    <MudTextField Variant="Variant.Text" T="string"
                                  @ref="SearchRef"
                                  ValueChanged="@(s => OnSearch(s))" Placeholder="@(L["Search"])" Adornment="Adornment.Start"
                                  AdornmentIcon="@Icons.Material.Filled.Search" />
                    <MudIconButton Icon="@Icons.Material.Filled.Clear" Color="Color.Error" OnClick="@(async() => await SearchRef.Clear())" Size="Size.Medium" Class="ma-2" />
                    <MudSpacer />

                </ToolBarContent>
                <HeaderContent>
                    <MudTh>
                        <MudTableSortLabel SortLabel="Code" T="UnitOfMeasureDto">Code</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="Description" T="UnitOfMeasureDto">Description</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="Status" T="UnitOfMeasureDto">Status</MudTableSortLabel>
                    </MudTh>
                </HeaderContent>
                <RowTemplate Context="row">
                    <MudTd DataLabel="ItemNumber">
                        <MudLink Href="@("/unit-of-measure/" + @row.Code)">@row.Code</MudLink>
                    </MudTd>
                    <MudTd DataLabel="Description">@row.Description </MudTd>
                    <MudTd DataLabel="Status">@row.Status </MudTd>
                </RowTemplate>
                <PagerContent>
                    <MudTablePager RowsPerPageString="Rows per page" />
                </PagerContent>
            </MudTable>
        </MudCardContent>
    </MudCard>

}
