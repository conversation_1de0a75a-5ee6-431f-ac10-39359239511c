﻿using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
namespace PurchaseManager.Server.Services.PurchaseOrder.Validation;

public class HeaderValidation
{
    private readonly IPOHeaderManager _poHeaderManager;
    public HeaderValidation(IPOHeaderManager poHeaderManager)
    {
        _poHeaderManager = poHeaderManager;
    }
    public async Task<ApiResponse> ValidationHeader(HeaderValidationDto headerValidation)
    {
        var vendorInfo = await _poHeaderManager.ValidateVendorBeforePOCreationAsync(headerValidation.VendorNumber);
        if (!vendorInfo.IsSuccessStatusCode)
        {
            return vendorInfo;
        }

        var purchaseUserResult = await _poHeaderManager.UserIdValidAsync(headerValidation.PurchaseUser);
        if (!purchaseUserResult.IsSuccessStatusCode)
        {
            return purchaseUserResult;
        }

        var vendorApproveResult = await _poHeaderManager.UserIdValidAsync(headerValidation.VendorApprovalBy);
        if (!vendorApproveResult.IsSuccessStatusCode)
        {
            return ApiResponse.S404("VendorApprove is not found in system or blocked");
        }

        var purchaserApproveResult = await _poHeaderManager.UserIdValidAsync(headerValidation.PurchaserApprovalBy);
        return !purchaserApproveResult.IsSuccessStatusCode ? ApiResponse.S404("PurchaserApprove is not found in system or blocked")
            : ApiResponse.S200();

    }
}
public class HeaderValidationDto
{
    public string VendorNumber { get; set; } = string.Empty;
    public string PurchaseUser { get; set; } = string.Empty;
    public string VendorApprovalBy { get; set; } = string.Empty;
    public string PurchaserApprovalBy { get; set; } = string.Empty;
}
