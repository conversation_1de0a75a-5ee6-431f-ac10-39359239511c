namespace PurchaseManager.Shared.Dto.StockOrder;

public class GetStockOrderHeaderDto
{
  public string Number { get; set; } = null!;
  public string POHeaderNumber { get; set; } = null!;
  public int DocumentType { get; set; }
  public DateTime CreateAt { get; set; }
  public string CreateBy { get; set; } = null!;
  public int Status { get; set; }
  public bool IsERPSynced { get; set; }
  public string Note { get; set; } = string.Empty;
  public List<GetStockOrderLineDto> Lines { get; set; } = new List<GetStockOrderLineDto>();
}

public class GetStockOrderLineDto
{
  public string StockOrderNumber { get; set; } = null!;
  public int LineNumber { get; set; }
  public string POHeaderNumber { get; set; } = null!;
  public int POLineNumber { get; set; }
  public int DocumentType { get; set; }
  public string ItemNumber { get; set; } = null!;
  public string LotNo { get; set; } = string.Empty;
  public DateTime? ExpirationDate { get; set; }
  public int TotalQuantity { get; set; }
  public int QuantityReceived { get; set; }
  public string ItemName { get; set; } = string.Empty;
  public string Note { get; set; } = string.Empty;
  public DateTime CreateAt { get; set; }
  public string CreateBy { get; set; } = null!;
}
