@page "/admin/settings/email/sent-email"
@inherits PurchaseManager.Theme.Material.Admin.Pages.Admin.Settings.SentEmailBasePage
@using PurchaseManager.Shared.Models
@layout AdminLayout

@if (isLoading)
{
    <LoadingBackground />
}
else
{
    <MudCard Elevation="0" Class="mt-2" Outlined>
        <MudCardHeader Class="">
            <CardHeaderContent>
                <div>
                    <MudText Typo="Typo.h5">Sent Email</MudText>
                    <MudText Typo="Typo.caption">List Email sent at: @sentDate.ToString("MM/yyyy") </MudText>
                </div>
                <MudStack Row>
                    <MudDatePicker Label="Create Month" OpenTo="OpenTo.Month" FixYear="@DateTime.Today.Year"
                        FixDay="@DateTime.Today.Day" DateFormat="MM/yyyy"
                        DateChanged="@(async (date) => await OnSearchByDate(date))" Class="mr-4" />
                    <MudTextField @ref="userNameFilterRef" Immediate="true" T="string"
                        OnAdornmentClick="@(async _ => { await ClearNameFilter(); })" ValueChanged="@(s => FilterByUser(s))"
                        Label="Create by" AdornmentIcon="@Icons.Material.Filled.Clear" AdornmentColor="Color.Error"
                        Adornment="Adornment.End">
                    </MudTextField>
                    <MudTextField @ref="poNumberFilterRef" Immediate="true" T="string"
                        OnAdornmentClick="@(async _ => { await ClearPoNumberFilter(); })"
                        ValueChanged="@(s => FilterByPONumber(s))" Label="PO Number"
                        AdornmentIcon="@Icons.Material.Filled.Clear" AdornmentColor="Color.Error" Adornment="Adornment.End">
                    </MudTextField>
                    <MudAutocomplete T="PurchaseManager.Shared.Models.GetVendorDto" ShrinkLabel="true"
                        Label="Tìm theo nhà cung cấp" ShowProgressIndicator="true" @ref="vendorAutoRef"
                        ValueChanged="@(async _ => await OnSearchContactByVendorName(_))"
                        ToStringFunc="dto => dto == null ? null : string.Concat(dto.Number, ' ', ('-'), ' ', dto.Name)"
                        AdornmentIcon="@Icons.Material.Filled.Clear" AdornmentColor="Color.Error"
                        OnAdornmentClick="@(async _ => { await vendorAutoRef.ResetAsync(); await Reload(); })"
                        ResetValueOnEmptyText SearchFunc="@ItemSearch">
                    </MudAutocomplete>
                </MudStack>
            </CardHeaderContent>
        </MudCardHeader>
        <MudCardContent>
            <MudTable @ref="table"
                ServerData="@(new Func<TableState, CancellationToken, Task<TableData<PurchaseManager.Shared.Dto.Email.SentEmailDto>>>(ServerReload))"
                Striped="false" Bordered="false" Dense="true" Hover="true" Elevation="0" Outlined="true" FixedHeader="true"
                LoadingProgressColor="Color.Primary">
                <HeaderContent>
                    <MudTh>Sent By</MudTh>
                    <MudTh>Sent At</MudTh>
                    <MudTh>Vendor Number</MudTh>
                    <MudTh>Po Number</MudTh>
                    <MudTh>Type</MudTh>
                    <MudTh>Actions</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd>@context.SentBy</MudTd>
                    <MudTd>@context.SentAt</MudTd>
                    <MudTd>@context.VendorNumber</MudTd>
                    <MudTd>@context.PoNumber</MudTd>
                    <MudTd>
                        @foreach (EmailType type in Enum.GetValues(typeof(EmailType)))
                        {
                            @if ((int)@context.Type == (int)type)
                            {
                                <MudChip T="string" Size="Size.Small" Variant="Variant.Text" Color="Color.Info">
                                    @L[type.GetDisplayName()] </MudChip>
                            }
                        }
                    </MudTd>
                    <MudTd>
                        <MudIconButton Icon="@Icons.Material.Filled.Preview" OnClick="@(() => ShowPreview(@context.Id))"
                            Color="Color.Surface" />
                    </MudTd>
                </RowTemplate>
                <PagerContent>
                    <MudTablePager RowsPerPageString=@L["Rows per page"] />
                </PagerContent>
            </MudTable>
        </MudCardContent>
    </MudCard>
}
<MudDialog @bind-Visible="@isShowPreview"
    Options="new DialogOptions() { FullWidth = true, MaxWidth = MaxWidth.ExtraLarge, CloseButton = true, CloseOnEscapeKey = true }">
    <TitleContent>
        <MudText Typo="Typo.h6">
            Sent Email Preview
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudBlazor.Extensions.Components.MudExHtmlEdit ReadOnly="false" Class="border-0" UpdateValueOnChange="true"
            Value="@currentSentEmail.Body" OnClickStopPropagation="true" OnContextMenuPreventDefault="true"
            Height="1000" OnContextMenuStopPropagation="true">
        </MudBlazor.Extensions.Components.MudExHtmlEdit>
    </DialogContent>
</MudDialog>


@code
{

    protected MudTextField<string> userNameFilterRef { get; set; }
    protected MudTextField<string> poNumberFilterRef { get; set; }
    protected MudAutocomplete<GetVendorDto> vendorAutoRef { get; set; } = new MudAutocomplete<GetVendorDto>();

    protected async Task ClearNameFilter()
    {
        userNameFilterRef.Clear();
        sentEmailFilter.SentBy = null;
        await Reload();
    }

    protected async Task ClearPoNumberFilter()
    {
        poNumberFilterRef.Clear();
        sentEmailFilter.PoNumber = null;
        await Reload();
    }

}