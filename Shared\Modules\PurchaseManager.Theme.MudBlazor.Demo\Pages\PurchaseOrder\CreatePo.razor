@page "/po/create"
@using PurchaseManager.Shared.Dto.Contact
@attribute [Authorize]

<PageTitle>Create P.O </PageTitle>
@if (IsLoading || UserViewModel is null)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate />
}
else
{
    <MudStack>
        <MudCard>
            <MudCardHeader>@L["Create Manual Purchase Order"]</MudCardHeader>
            <MudCardContent>
                <MudForm Spacing="6">
                    <MudText>
                        @L["Buy From"]
                    </MudText>
                    <MudRadioGroup Value="@SelectedType" T="TypeOfProviderForPOEnum"
                        ValueChanged="@OnTypeOfPORadioGroupChanged">
                        <MudRadio Value="@TypeOfProviderForPOEnum.Vendor" Color="Color.Primary">Vendor</MudRadio>
                        <MudRadio Value="@TypeOfProviderForPOEnum.Contact" Color="Color.Primary">Contact</MudRadio>
                    </MudRadioGroup>
                    @if (string.IsNullOrEmpty(UserViewModel.VendorCode))
                    {
                        @if (SelectedType.Equals(TypeOfProviderForPOEnum.Vendor))
                        {
                            <MudAutocomplete T="GetVendorDto" ShrinkLabel Label="NCC" FullWidth="false" ShowProgressIndicator Clearable
                                             @ref="AutoComplete" ValueChanged="@(value => SelectedVendorNumber = value.Number)"
                                ToStringFunc="dto => dto == null ? null : string.Concat(dto.Number, ' ', ('-'), ' ', dto.Name)"
                                Required SearchFunc="@ItemSearch" Margin="Margin.Dense" Dense>
                                <ProgressIndicatorInPopoverTemplate>
                                    <MudList T="String" ReadOnly>
                                        <MudListItem>
                                            Loading...
                                        </MudListItem>
                                    </MudList>
                                </ProgressIndicatorInPopoverTemplate>
                                <ItemTemplate Context="e">
                                    <MudStack Row Justify="Justify.SpaceBetween">
                                        <MudStack>
                                            <MudText>@e.Name</MudText>
                                            <MudText Typo="Typo.body2"><b>Number: </b>@e.Number</MudText>
                                        </MudStack>
                                        <MudStack AlignItems="AlignItems.Center" Justify="Justify.Center">
                                            <MudChip Size="Size.Small" Color="@(e.Blocked == 1 ? Color.Default : Color.Success)"
                                                     T="String">@(e.Blocked == 1 ? "Block" : "Active")</MudChip>
                                        </MudStack>
                                    </MudStack>
                                </ItemTemplate>
                            </MudAutocomplete>
                        }
                        @if (SelectedType.Equals(TypeOfProviderForPOEnum.Contact))
                        {
                            <MudAutocomplete T="GetContactDto" ShrinkLabel Label="Contacts" FullWidth="false" ShowProgressIndicator
                                             Clearable ValueChanged="@(value => SelectedContactNumber = value.Number)"
                                             ToStringFunc="dto => dto == null ? null : string.Concat(dto.VendorName, ' ', '-', ' ', dto.Name)"
                                Required SearchFunc="@SearchContactAsync" Margin="Margin.Dense" Dense>
                                <ProgressIndicatorInPopoverTemplate>
                                    <MudList T="String" ReadOnly>
                                        <MudListItem>
                                            Loading...
                                        </MudListItem>
                                    </MudList>
                                </ProgressIndicatorInPopoverTemplate>
                                <ItemTemplate Context="e">
                                    <MudStack Row Justify="Justify.SpaceBetween">
                                        <MudStack>
                                            <MudText>@e.Name</MudText>
                                            <MudText Typo="Typo.body2"><b>Number: </b>@e.VendorNumber - @e.VendorName</MudText>
                                        </MudStack>
                                        <MudStack AlignItems="AlignItems.Center" Justify="Justify.Center">
                                            <MudChip Size="Size.Small" Color="@(e.Block ? Color.Default : Color.Success)"
                                                     T="String">@(e.Block ? "Block" : "Active")</MudChip>
                                        </MudStack>
                                    </MudStack>
                                </ItemTemplate>
                            </MudAutocomplete>
                        }

                    }
                    <MudSelect T="DocNoOccurrenceEnum" @bind-Value="@DocNoOccurrence" Label="Loại" ReadOnly>
                        <MudSelectItem Value="DocNoOccurrenceEnum.Order">Nhập mua</MudSelectItem>
                        <MudSelectItem Value="DocNoOccurrenceEnum.Consigned">Ký gửi</MudSelectItem>
                    </MudSelect>
                </MudForm>

            </MudCardContent>
            <MudCardActions>
                <MudButton Disabled="@(!CanCreate())" OnClick="CheckPaymentDayByVendorNumber" Color="Color.Primary"
                           Variant="Variant.Filled">@L["Create"]</MudButton>
            </MudCardActions>
        </MudCard>
    </MudStack>

    <MudDialog @bind-Visible="@IsAlertDialogOpen" Options="@(new DialogOptions
                                                  {
                                                      MaxWidth = MaxWidth.Medium,
                                                      FullWidth = true
                                                  })">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.ReportProblem" Color="Color.Warning" Class="mr-3 mb-n1" />
            @L["There are a number of vendors who have PO available"]
        </MudText>
    </TitleContent>
    <DialogContent>
        <AlertPaymentDay Parameters="@ListCheckPaymentDayResponse"></AlertPaymentDay>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(_ => { IsAlertDialogOpen = false; })">@L["Cancel"]</MudButton>
        <MudButton OnClick="@(async () => await Create())" Color="Color.Primary">@L["Continue create"]</MudButton>
    </DialogActions>
</MudDialog>
}
