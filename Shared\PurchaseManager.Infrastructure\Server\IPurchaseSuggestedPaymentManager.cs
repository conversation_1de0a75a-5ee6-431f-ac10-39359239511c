﻿using Microsoft.AspNetCore.Http;
using PurchaseManager.Constants.PurchaseSuggestedPayment;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
namespace PurchaseManager.Infrastructure.Server;

public interface IPurchaseSuggestedPaymentManager
{
    Task<ApiResponse> HandlePurchaseSuggestedPaymentAsync(string documentNumber, HandlePurchaseSuggestedPaymentEnum handlePurchaseSuggestedPaymentEnum);
    Task<ApiResponse> CreatePurchaseSuggestedPaymentAsync(List<CreatePOFromInputDemandDto> createPOFromInputDemand);
    Task<ApiResponse> UpdatePurchaseSuggestedPaymentHeaderAsync(UpdatePurchaseSuggestedPaymentHeaderDto updatePurchaseSuggestedPaymentLineDto);
    Task<ApiResponse> UpdatePurchaseSuggestedPaymentLineAsync(UpdatePurchaseSuggestedPaymentLineDto updatePurchaseSuggestedPaymentLineDto);
    Task<ApiResponse> GetPurchaseSuggestedPaymentByVendorAsync();
    Task<ApiResponse> CreateDemandV2Async(IFormFile demandFile);

    Task<ApiResponse> CreateDemandReasonAsync(CreateDemandReasonDto createDemandReasonDto);
    Task<ApiResponse> UpdateDemandReasonAsync(UpdateDemandReasonDto updateDemandReasonDto);
    Task<ApiResponse> BlockDemandReasonAsync(string reasonCode);
    Task<ApiResponse> GetsDemandReasonAsync();
    Task<ApiResponse<List<InvalidItem>>> ValidateItemsInDemandV2(List<DemandItemInfo> listDemandV2);

}
