using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Dto.QueueGenPO;
namespace PurchaseManager.Infrastructure.Server;

public interface IQueueGenPurchaseOrderManager
{
    Task<ApiResponse> CreateQueueGenPurchaseOrder(List<DetailPoDto> detailPos);
    Task<ApiResponse> CreateQueueGenPurchaseOrderDataInput(List<CreateQueueGenPoDemandDto> detailPos);
    Task<ApiResponse> CreateQueueGenPurchaseOrderDemandV2(List<ApproveDocumentDto> documentApproved);
}
