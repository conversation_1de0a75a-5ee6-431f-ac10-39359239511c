﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

public class EmailTemplates
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int EmailTemplatesId { get; set; }
    [StringLength(255)]
    public string TemplateName { get; set; } = null!;
    [StringLength(255)]
    public string Subject { get; set; } = null!;
    [StringLength(255)]
    public string? LoginId { get; set; }
    public string Body { get; set; } = null!;
    public bool Hidden { get; set; }
    [StringLength(255)]
    public DateTime? LastDateModified { get; set; }
    [InverseProperty("EmailsNavigation")]
    public virtual ICollection<QueuedEmail> QueuedEmail { get; set; }
}
