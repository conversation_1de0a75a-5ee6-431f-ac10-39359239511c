﻿@page "/forum"
@inherits ForumPageModel
@inject IStringLocalizer<Global> L

<PageTitle>Forum</PageTitle>

<p class="my-4">This forum page uses SignalR and persists in a database.</p>

<AuthorizeView Context="AuthorizeContext">
    <NotAuthorized>
        <MudAlert Severity="Severity.Warning">
            <a href="@PurchaseManager.Constants.Settings.LoginPath">Log in</a> to post a message.
        </MudAlert>
    </NotAuthorized>
    <Authorized>
        <ForumMessageCreateForm Send="Send" />

        <MudList ReadOnly="true" T="string">
            @if (Messages == null || Messages.Count == 0)
            {
                <MudListItem T="string">
                    <MudSkeleton />
                </MudListItem>
            }
            else
            {
                @foreach (var message in Messages)
                {
                    <MudListItem T="string">
                        <ForumMessage Delete="Delete" Message="@message" />
                    </MudListItem>
                }
            }
        </MudList>
    </Authorized>
</AuthorizeView>
