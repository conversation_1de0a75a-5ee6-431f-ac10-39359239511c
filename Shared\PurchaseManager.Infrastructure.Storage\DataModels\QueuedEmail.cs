﻿using PurchaseManager.Constants;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Diagnostics.CodeAnalysis;

namespace PurchaseManager.Infrastructure.Storage.DataModels
{
    [SuppressMessage("ReSharper", "EntityFramework.ModelValidation.UnlimitedStringLength")]
    public class QueuedEmail
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }
        [Required]
        public string Email { get; set; }
        public EmailType EmailType { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime? SentOn { get; set; }
        public int EmailTemplatesId { get; set; }
        [ForeignKey("EmailTemplatesId")]
        [InverseProperty("QueuedEmail")]
        public virtual EmailTemplates EmailsNavigation { get; set; } = null!;
        [StringLength(100)]
        public string VendorNumber { get; set; }

        [StringLength(100)]
        public string PoNumber { get; set; }
    }
}
