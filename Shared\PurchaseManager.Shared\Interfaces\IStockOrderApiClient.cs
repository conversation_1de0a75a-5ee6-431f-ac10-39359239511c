﻿using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Models.StockOrder;
namespace PurchaseManager.Shared.Interfaces;

public interface IStockOrderApiClient
{
    // Legacy methods
    Task<ApiResponseDto> CreateMultipleStockOrder(CreateStockOrderDto stockOrders);
    Task<ApiResponseDto> UpdateStockOrderAsync(string number, UpdateStockOrderDto dto);
    Task<ApiResponseDto> SaveDraftStockOrdersAsync(string headerNumber);
    Task<ApiResponseDto<List<GetStockOrderDto>>> GetStockOrderByPoHeader(string poHeader);
    Task<ApiResponseDto<PagedResultDto<GetStockOrderDto>>> GetStockOrderByPoHeaderAsync(StockOrderFilter filter);
    Task<ApiResponseDto<List<SOLineGetDto>>> GetLinesForStockOrder(string purchaseOrderNumber);

    // New Header/Line methods
    Task<ApiResponseDto<PagedResultDto<GetStockOrderHeaderDto>>> GetStockOrderHeadersAsync(StockOrderFilter filter);
    Task<ApiResponseDto<GetStockOrderHeaderDto>> GetStockOrderHeaderByNumberAsync(string number);
    Task<ApiResponseDto> CreateDraftStockOrderAsync(CreateStockOrderHeaderDto createDto);
    Task<ApiResponseDto> UpdateDraftStockOrderAsync(string stockOrderNumber, SaveStockOrderDto updateDto);
    Task<ApiResponseDto> FinalizeStockOrderAsync(string stockOrderNumber, SaveStockOrderDto finalizeDto);
    Task<ApiResponseDto> CreateFinalStockOrderAsync(CreateStockOrderHeaderDto createDto);
    Task<ApiResponseDto> BatchUpdateStockOrderStatusAsync(BatchUpdateStockOrderStatusDto dto);
}
