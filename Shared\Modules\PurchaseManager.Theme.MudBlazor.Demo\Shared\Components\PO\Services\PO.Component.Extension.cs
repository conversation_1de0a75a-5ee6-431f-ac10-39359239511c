﻿using MudBlazor;
using PurchaseManager.Constants;
namespace PurchaseManager.Theme.Material.Demo.Shared.Components.PO.Services;

public static class POComponentExtension
{
    public static string GetDocNoOccurrenceText(int docNoOccurrence) => docNoOccurrence switch
    {
        (int)DocNoOccurrenceEnum.Order => "Đơn mua.",
        (int)DocNoOccurrenceEnum.Consigned => "Đơn hàng ký gửi.",
        _ => "Đơn hàng khuyến mãi."
    };

    public static string GetStatusText(int status)
    {
        var finalStatus = status switch
        {
            (int)PurchaseOrderEnum.NewCreate => "PO có thể chỉnh sửa",
            (int)PurchaseOrderEnum.Confirm => "PO đang chờ Approve",
            (int)PurchaseOrderEnum.Approve => "PO hoàn tất",
            (int)PurchaseOrderEnum.PartiallyReceived => "Kho đã nhận một phần",
            (int)PurchaseOrderEnum.Completed => "Kho đã nhận đủ",
            (int)PurchaseOrderEnum.ERPStatus => "Đơn hàng đã được SYNC sang ERP",
            (int)PurchaseOrderEnum.ReceivingStock => "PO đang nhập kho",
            _ => "PO"
        };
        return finalStatus;
    }

    public static Color GetColorByPOStatus(int status) => status switch
    {
        (int)PurchaseOrderEnum.Approve => Color.Info,
        (int)PurchaseOrderEnum.PartiallyReceived => Color.Warning,
        (int)PurchaseOrderEnum.Completed => Color.Success,
        (int)PurchaseOrderEnum.ERPStatus => Color.Primary,
        (int)PurchaseOrderEnum.ReceivingStock => Color.Default,
        _ => Color.Tertiary
    };

    public static string DisplayStatus(int status)
    {
        var finalStatus = status switch
        {
            (int)PurchaseOrderEnum.Approve => "Finished",
            (int)PurchaseOrderEnum.PartiallyReceived => "Partially",
            (int)PurchaseOrderEnum.Completed => "Received",
            (int)PurchaseOrderEnum.ERPStatus => "ERP Synced",
            (int)PurchaseOrderEnum.ReceivingStock => "Receiving",
            _ => "Opened"
        };
        return finalStatus;
    }
}
