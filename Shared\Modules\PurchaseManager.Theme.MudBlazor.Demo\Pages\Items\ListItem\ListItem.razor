﻿@inherits ListItemPage
@using PurchaseManager.Shared.Dto.Item;
@page "/items"
@attribute [Authorize]

<PageTitle>Add new Item</PageTitle>
@if (isLoading)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    <MudCard Elevation="0">
        <MudCardHeader Class="">
            <CardHeaderContent>

                <div>
                    <MudText Typo="Typo.h5">Item</MudText>
                    <MudText Typo="Typo.caption">List Item</MudText>
                </div>

            </CardHeaderContent>
            <CardHeaderActions>

                <MudButton StartIcon="@Icons.Material.Filled.Add"
                           OnClick="@(() => {navigation.NavigateTo("/item/Add");})" ButtonType="ButtonType.Submit"
                           Variant="Variant.Filled" Color="Color.Primary" Class="">
                    Add
                </MudButton>


                <MudIconButton Icon="@Icons.Material.Filled.Refresh" OnClick="@(() => Reload())" Size="Size.Medium" Class="ma-2" />
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent>

            <MudTable T="DetailItemDto" ServerData="@(new Func<TableState, CancellationToken, Task<TableData<DetailItemDto>>>(ServerReload))"
                      Striped
                      Dense
                      Bordered
                      Outlined
                      Hover
                      Class=" pa-2" 
                      Elevation="0"
                      LoadingProgressColor="Color.Info"
                      Loading="@isBusy" 
                      RowClass="" 
                      @ref="table">
                <ToolBarContent>
                    <MudSpacer />
                    <MudTextField T="string"
                                  ValueChanged="@(s=>OnSearch(s))"
                                  Placeholder="Tìm theo tên hoặc mã SKU" 
                                  @ref="SearchRef"
                                  Adornment="Adornment.Start" 
                                  AdornmentIcon="@Icons.Material.Filled.Search" 
                                  IconSize="Size.Small"
                                  Class="mt-0">
                    </MudTextField>
                    <MudIconButton Icon="@Icons.Material.Filled.Clear" Color="Color.Error" OnClick="@(async() => await SearchRef.Clear())" Size="Size.Medium" Class="ma-2" />
                    <MudSpacer />
                </ToolBarContent>
                <HeaderContent>
                    <MudTh>
                        <MudTableSortLabel SortLabel="Number" T="DetailItemDto">Number</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="Name" T="DetailItemDto">Name</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="AllowInvoiceDiscount" T="DetailItemDto">AllowInvoiceDiscount</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="AlternativeItemNo" T="DetailItemDto">AlternativeItemNo</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="ItemLocation" T="DetailItemDto">ItemLocation</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="UnitPrice" T="DetailItemDto">UnitPrice</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="Status" T="DetailItemDto">Status</MudTableSortLabel>
                    </MudTh>
                </HeaderContent>
                <RowTemplate Context="row">
                    <MudTd DataLabel="Number"><MudLink Href="@("/item/" + @row.Number)">@row.Number</MudLink></MudTd>
                    <MudTd DataLabel="AllowInvoiceDiscount">@row.Name</MudTd>
                    <MudTd DataLabel="AllowInvoiceDiscount">@row.AllowInvoiceDiscount</MudTd>
                    <MudTd DataLabel="AlternativeItemNo">@row.AlternativeItemNo </MudTd>
                    <MudTd DataLabel="ItemLocation">@row.ItemLocation </MudTd>
                    <MudTd DataLabel="UnitPrice">@row.UnitPrice </MudTd>
                    <MudTd DataLabel="Status">@row.Status </MudTd>
                </RowTemplate>
                <PagerContent>
                    <MudTablePager RowsPerPageString=@L["Rows per page"] />
                </PagerContent>
            </MudTable>
        </MudCardContent>
    </MudCard>
}
