using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

public partial class Contact
{
    [Key]
    [StringLength(100)]
    public string Number { get; set; }

    [StringLength(50)]
    public string Name { get; set; }

    [StringLength(255)]
    public string Address { get; set; }

    [StringLength(20)]
    [Unicode(false)]
    public string Phone { get; set; }

    [StringLength(13)]
    public string Tax { get; set; }

    [StringLength(10)]
    public string Email { get; set; }

    /// <summary>
    /// các lĩnh vực(vd: thuc pham chuc nang,...)
    /// </summary>
    [StringLength(10)]
    public string Tags { get; set; }

    [StringLength(100)]
    public string VendorNumber { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }
    public bool Block { get; set; }
    public bool HasAccount { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public DateTime CreatedAt { get; set; }
    [StringLength(100)]
    public string CreatedBy { get; set; }
    [StringLength(100)]
    public string? LastModifiedBy { get; set; }

    /// <summary>
    ///     Personal, Company, ...
    ///     1. Personal
    ///     2. Company
    /// </summary>
    public int Type { get; set; }

    [ForeignKey("VendorNumber")]
    [InverseProperty("Contacts")]
    public virtual Vendor VendorNumberNavigation { get; set; }
}
