﻿@using Breeze.Sharp
@using System.Globalization
@using System.Net.Http
@using System.Net.Http.Json
@using System.Security.Claims
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.AspNetCore.Http;
@using Microsoft.Extensions.Localization;
@using static Microsoft.AspNetCore.Http.StatusCodes
@using Microsoft.JSInterop
@using PurchaseManager.Constants
@using PurchaseManager.Shared
@using PurchaseManager.Shared.Dto
@using PurchaseManager.Shared.Dto.Db
@using PurchaseManager.Shared.Dto.ExternalAuth
@using PurchaseManager.Shared.Models
@using PurchaseManager.Infrastructure.AuthorizationDefinitions
@using PurchaseManager.Theme.Material
@using PurchaseManager.Theme.Material.Shared
@using PurchaseManager.Theme.Material.Pages
@using PurchaseManager.Theme.Material.Shared.Layouts
@using PurchaseManager.Theme.Material.Shared.Components
@using PurchaseManager.Theme.Material.Demo
@using PurchaseManager.Theme.Material.Demo.Shared.Components
@using PurchaseManager.Theme.Material.Demo.Shared.Components.DragNDrop
@using PurchaseManager.UI.Base.Shared.Components
@using PurchaseManager.Shared.Extensions
@using PurchaseManager.Shared.Interfaces
@using PurchaseManager.Shared.Providers
@using PurchaseManager.Shared.Services
@using PurchaseManager.Shared.Localizer
@using Blazored.FluentValidation
@using Karambolo.Common.Localization
@using MudBlazor
@using Humanizer
@using System.ComponentModel
