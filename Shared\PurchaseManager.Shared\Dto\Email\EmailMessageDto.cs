﻿namespace PurchaseManager.Shared.Dto.Email
{
    public class EmailMessageDto
    {
        public List<EmailAddressDto> ToAddresses { get; set; } = new List<EmailAddressDto>();
        public List<EmailAddressDto> FromAddresses { get; set; } = new List<EmailAddressDto>();
        public List<EmailAddressDto> BccAddresses { get; set; } = new List<EmailAddressDto>();
        public List<EmailAddressDto> CcAddresses { get; set; } = new List<EmailAddressDto>();
        public string Subject { get; set; }
        public string Body { get; set; }
        public string? VendorNumber { get; set; } = null;
        public string? PoNumber { get; set; } = null;
        public bool IsHtml { get; set; } = true;
        public int TemplateId { get; set; }
    }
}
