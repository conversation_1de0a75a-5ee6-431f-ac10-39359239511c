﻿using System.Reflection;
using System.Text;
using Finbuckle.MultiTenant;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using PurchaseManager.Infrastructure.Storage.DataInterfaces;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Storage.Configurations;
using ApiLogItem = PurchaseManager.Infrastructure.Storage.DataModels.ApiLogItem;
using Message = PurchaseManager.Infrastructure.Storage.DataModels.Message;
using UserProfile = PurchaseManager.Infrastructure.Storage.DataModels.UserProfile;

namespace PurchaseManager.Storage;

//https://trailheadtechnology.com/entity-framework-core-2-1-automate-all-that-boring-boiler-plate/
public class ApplicationDbContext : MultiTenantIdentityDbContext<ApplicationUser, ApplicationRole, Guid, IdentityUserClaim<Guid>,
    ApplicationUserRole, IdentityUserLogin<Guid>,
    IdentityRoleClaim<Guid>, IdentityUserToken<Guid>>
{
    public DbSet<TenantSetting> TenantSettings { get; set; }
    public DbSet<Person> Persons { get; set; }
    public DbSet<Company> Companies { get; set; }
    public DbSet<ApiLogItem> ApiLogs { get; set; }
    public DbSet<UserProfile> UserProfiles { get; set; }
    public DbSet<QueuedEmail> QueuedEmails { get; set; }
    public DbSet<Todo> Todos { get; set; }
    public DbSet<Message> Messages { get; set; }
    public DbSet<PurchaseOrder> PurchaseOrders { get; set; }
    private IUserSession UserSession { get; set; }

    public DbSet<DbLog> Logs { get; set; }
    public DbSet<QueueGenPurchaseOrder> QueueGenPurchaseOrders { get; set; }
    public DbSet<DemandInput> DemandInputs { get; set; }
    public DbSet<ItemColors> ItemColors { get; set; }
    public DbSet<Colors> Colors { get; set; }
    public DbSet<PurchaseSuggestedPaymentHeader> PurchaseSuggestedPaymentHeaders { get; set; }

    public DbSet<PurchaseSuggestedPaymentLine> PurchaseSuggestedPaymentLines { get; set; }

    public DbSet<InventoryControl> InventoryControls { get; set; }

    public virtual DbSet<PurchasePrice> PurchasePrices { get; set; }

    public virtual DbSet<PurchasePriceFieldChangeLog> PurchasePriceFieldChangeLogs { get; set; }

    public virtual DbSet<PurchasePriceLog> PurchasePriceLogs { get; set; }
    public DbSet<DemandReason> DemandReasons { get; set; }
    public DbSet<Employee> Employees { get; set; }
    public DbSet<StockOrder> StockOrders { get; set; }

    // New Stock Order Header/Line structure
    public DbSet<StockOrderHeader> StockOrderHeaders { get; set; }
    public DbSet<StockOrderLine> StockOrderLines { get; set; }

    #region PO
    public virtual DbSet<PurchaseOrderHeader> PurchaseOrderHeaders { get; set; }

    public virtual DbSet<PurchaseOrderLine> PurchaseOrderLines { get; set; }
    #endregion
    #region MasterData
    public virtual DbSet<Item> Items { get; set; }

    public virtual DbSet<ItemUnitOfMeasure> ItemUnitOfMeasures { get; set; }

    public virtual DbSet<UnitOfMeasure> UnitOfMeasures { get; set; }

    public virtual DbSet<SalesPrice> SalesPrices { get; set; }

    public virtual DbSet<NumberSeries> NumberSeries { get; set; }

    public virtual DbSet<NumberSeriesLine> NumberSeriesLines { get; set; }

    public virtual DbSet<EmailTemplates> EmailTemplates { get; set; }

    public virtual DbSet<AuditLog> AuditLogs { get; set; }

    public virtual DbSet<Vendor> Vendors { get; set; }
    public virtual DbSet<VendorItem> VendorItems { get; set; }

    public virtual DbSet<FileStorage> FileStorages { get; set; }

    public virtual DbSet<Contact> Contacts { get; set; }
    public virtual DbSet<SentEmail> SentEmails { get; set; }

    #endregion

    /* We define a default value for TenantInfo. This is a hack. FinBuckle does not provide any method to init TenantInfo or define a default value when seeding the database (in DatabaseInitializer, HttpContext is not yet initialized). */
    public ApplicationDbContext(TenantInfo tenantInfo, DbContextOptions<ApplicationDbContext> options, IUserSession userSession)
        : base(tenantInfo ?? TenantStoreDbContext.DefaultTenant, options)
    {
        TenantNotSetMode = TenantNotSetMode.Overwrite;
        TenantMismatchMode = TenantMismatchMode.Overwrite;
        UserSession = userSession;

        //Database.ExecuteSqlRaw("SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;");
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<ApplicationUser>(b =>
        {
            b.HasOne(a => a.Profile)
                .WithOne(up => up.ApplicationUser)
                .HasForeignKey<UserProfile>(u => u.UserId);

            b.HasMany(e => e.UserRoles)
                .WithOne(e => e.User)
                .HasForeignKey(ur => ur.UserId)
                .IsRequired();
        });

        modelBuilder.Entity<Person>(entity =>
        {
            entity.Property(e => e.Id).ValueGeneratedNever();

            //entity.HasOne(e => e.User)
            //.WithOne(d => d.Person)
            //.HasForeignKey<ApplicationUser>(d => d.PersonId);

            entity.HasOne(d => d.CreatedBy)
                .WithMany()
                .HasForeignKey(p => p.CreatedById)
                .OnDelete(DeleteBehavior.ClientCascade);

            entity.HasOne(d => d.ModifiedBy)
                .WithMany()
                .HasForeignKey(p => p.ModifiedById)
                .OnDelete(DeleteBehavior.ClientCascade);
        });

        modelBuilder.Entity<PurchaseSuggestedPaymentHeader>(entity =>
        {
            entity.HasKey(e => e.Number).HasName("PurchaseSuggestedPaymentHeader_PK");
        });
        modelBuilder.Entity<PurchaseSuggestedPaymentLine>(entity =>
        {
            entity.HasKey(e => new
            {
                e.DocumentType,
                e.DocumentNumber,
                e.LineNumber,
                e.Number,
                e.UnitOfMeasure
            }).HasName("PurchaseSuggestedPaymentLine_PK");
            entity.HasOne(d => d.DocumentNumberNavigation)
                .WithMany(p => p.PurchaseSuggestedPaymentLines)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("PurchaseSuggestedPaymentLine_PurchaseSuggestedPaymentHeader_FK");

            entity.HasOne(d => d.ItemNumberNavigation)
                .WithMany(p => p.PurchaseSuggestedPaymentLines)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_PurchaseSuggestedPaymentLine_Item");

            entity.HasOne(d => d.ReasonCodeNavigation)
                .WithMany(p => p.PurchaseSuggestedPaymentLines)
                .HasConstraintName("PurchaseSuggestedPaymentLine_DemandReason_FK");
        });

        modelBuilder.Entity<DemandReason>(entity =>
        {
            entity.HasKey(e => e.ReasonCode).HasName("DemandReason_PK");
            entity.Property(e => e.ReasonCode).UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.CreateBy).UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.ReasonName).UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.RowId).ValueGeneratedOnAdd();
            entity.Property(e => e.UpdateBy).UseCollation("Latin1_General_CI_AS");
        });

        modelBuilder.Entity<Company>().HasIndex(e => e.VatIn).IsUnique();

        modelBuilder.Entity<ApplicationRole>(b =>
        {
            b.HasMany(e => e.UserRoles)
                .WithOne(e => e.Role)
                .HasForeignKey(ur => ur.RoleId)
                .IsRequired();
        });

        modelBuilder.Entity<ApiLogItem>(b =>
        {
            b.HasOne(e => e.ApplicationUser)
                .WithMany(e => e.ApiLogItems)
                .HasForeignKey(e => e.ApplicationUserId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        modelBuilder.Entity<QueuedEmail>(b =>
        {
            b.Property(q => q.CreatedOn).HasDefaultValueSql("getdate()");
        });
        modelBuilder.Entity<SentEmail>(entity =>
        {
            entity.Property(e => e.Body).UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.Id).ValueGeneratedOnAdd();
            entity.Property(e => e.PoNumber).UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.SentBy).UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.Type).HasComment("DetailPO\r\nConfirmation\r\nGeneric\r\nPassword\r\n...");
            entity.Property(e => e.VendorNumber).UseCollation("Latin1_General_CI_AS");
        });

        modelBuilder.ShadowProperties();

        modelBuilder.Entity<TenantSetting>().ToTable("TenantSettings").HasKey(i => new
        {
            i.TenantId, i.Key
        });

        modelBuilder.Entity<Message>().ToTable("Messages");

        modelBuilder.ApplyConfiguration(new MessageConfiguration());

        modelBuilder.Entity<Contact>(entity =>
        {
            entity.Property(e => e.Email).IsFixedLength();
            entity.Property(e => e.RowId).ValueGeneratedOnAdd();
            entity.Property(e => e.Tags)
                .IsFixedLength()
                .HasComment("các lĩnh vực(vd: thuc phẩm chức nang,...)");
            entity.Property(e => e.Tax).IsFixedLength();
            entity.Property(e => e.VendorNumber).UseCollation("Latin1_General_CI_AS");

            entity.HasOne(d => d.VendorNumberNavigation).WithMany(p => p.Contacts).HasConstraintName("Contacts_Vendor_FK");
        });

        modelBuilder.Entity<VendorItem>(entity =>
        {
            entity.HasKey(e => e.RowId).HasName("VendorItems_PK");

            entity.Property(e => e.Blocked).HasComment("status of item");
            entity.Property(e => e.CreatedBy)
                .HasComment("username")
                .UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.Description).UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.ItemNumber).UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.LastUpdatedBy).UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.Name).UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.UnitOfMeasure).UseCollation("Latin1_General_CI_AS");
            entity.Property(e => e.Vat).HasComment("sample: 2, 5, 8, 10,...");
            entity.Property(e => e.VendorNumber).UseCollation("Latin1_General_CI_AS");
            entity.HasOne(d => d.VendorNumberNavigation).WithMany(p => p.VendorItems).HasConstraintName("VendorItems_Vendor_FK");

        });
        modelBuilder.Entity<Item>(i =>
        {
            i.HasMany(item => item.ItemUnitOfMeasures)
                .WithOne(sp => sp.ItemNumberNavigation)
                .HasForeignKey(sp => sp.ItemNumber);

            i.HasMany(item => item.SalesPrices)
                .WithOne(sp => sp.ItemNumberNavigation)
                .HasForeignKey(sp => sp.ItemNumber);

            i.HasMany(item => item.ItemColors)
                .WithOne(sp => sp.ItemNumberNavigation)
                .HasForeignKey(sp => sp.ItemNumber);
        });
        modelBuilder.Entity<Employee>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Employees_PK");

            entity.Property(e => e.AllowedStatistic).HasDefaultValue(1);
            entity.Property(e => e.InReport).HasDefaultValue(false);
            entity.Property(e => e.NotAccept).HasDefaultValue(false);
            entity.Property(e => e.Seniority).HasDefaultValueSql("(NULL)");
            entity.Property(e => e.SeniorityOrigin).HasDefaultValueSql("(NULL)");
            entity.Property(e => e.Status).HasComment("1:online, 2: tam nghi, 3 thai sản, 4:off");
            entity.Property(e => e.SuspendDate).HasDefaultValue(new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));
        });
        modelBuilder.Entity<PurchasePrice>(entity =>
        {
            entity.HasKey(e => e.Number).HasName("PurchasePrice_PK");

            entity.ToTable("PurchasePrice", buildAction: tb =>
            {
                tb.HasTrigger("trg_PurchasePrice_Delete");
                tb.HasTrigger("trg_PurchasePrice_FieldChange_Update");
                tb.HasTrigger("trg_PurchasePrice_Insert");
                tb.HasTrigger("trg_PurchasePrice_Update");
            });

            entity.Property(e => e.Number).HasMaxLength(100);
            entity.Property(e => e.CreateAt).HasColumnType("datetime");
            entity.Property(e => e.CreateBy).HasMaxLength(100);
            entity.Property(e => e.DiscountBySkus).HasColumnName("DiscountBySKUs");
            entity.Property(e => e.FoCpromotion)
                .IsRequired()
                .HasMaxLength(100)
                .HasColumnName("FoCPromotion");
            entity.Property(e => e.GroupProduct).HasMaxLength(100);
            entity.Property(e => e.ItemCategory).HasMaxLength(100);
            entity.Property(e => e.ItemNumber)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.LastUpdateAt).HasColumnType("datetime");
            entity.Property(e => e.LastUpdateBy).HasMaxLength(100);
            entity.Property(e => e.Pic)
                .IsRequired()
                .HasMaxLength(100)
                .HasComment("Personal in change")
                .HasColumnName("PIC");
            entity.Property(e => e.PriceAftDiscountNoVat)
                .HasColumnType("decimal(38, 2)")
                .HasColumnName("PriceAftDiscountNoVAT");
            entity.Property(e => e.PriceAftDiscountVat)
                .HasColumnType("decimal(38, 2)")
                .HasColumnName("PriceAftDiscountVAT");
            entity.Property(e => e.PriceAftVat)
                .HasColumnType("decimal(38, 2)")
                .HasColumnName("PriceAftVAT");
            entity.Property(e => e.PriceBefVat)
                .HasColumnType("decimal(38, 2)")
                .HasColumnName("PriceBefVAT");
            entity.Property(e => e.PurchasingUnit)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.Vat).HasColumnName("VAT");
            entity.Property(e => e.VendorNumber)
                .IsRequired()
                .HasMaxLength(100);

            entity.HasOne(d => d.ItemNumberNavigation).WithMany(p => p.PurchasePrices)
                .HasForeignKey(d => d.ItemNumber)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("PurchasePrice_Item_FK");

            entity.HasOne(d => d.VendorNumberNavigation).WithMany(p => p.PurchasePrices)
                .HasForeignKey(d => d.VendorNumber)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("PurchasePrice_Vendor_FK");
        });

        modelBuilder.Entity<PurchasePriceFieldChangeLog>(entity =>
        {
            entity.HasKey(e => e.LogId).HasName("PK__Purchase__5E54864820DFB96D");

            entity.ToTable("PurchasePrice_FieldChange_Log");

            entity.Property(e => e.FieldName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.LogAction)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.LogBy).HasMaxLength(100);
            entity.Property(e => e.LogDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.NewValue).HasMaxLength(100);
            entity.Property(e => e.Number)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.OldValue).HasMaxLength(100);

            entity.HasOne(d => d.NumberNavigation).WithMany(p => p.PurchasePriceFieldChangeLogs)
                .HasForeignKey(d => d.Number)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("PurchasePrice_FieldChange_Log_PurchasePrice_FK");
        });

        modelBuilder.Entity<PurchasePriceLog>(entity =>
        {
            entity.HasKey(e => e.LogId).HasName("PK__Purchase__5E5486487A44A5CD");

            entity.ToTable("PurchasePrice_Log");

            entity.Property(e => e.CreateAt).HasColumnType("datetime");
            entity.Property(e => e.CreateBy).HasMaxLength(100);
            entity.Property(e => e.DiscountBySkus).HasColumnName("DiscountBySKUs");
            entity.Property(e => e.FoCpromotion)
                .HasMaxLength(100)
                .HasColumnName("FoCPromotion");
            entity.Property(e => e.GroupProduct).HasMaxLength(100);
            entity.Property(e => e.ItemCategory).HasMaxLength(100);
            entity.Property(e => e.ItemNumber).HasMaxLength(100);
            entity.Property(e => e.LastUpdateAt).HasColumnType("datetime");
            entity.Property(e => e.LastUpdateBy).HasMaxLength(100);
            entity.Property(e => e.LogAction)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.LogBy).HasMaxLength(100);
            entity.Property(e => e.LogDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Number)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.Pic)
                .HasMaxLength(100)
                .HasColumnName("PIC");
            entity.Property(e => e.PriceAftDiscountNoVat)
                .HasMaxLength(100)
                .HasColumnName("PriceAftDiscountNoVAT");
            entity.Property(e => e.PriceAftDiscountVat)
                .HasMaxLength(100)
                .HasColumnName("PriceAftDiscountVAT");
            entity.Property(e => e.PriceAftVat)
                .HasMaxLength(100)
                .HasColumnName("PriceAftVAT");
            entity.Property(e => e.PriceBefVat)
                .HasMaxLength(100)
                .HasColumnName("PriceBefVAT");
            entity.Property(e => e.PurchasingUnit).HasMaxLength(100);
            entity.Property(e => e.Vat).HasColumnName("VAT");
            entity.Property(e => e.VendorNumber).HasMaxLength(100);
        });

        modelBuilder.Entity<StockOrder>(entity =>
        {
            entity.HasKey(e => e.Number).HasName("StockOrders_PK");

            entity.Property(e => e.Number).HasMaxLength(100);
            entity.Property(e => e.CreateAt).HasColumnType("datetime");
            entity.Property(e => e.CreateBy).HasMaxLength(100);
            entity.Property(e => e.ExpirationDate).HasColumnType("datetime");
            entity.Property(e => e.HeaderNumber)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.ItemName).HasMaxLength(100);
            entity.Property(e => e.ItemNumber).HasMaxLength(100);
            entity.Property(e => e.LotNo)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.Note).HasMaxLength(100);
            entity.Property(e => e.QuantityReceived);
            entity.Property(e => e.RowId).ValueGeneratedOnAdd();
            entity.Property(e => e.Status).HasDefaultValue(1);
            entity.Property(e => e.TotalQuantity);
            entity.Property(e => e.IsCreateSO).HasDefaultValue(false);

            entity.HasOne(d => d.HeaderNumberNavigation).WithMany(p => p.StockOrders)
                .HasForeignKey(d => d.HeaderNumber)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("StockOrders_PurchaseOrderHeader_FK");

            entity.HasOne(d => d.PurchaseOrderLine).WithMany(p => p.StockOrders)
                .HasForeignKey(d => new
                {
                    d.DocumentType, d.HeaderNumber, d.LineNumber, d.ItemNumber
                })
                .HasConstraintName("StockOrders_PurchaseOrderLine_FK");
        });
        SetGlobalQueryFilters(modelBuilder);
    }

    private void SetGlobalQueryFilters(ModelBuilder modelBuilder)
    {
        foreach (var tp in modelBuilder.Model.GetEntityTypes())
        {
            var t = tp.ClrType;

            // set Soft Delete Property
            if (typeof(ISoftDelete).IsAssignableFrom(t))
            {
                var method = _setGlobalQueryForSoftDeleteMethodInfo.MakeGenericMethod(t);
                method.Invoke(this, [
                    modelBuilder
                ]);
            }
        }
    }

    private static readonly MethodInfo _setGlobalQueryForSoftDeleteMethodInfo = typeof(ApplicationDbContext)
        .GetMethods(BindingFlags.Public | BindingFlags.Instance)
        .Single(t => t.IsGenericMethod && t.Name == "SetGlobalQueryForSoftDelete");

    public void SetGlobalQueryForSoftDelete<T>(ModelBuilder builder) where T : class, ISoftDelete
        => builder.Entity<T>().HasQueryFilter(item => !EF.Property<bool>(item, "IsDeleted"));

    public override int SaveChanges()
    {
        ChangeTracker.SetShadowProperties(UserSession);
        return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Check whether this is an api system call or a user call
        if (UserSession.UserName != null)
        {
            var modifiedEntities = ChangeTracker.Entries()
                .Where(e => e.State is EntityState.Added or EntityState.Modified)
                .ToList();
            foreach (var auditLog in modifiedEntities.Select(modifiedEntity => new AuditLog
            {
                EntityName = modifiedEntity.Entity.GetType().Name,
                Action = modifiedEntity.State.ToString(),
                Timestamp = DateTime.UtcNow,
                Changes = GetChanges(modifiedEntity),
                UserId = UserSession.UserId.ToString(),
                UserName = UserSession.UserName
            }))
            {
                AuditLogs.Add(auditLog);
            }
        }
        ChangeTracker.SetShadowProperties(UserSession);
        return await base.SaveChangesAsync(true, cancellationToken);
    }
    private static string GetChanges(EntityEntry entity)
    {
        var changes = new StringBuilder();
        foreach (var property in entity.OriginalValues.Properties)
        {
            var originalValue = entity.OriginalValues[property];
            var currentValue = entity.CurrentValues[property];
            if (!Equals(originalValue, currentValue))
            {
                changes.AppendLine($"{property.Name}: From '{originalValue}' to '{currentValue}'");
            }
        }
        return changes.ToString();
    }
}
