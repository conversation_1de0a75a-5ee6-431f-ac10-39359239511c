﻿using SourceGenerators;
namespace PurchaseManager.Shared.Models.Account;

public partial class AccountVendorFilter : QueryParameters
{
    [AutoNotify] private string _query = null!;
    [AutoNotify] private int _pageIndex;
    [AutoNotify] private bool? _allDeactivate;
    [AutoNotify] private bool? _allBlocked;
    [AutoNotify] private int _pageSize;
    [AutoNotify] private string _userName = null!;

}
