@inherits AddItemBase
@page "/Item/Add"
@attribute [Authorize]

<PageTitle>Add new Item</PageTitle>
@if (isLoading)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    <div class="mb-4">
        <EditForm EditContext="editContext" OnValidSubmit="OnAddItem">
            <FluentValidationValidator />
            <MudCard>
                <MudText Typo="Typo.h4" Align="Align.Center" Class="pt-2">Add Item</MudText>
                <MudGrid>
                    <MudItem sm="12">
                        <MudCardContent>
                            <MudTextField Label="Name" HelperText="" @bind-Value="item.Name" For="@(() => item.Name)" Required />
                            <MudSelect T="UnitOfMeasureDto" @bind-Value="@unitOfMeasureDto" Required ToStringFunc="@converter"
                                       Label="Other Unit Of Measure" AnchorOrigin="Origin.BottomCenter" Clearable>
                                @foreach (var o in listUnitOfMeasureDto)
                                {
                                    <MudSelectItem Value="@o" />
                                }
                            </MudSelect>
                            <MudTextField Label="Description" HelperText="" @bind-Value="item.Description" Required For="@(() => item.Description)" />

                            <div class="d-flex flex-row-reverse">
                                <MudButton Variant="Variant.Filled" Disabled="@(!editContext.IsModified())" Color="Color.Tertiary" OnClick="OnAddItem" Class="mt-2">
                                    Add Item
                                </MudButton>
                            </div>
                        </MudCardContent>
                    </MudItem>
                </MudGrid>
            </MudCard>
        </EditForm>
    </div>
}