﻿using System.ComponentModel.DataAnnotations;
namespace PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;

public class UpdatePurchaseSuggestedPaymentLineDto
{
    public int DocumentType { get; set; }
    public string DocumentNumber { get; set; } = null!;
    public int LineNumber { get; set; }
    public string Number { get; set; } = null!;
    public string UnitOfMeasure { get; set; } = null!;

    public string BuyFromVendorNumber { get; set; }
    public decimal Quantity { get; set; }
    [Range(0, int.MaxValue, ErrorMessage = "Request Quantity is invalid format")]
    public int RequestQuantity { get; set; }
    public int ConfirmQuantity { get; set; }
    public string Rate { get; set; } = null!;
    public string? ReasonCode { get; set; }
    public DateOnly? ExpirationDate { get; set; }
}
