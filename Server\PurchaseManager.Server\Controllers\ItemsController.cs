using System.Data.Entity;
using AutoMapper;
using Breeze.AspNetCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using NetTopologySuite.Utilities;
using Colors=PurchaseManager.Infrastructure.Storage.DataModels.Colors;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.Permissions;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Models.Item;
using PurchaseManager.Storage;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Controllers;

[Route("api/data/[action]")]
[ApiController]
[Authorize]
public class ItemsController : ControllerBase
{
    private readonly IItemManager _itemManager;
    private readonly ApplicationPersistenceManager _persistenceManager;
    private readonly IStringLocalizer<Global> L;
    private readonly IMapper _mapper;

    public ItemsController(IItemManager itemManager, IStringLocalizer<Global> l, ApplicationPersistenceManager persistenceManager, IMapper mapper)
    {
        _itemManager = itemManager;
        L = l;
        _persistenceManager = persistenceManager;
        _mapper = mapper;
    }
    [HttpGet]
    [BreezeQueryFilter]
    [Authorize(Permissions.Item.Read)]
    public IQueryable<DetailItemDto> GetItems([FromQuery] ItemFilter filter)
    {
        var queryString = HttpContext.Request.QueryString.ToString();
        var rs = _itemManager.GetAllItems(filter, queryString);
        return rs;
    }
    [HttpGet]
    [BreezeQueryFilter]
    [Authorize(Permissions.Item.Read)]
    public async Task<ApiResponse> GetItem(string itemNumber)
    {
        var rs = await _itemManager.GetItem(itemNumber);
        return rs;
    }

    [BreezeQueryFilter]
    [HttpGet]
    [Authorize(Permissions.UnitOfMeasure.Read)]
    public IQueryable<UnitOfMeasureDto> GetAllUnitOfMeasure([FromQuery] UnitFilter filter)
    {
        return _itemManager.GetAllUnitOfMeasure(filter, HttpContext.Request.QueryString.ToString());
    }
    [HttpPost]
    [ProducesResponseType(Status201Created)]
    [ProducesResponseType(Status404NotFound)]
    [ProducesResponseType(Status500InternalServerError)]
    [Authorize(Permissions.Item.Create)]
    public async Task<ApiResponse> InsertItem(CreateItemDto item)
    {
        return ModelState.IsValid ? await _itemManager.InsertItem(item) : new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }

    [HttpGet]
    [Authorize(Permissions.Item.Read)]
    public async Task<ApiResponse> SearchItem(string number, string name, CancellationToken cancellationToken = default)
    {
        var queryString = HttpContext.Request.QueryString.ToString();
        return await _itemManager.SearchItemByNumberOrName(number, name, queryString, cancellationToken);
    }

    [HttpPut("{number}")]
    [Authorize(Permissions.Item.Update)]
    public async Task<ApiResponse> UpdateItem(UpdateItemDto item, string number)
    {
        return ModelState.IsValid
            ? await _itemManager.UpdateItem(number, item)
            : new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }

    // Unit of measure
    [HttpPost]
    [Authorize(Permissions.UnitOfMeasure.Create)]
    public async Task<ApiResponse> InsertUnit(List<CreateUnitOfMeasureDto> unit)
    {
        return ModelState.IsValid ? await _itemManager.InsertUnitOfMeasure(unit) : new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }

    [HttpPut("{code}")]
    [Authorize(Permissions.UnitOfMeasure.Update)]
    public async Task<ApiResponse> UpdateUnit(UpdateUnitOfMeasureDto unit, string code)
    {
        return ModelState.IsValid ? await _itemManager.UpdateUnitOfMeasure(code, unit) : new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }

    // Sale price
    [HttpPost]
    [Authorize(Permissions.SalesPrice.Create)]
    public async Task<ApiResponse> InsertSalesPrice([FromBody] List<CreateSalesPriceDto> salesPrice)
    {
        if (salesPrice.Any(sp => sp.ItemNumber == null))
        {
            return new ApiResponse(Status400BadRequest, L["InvalidData"]);
        }
        return ModelState.IsValid
            ? await _itemManager.InsertSalesPrice(salesPrice)
            : new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }

    [HttpPut]
    [Authorize(Permissions.SalesPrice.Update)]
    public async Task<ApiResponse> UpdateSalesPrice(string itemNumber, DateTime startDate, string unitOfMeasure, UpdateSalesPriceDto salesPrice)
    {
        return ModelState.IsValid
            ? await _itemManager.UpdateSalesPrice(itemNumber, startDate, unitOfMeasure, salesPrice)
            : new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }

    // Item unit of measure
    [HttpPost]
    [Authorize(Permissions.Item.Create)]
    public async Task<ApiResponse> InsertItemUnitOfMeasure(List<CreateItemUnitOfMeasureDto> itemUnitOfMeasure)
    {
        if (itemUnitOfMeasure.Any(sp => sp.ItemNumber == null))
        {
            return new ApiResponse(Status400BadRequest, L["InvalidData"]);
        }
        return ModelState.IsValid
            ? await _itemManager.InsertItemUnitOfMeasure(itemUnitOfMeasure)
            : new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }

    [HttpPut]
    [Authorize(Permissions.Item.Update)]
    public async Task<ApiResponse> UpdateItemUnitOfMeasure(string itemNumber, string unitOfMeasureCode, UpdateItemUnitOfMeasureDto itemUnitOfMeasure)
    {
        return ModelState.IsValid
            ? await _itemManager.UpdateItemUnitOfMeasure(itemNumber, unitOfMeasureCode, itemUnitOfMeasure)
            : new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }

    [HttpGet]
    [Authorize(Permissions.Item.Read)]
    public IQueryable<ColorDto> GetColors()
    {
        var result = _persistenceManager.GetEntities<Colors>().AsNoTracking();
        return _mapper.ProjectTo<ColorDto>(result);
    }

    [HttpPost]
    [Authorize(Permissions.Item.CreateOrUpdate)]
    public async Task<ApiResponse> InsertOrUpdateColors(List<InsertOrUpdateItemColorsDto> colors)
    {
        return ModelState.IsValid
            ? await _itemManager.InsertOrUpdateColors(colors)
            : new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }
    [HttpPost]
    [Authorize(Permissions.VendorItem.Create)]
    public async Task<ApiResponse> CreateVendorItem(CreateVendorItemDto dto)
    {
        return ModelState.IsValid
            ? await _itemManager.CreateVendorItem(dto)
            : new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }
    [HttpPost]
    [Authorize(Permissions.VendorItem.Update)]
    public async Task<ApiResponse> UpdateVendorItem(UpdateVendorItemDto dto)
    {
        return ModelState.IsValid
            ? await _itemManager.UpdateVendorItem(dto)
            : new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }
    [HttpPost]
    [Authorize(Permissions.VendorItem.Update)]
    public async Task<ApiResponse<DetailVendorItemDto>> ToggleVendorItemStatus(int rowId)
    {
        return await _itemManager.ToggleVendorItemStatus(rowId);
    }
    [HttpGet]
    [BreezeQueryFilter]
    [Authorize(Permissions.VendorItem.Read)]
    public IQueryable<DetailVendorItemDto> GetAllVendorItems([FromQuery] VendorItemFilter filter)
    {
        var rs = _itemManager.GetAllVendorItems(filter);
        return rs;
    }

    [HttpGet("{itemNumber}")]
    public async Task<ApiResponse> ItemUnitOfMeasure(string itemNumber)
    {
        var data = await _itemManager.GetItemUnitOfMeasure(itemNumber);
        return data;
    }
}
