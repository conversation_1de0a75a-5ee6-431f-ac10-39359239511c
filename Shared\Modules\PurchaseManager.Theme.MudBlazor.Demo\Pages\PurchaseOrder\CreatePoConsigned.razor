@page "/po/create-consigned"
@attribute [Authorize]
<PageTitle>Create P.O </PageTitle>

<MudStack >
        <MudCard>
            <MudCardHeader>@L["Create Purchase Order Consigned"]</MudCardHeader>
            <MudCardContent>
                <MudForm Spacing="6">
                    <MudAutocomplete T="GetVendorDto" ShrinkLabel="true" Label="NCC" FullWidth="false"
                                     ShowProgressIndicator="true" @ref="AutoComplete"
                                     ToStringFunc="dto => dto == null ? null : string.Concat(dto.Number, ' ', ('-'), ' ', dto.Name)" Required="true"
                                     SearchFunc="@ItemSearch" Margin="Margin.Dense" Dense="true">
                        <ProgressIndicatorInPopoverTemplate>
                            <MudList T="string" ReadOnly>
                                <MudListItem>
                                    Loading...
                                </MudListItem>
                            </MudList>
                        </ProgressIndicatorInPopoverTemplate>
                        <ItemTemplate Context="e">
                            <MudText>@e.Name</MudText>
                            <MudText Typo="Typo.body2">@e.Number</MudText>
                        </ItemTemplate>
                    </MudAutocomplete>
                    <MudSelect T="DocNoOccurrenceEnum" @bind-Value="@DocNoOccurrence" Label="Loại" ReadOnly="true">
                        <MudSelectItem Value="DocNoOccurrenceEnum.Order">Nhập mua</MudSelectItem>
                        <MudSelectItem Value="DocNoOccurrenceEnum.Consigned">Ký gửi</MudSelectItem>
                    </MudSelect>
                </MudForm>

            </MudCardContent>
            <MudCardActions>
                <MudButton OnClick="Create" Color="Color.Primary" Variant="Variant.Filled">Create</MudButton>
            </MudCardActions>
        </MudCard>
</MudStack>