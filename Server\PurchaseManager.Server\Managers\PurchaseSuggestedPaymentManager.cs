﻿using System.Diagnostics;
using System.Globalization;
using AutoMapper;
using ExcelDataReader;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using NetTopologySuite.Utilities;
using PurchaseManager.Constants.PurchaseSuggestedPayment;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Storage;
using Serilog;
namespace PurchaseManager.Server.Managers;

public partial class PurchaseSuggestedPaymentManager : IPurchaseSuggestedPaymentManager
{
    private readonly IMapper _mapper;
    private readonly ApplicationDbContext _context;
    private readonly IStringLocalizer<Global> L;
    private readonly IAdminManager _adminManager;
    private const string Business = "DEMANDORDER";
    private const string BusinessReason = "DEMANDREASON";
    private const string Branch = "AL";

    public PurchaseSuggestedPaymentManager(IMapper mapper, ApplicationDbContext context, IAdminManager adminManager, IStringLocalizer<Global> l)
    {
        _mapper = mapper;
        _context = context;
        _adminManager = adminManager;
        L = l;
    }
    public async Task<ApiResponse> HandlePurchaseSuggestedPaymentAsync(string documentNumber, HandlePurchaseSuggestedPaymentEnum action)
    {
        if (_adminManager.GetUserLogin() == null)
            return new ApiResponse(404, L["User not found"]);
        var header = await _context.PurchaseSuggestedPaymentHeaders.FirstOrDefaultAsync(h => h.Number == documentNumber);
        if (header == null)
            return new ApiResponse(404, L["Record not found"]);
        if (header.LastUsingId != null && header.LastUsingId != _adminManager.GetUserLogin()
                                       && header.Status == (int)PurchaseSuggestedPaymentEnum.Edit)
            return new ApiResponse(404, L["Record has been opened by {0}", header.LastUsingId]);
        if (header.Status == (int)PurchaseSuggestedPaymentEnum.Approve)
            return new ApiResponse(404, L["Record has been approved"]);
        switch (action)
        {
            case HandlePurchaseSuggestedPaymentEnum.Open:
                if (header.Status is (int)PurchaseSuggestedPaymentEnum.New
                    or (int)PurchaseSuggestedPaymentEnum.Save
                    or (int)PurchaseSuggestedPaymentEnum.Edit)
                {
                    header.Status = (int)PurchaseSuggestedPaymentEnum.Edit;
                    header.LastOpenTime = DateTime.Now;
                    header.LastUsingId = _adminManager.GetUserLogin();
                    await _context.SaveChangesAsync();
                    return new ApiResponse(200, L["Opened"]);
                }
                break;
            case HandlePurchaseSuggestedPaymentEnum.Close:
                if (header.Status == (int)PurchaseSuggestedPaymentEnum.Edit)
                {
                    header.Status = (int)PurchaseSuggestedPaymentEnum.Save;
                    header.LastCloseTime = DateTime.Now;
                    header.LastCloseId = _adminManager.GetUserLogin();
                    await _context.SaveChangesAsync();
                    return new ApiResponse(200, L["Closed"]);
                }
                break;
            case HandlePurchaseSuggestedPaymentEnum.Approve:
                if (header.Status is (int)PurchaseSuggestedPaymentEnum.Save or (int)PurchaseSuggestedPaymentEnum.New)
                {
                    header.Status = (int)PurchaseSuggestedPaymentEnum.Approve;
                    await _context.SaveChangesAsync();
                    return new ApiResponse(200, L["Approved"]);
                }
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(action), action, null);
        }
        return new ApiResponse(404, L["Invalid action or status"]);
    }

    public async Task<ApiResponse> CreatePurchaseSuggestedPaymentAsync(List<CreatePOFromInputDemandDto> createPOFromInputDemand)
    {
        var s = new Stopwatch();
        s.Start();
        await using var transaction = await _context.Database.BeginTransactionAsync();
        Console.WriteLine("Start at " + DateTime.Now);
        try
        {
            // var listLine = new List<PurchaseSuggestedPaymentLine>();
            // var listHeaders = new List<PurchaseSuggestedPaymentHeader>();

            // Load items once into a dictionary for fast lookup
            var itemNumbers = createPOFromInputDemand
                .SelectMany(d => d.ListDemand.Select(i => i.ItemNo.Trim()))
                .Distinct()
                .ToList();

            var listItems = await _context.Items
                .Where(i => itemNumbers.Contains(i.Number.Trim()))
                .AsNoTracking()
                .ToDictionaryAsync(keySelector: i => i.Number.Trim(), elementSelector: i => new
                {
                    i.VatproductPostingGroup, i.VendorNumber
                });

            Console.WriteLine("start create header - time(ms): " + s.ElapsedMilliseconds);

            await foreach (var demand in createPOFromInputDemand.ToAsyncEnumerable())
            {
                var documentNumber = await _adminManager.CreateNumberSeries(Business, Branch);
                Console.WriteLine("id : " + documentNumber);

                var header = CreateHeader(documentNumber, demand.StoreId);
                await _context.PurchaseSuggestedPaymentHeaders.AddAsync(header);
                // Save header
                await _context.SaveChangesAsync();

                var batchLines = new List<PurchaseSuggestedPaymentLine>();

                const int batchSize = 5000;
                foreach (var (item, index) in demand.ListDemand.Select((v, i) => (v, i)))
                {
                    if (!listItems.TryGetValue(item.ItemNo.Trim(), out var result))
                    {
                        Console.WriteLine("Item not found: " + item.ItemNo);
                        continue;
                    }

                    // Tạo Line
                    var line = CreateLines(documentNumber, index, item, demand.StoreId, result.VatproductPostingGroup,
                    result.VendorNumber);

                    batchLines.Add(line);

                    // Ghi ngay khi đạt kích thước batch
                    if (batchLines.Count < batchSize)
                    {
                        continue;
                    }
                    await _context.PurchaseSuggestedPaymentLines.AddRangeAsync(batchLines);
                    await _context.SaveChangesAsync();
                    batchLines.Clear();// Giải phóng bộ nhớ
                }
            }

            // Console.WriteLine("Add header done " + listHeaders.Count + " - time (ms): " + s.ElapsedMilliseconds);

            // Console.WriteLine("Add lines done " + listLine.Count + " - time (ms): " + s.ElapsedMilliseconds);
            await transaction.CommitAsync();

            // Console.WriteLine("Complete - time (ms): " + s.ElapsedMilliseconds + " | item count: " + listLine.Count);
            s.Stop();

            return new ApiResponse(201, "Created");
        }
        catch (Exception e)
        {
            Log.Error(e.GetBaseException().Message);
            Console.WriteLine("Error at " + DateTime.Now + ": " + e);
            await transaction.RollbackAsync();
            return new ApiResponse(500, L["Error create record"]);
        }
    }

    public async Task<ApiResponse> UpdatePurchaseSuggestedPaymentHeaderAsync(UpdatePurchaseSuggestedPaymentHeaderDto updatePurchaseSuggestedPaymentHeaderDto)
    {
        try
        {
            var header = await _context.PurchaseSuggestedPaymentHeaders.SingleOrDefaultAsync(u => u.Number == updatePurchaseSuggestedPaymentHeaderDto.Number);
            if (header == null)
                return new ApiResponse(404, L["Record not found"]);
            switch (header.Status)
            {
                case (int)PurchaseSuggestedPaymentEnum.Approve:
                    return new ApiResponse(404, L["Record has been approved"]);
                case (int)PurchaseSuggestedPaymentEnum.New:
                    return new ApiResponse(404, L["Record has been closed"]);
                case (int)PurchaseSuggestedPaymentEnum.Edit:
                    if (header.LastUsingId != _adminManager.GetUserLogin())
                        return new ApiResponse(404, L["Record has been opened by another user"]);
                    break;
            }
            //_mapper.Map(updatePurchaseSuggestedPaymentHeaderDto, header);
            header.LoginId = _adminManager.GetUserLogin();
            header.Number = updatePurchaseSuggestedPaymentHeaderDto.Number;
            header.OrderDate = updatePurchaseSuggestedPaymentHeaderDto.OrderDate;
            header.PostingDate = updatePurchaseSuggestedPaymentHeaderDto.PostingDate;
            header.ExpectedReceiptDate = updatePurchaseSuggestedPaymentHeaderDto.ExpectedReceiptDate;
            header.DueDate = updatePurchaseSuggestedPaymentHeaderDto.DueDate;
            header.DocumentDate = updatePurchaseSuggestedPaymentHeaderDto.DocumentDate;
            header.PaymentMethodCode = updatePurchaseSuggestedPaymentHeaderDto.PaymentMethodCode;
            header.PurchaserCode = updatePurchaseSuggestedPaymentHeaderDto.PurchaserCode;
            header.PayToVendorNumber = updatePurchaseSuggestedPaymentHeaderDto.PayToVendorNumber;
            header.PayToName = updatePurchaseSuggestedPaymentHeaderDto.PayToName;
            header.PayToAddress = updatePurchaseSuggestedPaymentHeaderDto.PayToAddress;
            header.BuyFromVendorNumber = updatePurchaseSuggestedPaymentHeaderDto.BuyFromVendorNumber;
            await _context.SaveChangesAsync();
            return new ApiResponse(200, L["Updated successfully"]);
        }
        catch (Exception e)
        {
            Log.Error(e.GetBaseException().Message);
            return new ApiResponse(500, L["Error update record"]);
        }
    }

    public async Task<ApiResponse<List<InvalidItem>>> ValidateItemsInDemandV2(List<DemandItemInfo> listDemandV2)
    {
        var listInValidItems = new List<InvalidItem>();
        try
        {
            var startAt = DateTime.Now.ToString(CultureInfo.InvariantCulture);
            var listItems = await _context.Items.AsNoTracking().ToListAsync();
            var validatedCount = 0;
            Parallel.For(0, listDemandV2.Count, body: i =>
            {
                var demand = listDemandV2[i];
                var isValidUOM = listItems
                    .Where(x => x.Number == demand.ItemNo)
                    .Any(x => string.IsNullOrEmpty(x.BaseUnitOfMeasure) || string.Equals(x.BaseUnitOfMeasure, demand.UnitOfMeasure,
                    StringComparison.CurrentCultureIgnoreCase));

                if (!isValidUOM)
                {
                    listInValidItems.Add(new InvalidItem
                    {
                        ItemNumber = demand.ItemNo, Msg = $"Unit '{demand.UnitOfMeasure}' of item does not exist"
                    });
                }
                var foundItem = listItems
                    .Where(u => u.Number == demand.ItemNo)
                    .Select(u => new
                    {
                        u.VatproductPostingGroup,
                        u.VendorNumber
                    })
                    .FirstOrDefault();

                if (foundItem == null)
                {
                    listInValidItems.Add(new InvalidItem() { ItemNumber = demand.ItemNo, Msg = "Item not found" });
                }
                var currentCount = Interlocked.Increment(ref validatedCount);

                // Log số lượng đã validate sau mỗi 1000 item
                if (currentCount % 1000 == 0)
                {
                    Console.WriteLine($"Validated {currentCount} items so far...");
                }
            });
            Console.WriteLine("start validate at: " + startAt);

            Console.WriteLine("end validate at: " + DateTime.Now.ToString(CultureInfo.InvariantCulture));

            return new ApiResponse(200, L["Validate successfully"], listInValidItems);
        }
        catch (Exception e)
        {
            Log.Error(e.GetBaseException().Message);
            return new ApiResponse(500, L["Error update record"]);
        }
    }
    public async Task<ApiResponse> UpdatePurchaseSuggestedPaymentLineAsync(UpdatePurchaseSuggestedPaymentLineDto updateLineDto)
    {
        try
        {
            var line = await _context.PurchaseSuggestedPaymentLines
                .SingleOrDefaultAsync(u =>
                    u.DocumentNumber == updateLineDto.DocumentNumber &&
                    u.LineNumber == updateLineDto.LineNumber &&
                    u.DocumentType == updateLineDto.DocumentType &&
                    u.Number == updateLineDto.Number &&
                    u.UnitOfMeasure == updateLineDto.UnitOfMeasure);
            if (line == null)
                return new ApiResponse(404, L["Record not found"]);
            if (updateLineDto.ReasonCode != null)
            {
                var reason = await _context.DemandReasons.SingleOrDefaultAsync(u => u.ReasonCode == updateLineDto.ReasonCode);
                if (reason == null)
                    return new ApiResponse(404, L["Reason code not found"]);
            }
            var header = await _context.PurchaseSuggestedPaymentHeaders.SingleOrDefaultAsync(u => u.Number == updateLineDto.DocumentNumber);
            switch (header.Status)
            {
                case (int)PurchaseSuggestedPaymentEnum.Approve:
                    return new ApiResponse(404, L["Record has been approved"]);
                case (int)PurchaseSuggestedPaymentEnum.Save:
                    return new ApiResponse(404, L["Record has been saved"]);
                case (int)PurchaseSuggestedPaymentEnum.New:
                    return new ApiResponse(404, L["Record has been closed"]);
                case (int)PurchaseSuggestedPaymentEnum.Edit:
                    if (header.LastUsingId != _adminManager.GetUserLogin())
                        return new ApiResponse(404, L["Record has been opened by another user"]);
                    break;
            }
            line.BuyFromVendorNumber = updateLineDto.BuyFromVendorNumber;
            line.Quantity = updateLineDto.Quantity;
            line.ExpirationDate = updateLineDto.ExpirationDate;
            line.RequestQuantity = updateLineDto.RequestQuantity;
            line.ConfirmQuantity = updateLineDto.ConfirmQuantity;
            line.ReasonCode = updateLineDto.ReasonCode;
            line.Rate = updateLineDto.Rate;

            // _mapper.Map(updatePurchaseSuggestedPaymentLineDto, line);
            await _context.SaveChangesAsync();
            return new ApiResponse(200, L["Updated successfully"]);
        }
        catch (Exception e)
        {
            Log.Error(e.GetBaseException().Message);
            return new ApiResponse(500, L["Error update record"]);
        }
    }
    public Task<ApiResponse> GetPurchaseSuggestedPaymentByVendorAsync()
    {
        throw new NotImplementedException();
    }
    public async Task<ApiResponse> CreateDemandV2Async(IFormFile demandFile)
    {
        try
        {
            const long maxAllowedSize = 30 * 1024 * 1024;// 30MB
            if (demandFile == null || demandFile.Length == 0 || demandFile.Length > maxAllowedSize)
            {
                return new ApiResponse(400, "Invalid file format or file size too large.");
            }

            using var memoryStream = new MemoryStream();
            await demandFile.CopyToAsync(memoryStream);
            memoryStream.Position = 0;

            using var reader = ExcelReaderFactory.CreateReader(memoryStream);
            var result = reader.AsDataSet(new ExcelDataSetConfiguration
            {
                ConfigureDataTable = _ => new ExcelDataTableConfiguration
                {
                    UseHeaderRow = true
                }
            });

            var table = result.Tables[0];
            if (table.Rows.Count == 0)
            {
                return new ApiResponse(400, L["Empty file content."]);
            }

            var listDtPo = new List<DemandInfo>();
            for (var i = 0; i < table.Rows.Count; i++)
            {
                var row = table.Rows[i];
                if (row.ItemArray.Length < 5 || row.ItemArray[0] == null)
                {
                    return new ApiResponse(400, L["Invalid file format."]);
                }

                listDtPo.Add(new DemandInfo
                {
                    Index = i,
                    ItemNo = GetIntValue(row, 0),
                    StoreId = GetStringValue(row, 1),
                    QtyDemand = GetIntValue(row, 2),
                    UnitOfMeasure = GetStringValue(row, 3),
                    Tags = GetStringValue(row, 4),
                    QtyRequest = 0,
                    Quantity = GetIntValue(row, 2)
                });
            }

            Parallel.ForEach(listDtPo, body: currentItem =>
            {
                currentItem.UnitOfMeasure = currentItem.UnitOfMeasure.ToUpper();
                if (string.IsNullOrWhiteSpace(currentItem.Tags))
                {
                    return;
                }
                var listTags = currentItem.Tags.Split(',')
                    .Where(tag => !string.IsNullOrWhiteSpace(tag))
                    .Select(tag => tag.Trim())
                    .Distinct(StringComparer.OrdinalIgnoreCase)
                    .OrderBy(tag => tag.ToLower())
                    .ToList();

                currentItem.Tags = string.Join(",", listTags);
            });

            var duplicateItems = listDtPo
                .GroupBy(i => new
                {
                    i.ItemNo, i.StoreId, i.UnitOfMeasure
                })
                .Where(g => g.Count() > 1)
                .SelectMany(g => g)
                .ToList();

            if (duplicateItems.Count != 0)
            {
                return new ApiResponse(400, L["Duplicate items found."]);
            }

            var data = listDtPo
                .GroupBy(d => d.StoreId)
                .Select(g => new CreatePOFromInputDemandDto
                {
                    StoreId = g.Key, ListDemand = _mapper.Map<List<DemandItemInfo>>(g.ToList())
                })
                .ToList();
            return await CreatePurchaseSuggestedPaymentV2Async(data);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            return new ApiResponse(500, L["Error reading file."]);
        }
    }

    public async Task<ApiResponse> CreateDemandReasonAsync(CreateDemandReasonDto createDemandReasonDto)
    {
        try
        {
            var reasonCode = await _adminManager.CreateNumberSeries(BusinessReason, Branch);
            var demandEntity = _mapper.Map<DemandReason>(createDemandReasonDto);
            demandEntity.ReasonCode = reasonCode;
            demandEntity.CreateBy = _adminManager.GetUserLogin();
            demandEntity.CreateAt = DateTime.Now;
            demandEntity.Block = false;
            demandEntity.Status = (int)DemandReasonEnum.Create;
            await _context.DemandReasons.AddAsync(demandEntity);
            await _context.SaveChangesAsync();
            return new ApiResponse(201, "Created");
        }
        catch (Exception e)
        {
            Log.Error(e.GetBaseException().Message);
            return new ApiResponse(500, L["Error create record"]);
        }
    }
    public async Task<ApiResponse> UpdateDemandReasonAsync(UpdateDemandReasonDto updateDemandReasonDto)
    {
        try
        {
            var demandEntity = await _context.DemandReasons.SingleOrDefaultAsync(u => u.ReasonCode == updateDemandReasonDto.ReasonCode);
            demandEntity.ReasonName = updateDemandReasonDto.ReasonName;
            demandEntity.UpdateBy = _adminManager.GetUserLogin();
            demandEntity.UpdateAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return new ApiResponse(200, L["Updated successfully"]);
        }
        catch (Exception e)
        {
            Log.Error(e.GetBaseException().Message);
            return new ApiResponse(500, L["Error update record"]);
        }
    }
    public async Task<ApiResponse> BlockDemandReasonAsync(string reasonCode)
    {
        var demandEntity = _context.DemandReasons.SingleOrDefault(u => u.ReasonCode == reasonCode);
        if (demandEntity == null)
        {
            return new ApiResponse(404, L["Record not found"]);
        }
        demandEntity.Block = !demandEntity.Block;
        await _context.SaveChangesAsync();
        return new ApiResponse(200, L["Updated successfully"]);
    }
    public async Task<ApiResponse> GetsDemandReasonAsync()
    {
        var demandEntities = await _context.DemandReasons.ToListAsync();
        var demandDtos = _mapper.Map<List<DetailDemandReasonDto>>(demandEntities);
        return new ApiResponse(200, "", demandDtos);
    }
}
