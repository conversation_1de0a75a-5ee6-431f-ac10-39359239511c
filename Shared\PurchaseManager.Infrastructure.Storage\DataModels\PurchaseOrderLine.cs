﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

[PrimaryKey("DocumentType", "DocumentNumber", "LineNumber", "ItemNumber")]
[Table("PurchaseOrderLine")]
public class PurchaseOrderLine
{
    [Key]
    public int DocumentType { get; set; }

    [Key]
    [StringLength(100)]
    public string DocumentNumber { get; set; } = null!;

    [Key]
    public int LineNumber { get; set; }

    public int Type { get; set; }

    [Key]
    [StringLength(100)]
    public string ItemNumber { get; set; } = null!;

    [StringLength(100)]
    public string LocationCode { get; set; } = null!;

    // [Column("Posting Group")]
    [StringLength(100)]
    public string PostingGroup { get; set; } = null!;

    // [Column("Expected Receipt Date", TypeName = "datetime")]
    public DateTime ExpectedReceiptDate { get; set; }

    [StringLength(200)]
    public string Description { get; set; } = null!;

    // [Column("Description 2")]
    [StringLength(100)]
    public string Description2 { get; set; } = null!;

    // [Column("Unit of Measure")]
    [StringLength(100)]
    public string UnitOfMeasure { get; set; } = null!;

    [Column(TypeName = "decimal(28, 10)")]
    public decimal Quantity { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal OutstandingQuantity { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal QuantityToInvoice { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal QuantityToReceive { get; set; }

    [Column("VAT", TypeName = "decimal(28, 10)")]
    public decimal Vat { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal LineDiscountPercent { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal LineDiscountAmount { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal Amount { get; set; }

    [Column("AmountIncludingVAT", TypeName = "decimal(28, 10)")]
    public decimal AmountIncludingVat { get; set; }

    public int AllowInvoiceDiscount { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal QuantityReceived { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal QuantityInvoiced { get; set; }

    [StringLength(500)]
    public string ReceiptNumber { get; set; } = null!;

    public int ReceiptLineNumber { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal ProfitPercent { get; set; }

    [Column("VATAmount", TypeName = "decimal(28, 10)")]
    public decimal VatAmount { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal UnitCost { get; set; }

    public int Status { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal LineAmount { get; set; }

    [Column("VATBaseAmount", TypeName = "decimal(28, 10)")]
    public decimal VatBaseAmount { get; set; }

    [StringLength(100)]
    public string UnitOfMeasureCode { get; set; } = null!;

    [StringLength(100)]
    public string CrossReferenceNumber { get; set; } = null!;

    [StringLength(100)]
    public string? LotNo { get; set; }

    public DateOnly? ExpirationDate { get; set; }

    [StringLength(50)]
    public string? CategoryCode { get; set; }

    [StringLength(250)]
    public string? ItemName { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }

    [Column(TypeName = "decimal(18, 0)")]
    public decimal? UnitPrice { get; set; }
    public decimal LastUnitCost { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal QtyPerUnitOfMeasure { get; set; }

    [ForeignKey("DocumentNumber")]
    [InverseProperty("PurchaseOrderLines")]
    public PurchaseOrderHeader DocumentNumberNavigation { get; set; } = null!;

    public ICollection<StockOrder> StockOrders { get; set; } = new List<StockOrder>();

}
