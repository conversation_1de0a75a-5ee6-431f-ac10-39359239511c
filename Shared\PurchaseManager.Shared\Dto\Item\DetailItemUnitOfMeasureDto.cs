﻿namespace PurchaseManager.Shared.Dto.Item;

public class DetailItemUnitOfMeasureDto
{
    public string Code { get; set; }
    public string ItemNumber { get; set; }
    public decimal QuantityPerUnitOfMeasure { get; set; }
    public string? Description { get; set; }
    public int Type { get; set; }
    public int Status { get; set; }
    // No remove ? from DateTime
    public DateTime? LastDateModified { get; set; }
    public string? LoginId { get; set; }
    public int Block { get; set; }
}
