using System.ComponentModel.DataAnnotations;

namespace PurchaseManager.Shared.Dto.Item;

public class CreateVendorItemDto
{
    [Required] public string Name { get; set; }
    public string ItemNumber { get; set; }
    [Required] public string UnitOfMeasure { get; set; }
    [Required]
    // [Range(100, double.MaxValue, ErrorMessage = "Price must be greater than 100")]
    public decimal Price { get; set; }
    [Required]
    public int Vat { get; set; }
    [Required] public string VendorNumber { get; set; }
}
