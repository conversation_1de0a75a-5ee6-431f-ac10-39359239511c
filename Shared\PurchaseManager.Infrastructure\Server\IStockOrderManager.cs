﻿using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Models.StockOrder;
namespace PurchaseManager.Infrastructure.Server;

public interface IStockOrderManager
{
    // Legacy methods for backward compatibility
    Task<ApiResponse> GetReceiveLotsByPoHeaderAsync(StockOrderFilter poHeader);
    Task<ApiResponse> CreateReceiveLotsAsync(CreateStockOrderDto stockOrders);
    Task<ApiResponse> UpdateReceiveLotsAsync(string number, UpdateStockOrderDto stockOrders);
    Task<ApiResponse> GetLinesStockOrderAsync(string code);
    Task<ApiResponse> SaveDraftStockOrdersAsync(string headerNumber);

    // New Header/Line methods
    Task<ApiResponse> GetStockOrderHeadersAsync(StockOrderFilter filter);
    Task<ApiResponse> GetStockOrderHeaderByNumberAsync(string number);
    Task<ApiResponse> CreateDraftStockOrderAsync(CreateStockOrderHeaderDto createDto);
    Task<ApiResponse> UpdateDraftStockOrderAsync(string stockOrderNumber, SaveStockOrderDto updateDto);
    Task<ApiResponse> FinalizeStockOrderAsync(string stockOrderNumber, SaveStockOrderDto finalizeDto);
    Task<ApiResponse> CreateFinalStockOrderAsync(CreateStockOrderHeaderDto createDto);
    Task<ApiResponse> BatchUpdateStockOrderStatusAsync(BatchUpdateStockOrderStatusDto dto);
}
