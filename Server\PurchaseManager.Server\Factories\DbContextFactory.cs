﻿using PurchaseManager.Storage;
namespace PurchaseManager.Server.Factories;

public class DbContextFactory : IDbContextFactory
{
    private readonly IServiceProvider _serviceProvider;

    public DbContextFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public ApplicationDbContext CreateDbContext()
    {
        return _serviceProvider.CreateScope().ServiceProvider.GetRequiredService<ApplicationDbContext>();
    }
}
