@page "/vendors/{vendorNumber}/contacts/{contactNumber}/detail"
@page "/vendors/{vendorNumber}/contact/add"
@attribute [Authorize]

@if (isLoading)
{
    <MudCard>
        <MudCardHeader>
            <CardHeaderContent>
                <MudSkeleton Animation="Animation.Wave" Width="30%" Height="50px;" />
            </CardHeaderContent>
        </MudCardHeader>
        <MudCardContent>
            <MudSkeleton Animation="Animation.Wave" Height="40px;" />
            <MudSkeleton Animation="Animation.Wave" Height="40px;" />
            <MudSkeleton Animation="Animation.Wave" Height="40px;" />
            <MudSkeleton Animation="Animation.Wave" Height="40px;" />
            <MudSkeleton Animation="Animation.Wave" Height="40px;" />
            <MudSkeleton Animation="Animation.Wave" Height="40px;" />
            <MudSkeleton Animation="Animation.Wave" Height="40px;" />
            <MudSkeleton Animation="Animation.Wave" Height="40px;" />
            <MudSkeleton Animation="Animation.Wave" Height="40px;" />
            <MudSkeleton Animation="Animation.Wave" Height="40px;" />
        </MudCardContent>
    </MudCard>
}
else
{
    <EditForm @ref="@formRef" id="contactForm" Model="@currentContact" OnValidSubmit="@SubmitContactAsync">
        <FluentValidationValidator />
        <MudValidationSummary />
        <MudCard Elevation="2" Class="pa-3">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudStack Row>
                        <MudIconButton Color="Color.Default" Icon="@Icons.Material.Filled.ArrowBack"
                            OnClick="@OnNavigateToListVendor" Variant="Variant.Text" />
                        <MudStack Justify="Justify.Center">
                            <MudText Typo="Typo.h6">
                                @if (isAdding)
                                {
                                    <span>
                                        Add new contact - @if(!string.IsNullOrEmpty(currentVendor.Name))
                                        {
                                            <em>
                                                @currentVendor.Name
                                            </em>
                                        }
                                    </span>
                                }
                                else
                                {
                                    <span>
                                        Edit contact <em>@ContactNumber - @currentContact.VendorName</em>
                                    </span>
                                }
                            </MudText>
                        </MudStack>
                    </MudStack>
                </CardHeaderContent>
                <CardHeaderActions>
                    @if (!currentContact.HasAccount && !isAdding)
                    {
                        <MudButton Variant="Variant.Filled" Color="Color.Success" OnClick="@OnCreateNewAccountForContactAsync"
                            Class="mx-3">@L["CreateAccount"]
                        </MudButton>
                    }
                    @if (isAdding)
                    {
                        <MudButton Variant="Variant.Text" Color="Color.Error" OnClick="@OnResetForm" Class="mx-3">
                            @L["Reset"]
                        </MudButton>
                    }
                    <MudButton ButtonType="ButtonType.Submit" form="contactForm" Variant="Variant.Filled"
                        Color="@(isAdding? Color.Success: Color.Primary)">
                        @L[(isAdding ? "Create" : "Update")]
                    </MudButton>
                </CardHeaderActions>
            </MudCardHeader>
            <MudCardContent>
                <MudTextField @bind-Value="@currentContact.Name" Label="@L["Name"]"
                    AdornmentIcon="@Icons.Material.Filled.Person" Adornment="Adornment.Start" FullWidth Required
                    RequiredError="@L["Required"]" />
                <MudTextField @bind-Value="@currentContact.Email" Label="@L["Email"]"
                    AdornmentIcon="@Icons.Material.Filled.Email" Adornment="Adornment.Start" FullWidth Required
                    RequiredError="@L["Required"]" />
                <MudSelect @bind-Value="@currentContact.Type" T="Int32" Variant="Variant.Text" Label="@L["Type"]">
                    @foreach (var state in listTypeOfContact)
                    {
                        <MudSelectItem Value="@state.Id">@state.DisplayValue</MudSelectItem>
                    }
                </MudSelect>
                <MudTextField @bind-Value="@currentContact.Tax"
                    Required="@(currentContact.Type == (int)ContactEnum.Company)" RequiredError="@L["Required"]"
                    Label="@L["Tax"]" AdornmentIcon="@Icons.Material.Filled.Menu" Adornment="Adornment.Start" FullWidth />
                <MudTextField @bind-Value="@currentContact.Tags" Label="@L["Tags"]"
                    HelperText="@L["Using `,` to separate. Sample: TPCN, AB, BC"]"
                    AdornmentIcon="@Icons.Material.Filled.Tag" Adornment="Adornment.Start" FullWidth
                    RequiredError="@L["Required"]" />
                <MudTextField @bind-Value="@currentContact.Phone" ReadOnly="@(currentContact.HasAccount)"
                    Label="@L["Phone"]" AdornmentIcon="@Icons.Material.Filled.Phone" Adornment="Adornment.Start" FullWidth
                    Required RequiredError="@L["Required"]" />
                <MudTextField @bind-Value="@currentContact.Address" Label="@L["Address"]"
                    AdornmentIcon="@Icons.Material.Outlined.MapsHomeWork" Adornment="Adornment.Start" FullWidth
                    RequiredError="@L["Required"]" />
            </MudCardContent>
        </MudCard>
    </EditForm>
}