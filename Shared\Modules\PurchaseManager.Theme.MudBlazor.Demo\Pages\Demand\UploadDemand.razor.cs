﻿using System.Data;
using System.Text;
using ExcelDataReader;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Localization;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto.QueueGenPO;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.PO;
namespace PurchaseManager.Theme.Material.Demo.Pages.Demand;

public partial class UploadDemand : ComponentBase
{

    [Parameter]
    public string Label { get; set; } = "Upload ";
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    protected List<CheckPaymentDayResponse> ListCheckPaymentDayResponse { get; set; } = [];
    protected bool IsAlertDialogOpen { get; set; }
    protected bool IsFileRead { get; set; }
    protected bool IsFileLoading { get; set; }
    [Inject]
    protected IStringLocalizer<Global> L { get; set; }
    protected bool IsCreatePO { get; set; }
    [Inject]
    protected IApiClient ApiClient { get; set; }
    [Inject]
    private IPurchaseOrderApiClient PurchaseOrderApiClient { get; set; }
    private List<CreateQueueGenPoDemandDto> DetailPoDtos { get; set; }
    private readonly List<CreateQueueGenPoDemandDto> _duplicateItems = [];
    private readonly List<CreateQueueGenPoDemandDto> _vatNotFound = [];
    private bool IsVatErrorDialogOpen { get; set; }
    private string VatErrorMessage { get; set; } = string.Empty;
    private string DuplicateErrorMessage { get; set; } = string.Empty;
    private string VatExceptionErrorMessage { get; set; } = string.Empty;
    private bool HasUploadError => _duplicateItems.Count != 0 || _vatNotFound.Count != 0;
    protected override void OnInitialized()
    {
        DetailPoDtos = [];
        base.OnInitialized();
    }
    protected async Task UploadFiles(IBrowserFile file)
    {
        IsFileLoading = true;
        if (file == null)
        {
            return;
        }
        if (!Path.GetExtension(file.Name).Equals(".xlsx", StringComparison.CurrentCultureIgnoreCase)
            && !Path.GetExtension(file.Name).Equals(".xls", StringComparison.CurrentCultureIgnoreCase))
            ViewNotifier.Show("Only Excel file", ViewNotifierType.Warning, "Operation Failed");
        else
        {
            DetailPoDtos = [];
            try
            {
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                var listDtPo = new List<CreateQueueGenPoDemandDto>();
                var stream = new MemoryStream();

                await file.OpenReadStream().CopyToAsync(stream);
                stream.Position = 0;// Đặt lại vị trí của stream về đầu

                var reader = ExcelReaderFactory.CreateReader(stream);

                var result = reader.AsDataSet(new ExcelDataSetConfiguration
                {
                    ConfigureDataTable = _ => new ExcelDataTableConfiguration
                    {
                        UseHeaderRow = true// Giả sử rằng file có hàng tiêu đề
                    }
                });
                var table = result.Tables[0];// Lấy bảng đầu tiên
                foreach (DataRow row in table.Rows)
                {
                    if (ReferenceEquals(row.ItemArray[0], null))
                    {
                        ViewNotifier.Show("File không đúng định dạng. Vui lòng kiểm tra lại", ViewNotifierType.Error);
                        return;
                    }
                    var docTypeInFile = row.ItemArray[5]?.ToString();// null: order, consigned: consigned, promotional: promotional
                    var docType = DocNoOccurrenceEnum.Order;
                    if (docTypeInFile != null && docTypeInFile == DocNoOccurrenceEnum.Consigned.GetDisplayName())
                    {
                        docType = DocNoOccurrenceEnum.Consigned;
                    }
                    else if (docTypeInFile != null && docTypeInFile == DocNoOccurrenceEnum.Promotional.GetDisplayName())
                    {
                        docType = DocNoOccurrenceEnum.Promotional;
                    }
                    var newPODetail = new CreateQueueGenPoDemandDto
                    {
                        VendorNumber = row.ItemArray[0]?.ToString(),
                        BrandCode = row.ItemArray[1]?.ToString(),
                        ItemNumber = row.ItemArray[2]?.ToString(),
                        Quantity = int.Parse(row.ItemArray[3]?.ToString() ?? "0"),
                        PurchaseUnitOfMeasure = row.ItemArray[4]?.ToString(),
                        DocumentType = (int)docType,
                        PriceB4VAT =
                            decimal.Parse((string.IsNullOrEmpty(row.ItemArray[6]?.ToString()) ? "0" : row.ItemArray[6].ToString()) ??
                                          "0"),
                        VAT = decimal.Parse(
    (string.IsNullOrEmpty(row.ItemArray[7]?.ToString()) ? "0" : row.ItemArray[7].ToString()) ?? "0"),
                        Description = row.ItemArray[8]?.ToString()
                    };
                    if (listDtPo.Any(x
                            => x.ItemNumber == newPODetail.ItemNumber && x.VendorNumber == newPODetail.VendorNumber &&
                               x.PurchaseUnitOfMeasure == newPODetail.PurchaseUnitOfMeasure &&
                               x.DocumentType == newPODetail.DocumentType))
                    {
                        // add item to a duplicate Items list to show an error
                        _duplicateItems.Add(newPODetail);
                    }

                    if (newPODetail.VAT != 0m && newPODetail.VAT != 5m && newPODetail.VAT != 8m && newPODetail.VAT != 10m)
                    {
                        _vatNotFound.Add(newPODetail);
                    }

                    listDtPo.Add(newPODetail);
                }
                DetailPoDtos = new List<CreateQueueGenPoDemandDto>(listDtPo);

                //check duplicate item
                DuplicateErrorMessage = string.Empty;
                VatExceptionErrorMessage = string.Empty;
                if (_duplicateItems.Count != 0)
                {
                    var sb = new StringBuilder();
                    var groupByVendor = _duplicateItems.GroupBy(x => x.VendorNumber);
                    foreach (var group in groupByVendor)
                    {
                        sb.AppendLine($"Vendor number: {group.Key}");
                        foreach (var item in group)
                        {
                            sb.AppendLine($"  - SKU: {item.ItemNumber}");
                        }
                    }
                    DuplicateErrorMessage = sb.ToString();
                }
                if (_vatNotFound.Count != 0)
                {
                    var sb = new StringBuilder();
                    var groupByVendor = _vatNotFound.GroupBy(x => x.VendorNumber);
                    foreach (var group in groupByVendor)
                    {
                        sb.AppendLine($"Vendor number: {group.Key}");
                        foreach (var item in group)
                        {
                            sb.AppendLine($"  - SKU: {item.ItemNumber}, VAT: {item.VAT}");
                        }
                    }
                    VatExceptionErrorMessage = sb.ToString();
                }
                if (!string.IsNullOrWhiteSpace(DuplicateErrorMessage) || !string.IsNullOrWhiteSpace(VatExceptionErrorMessage))
                {
                    IsVatErrorDialogOpen = true;
                }

                StateHasChanged();
                IsFileRead = true;
            }
            catch (Exception ex)
            {
                ViewNotifier.Show(ex.Message, ViewNotifierType.Error);
                IsFileLoading = false;
            }
        }
        IsFileLoading = false;
    }
    protected void Clear()
    {
        DetailPoDtos.Clear();
        _duplicateItems.Clear();
        _vatNotFound.Clear();
        IsFileRead = false;
    }

    public async Task CheckPaymentDayByVendorNumber()
    {
        try
        {
            var req = DetailPoDtos
                .Select(d => d.VendorNumber.ToString())
                .Distinct()
                .ToList();
            var resp = await PurchaseOrderApiClient.CheckPaymentDayByVendorNumberAsync(req);

            if (resp.Result.Count > 0)
            {
                ListCheckPaymentDayResponse = resp.Result;
                IsAlertDialogOpen = true;
            }
            else
            {
                await CreatePO();
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    protected async Task CreatePO()
    {
        IsCreatePO = true;
        IsAlertDialogOpen = false;
        try
        {
            var resp = await PurchaseOrderApiClient.CreateDemand(DetailPoDtos);
            if (!resp.IsSuccessStatusCode)
            {
                ViewNotifier.Show(resp.Message, ViewNotifierType.Error, L["Operation failed"]);
            }
            else
            {
                ViewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation successful"]);
                Clear();
            }

        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error, L["Operation failed"]);
        }
        finally
        {
            IsCreatePO = false;

        }
    }
}
