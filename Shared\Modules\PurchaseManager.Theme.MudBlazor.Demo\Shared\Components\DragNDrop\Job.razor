﻿@using PurchaseManager.Shared.Dto.Sample
<li class="draggable" draggable="true" title="@JobDto.Description" @ondragstart="@(() => HandleDragStart(JobDto))">
    <p class="description">@JobDto.Description</p>
    <p class="last-updated"><small>Last Updated</small> @JobDto.LastUpdated.ToString("HH:mm.ss tt")</p>
</li>

@code {
    [CascadingParameter] JobsContainer Container { get; set; }
    [Parameter] public JobDto JobDto { get; set; }

    private void HandleDragStart(JobDto selectedJob)
    {
        Container.Payload = selectedJob;
    }
}