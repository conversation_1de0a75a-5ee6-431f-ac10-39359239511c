using Microsoft.AspNetCore.Components;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.Email;
using PurchaseManager.UI.Base.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder;
public partial class PreviewEmailToSendInPODetailBase : BaseComponent
{
    [SupplyParameterFromQuery] public string PONumber { get; set; }
    [SupplyParameterFromQuery] public string OrderDate { get; set; }
    [SupplyParameterFromQuery] public string VendorName { get; set; }
    [SupplyParameterFromQuery] public string ConfirmDate { get; set; }
    [SupplyParameterFromQuery] public string VendorNumber { get; set; }
    [SupplyParameterFromQuery] public string PostDescription { get; set; }
    protected EmailInfoInPODetailDto emailInfoInPODetailDto { get; set; }
    protected bool isLoading { get; set; }
    protected bool isProcessing { get; set; }
    protected string emailBody { get; set; }
    protected bool isValidParams { get; set; }
    protected override async Task OnParametersSetAsync()
    {
        isLoading = true;
        isValidParams = PONumber is not null && !string.IsNullOrEmpty(PONumber)
                        && OrderDate is not null && !string.IsNullOrEmpty(PONumber)
                        && VendorName is not null && !string.IsNullOrEmpty(PONumber)
                        && ConfirmDate is not null && !string.IsNullOrEmpty(PONumber)
                        && VendorNumber is not null && !string.IsNullOrEmpty(PONumber);
        if (isValidParams) AssignParamsToModel();
        await LoadEmailTemplate();
        await base.OnParametersSetAsync();
        isLoading = false;
    }
    protected void AssignParamsToModel()
    {
        emailInfoInPODetailDto = new EmailInfoInPODetailDto
        {
            PONumber = PONumber,
            OrderDate = OrderDate,
            VendorName = VendorName,
            ConfirmDate = ConfirmDate,
            VendorNumber = VendorNumber,
            PostDescription = PostDescription,
        };
    }
    protected async Task LoadEmailTemplate()
    {
        try
        {
            EmailTemplateFilter emailFilter = new EmailTemplateFilter
            {
                TemplateName = "PO Detail"
            };
            var resp = await apiClient.GetTemplates(emailFilter);
            if (!resp.Results.Any())
            {
                viewNotifier.Show("Cannot load email template. Try again!", ViewNotifierType.Error, L["Operation Failed"]);
            }
            else
            {
                var respData = resp.Results.FirstOrDefault();
                emailBody = respData.Body
                                .Replace("{PONumber}", PONumber)
                                .Replace("{OrderDate}", OrderDate)
                                .Replace("{VendorName}", VendorName)
                                .Replace("{ConfirmDate}", ConfirmDate)
                                .Replace("{VendorNumber}", VendorNumber)
                                .Replace("{PostDescription}", PostDescription);
                emailInfoInPODetailDto.Body = emailBody;
                emailInfoInPODetailDto.Subject = respData.Subject;
                emailInfoInPODetailDto.TemplateName = respData.TemplateName;
                emailInfoInPODetailDto.EmailTemplatesId = respData.EmailTemplatesId;
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    protected async Task SendEmail()
    {
        try
        {
            isProcessing = true;
            var resp = await apiClient.SendEmailDetailPOToVendor(emailInfoInPODetailDto);
            isProcessing = false;
            if (!resp.IsSuccessStatusCode)
            {
                viewNotifier.Show(resp.Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
            else
                viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation Successfully"]);
        }
        catch (Exception ex)
        {
            isProcessing = false;
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
}