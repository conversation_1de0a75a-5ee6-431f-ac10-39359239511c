@page "/admin/contacts"
@layout AdminLayout
@inherits ContactsBase
@if (isLoading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate />
}
else
{
    <div class="mt-4">
        <MudCard Elevation="0" Class="pa-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudStack Row Justify="Justify.SpaceBetween">
                        <MudStack>
                            <MudText Typo="Typo.h4">@L["Contact"]</MudText>
                            <MudText Typo="Typo.caption">@L["List"]</MudText>
                        </MudStack>
                        <MudPaper Elevation="0">
                            <MudButton StartIcon="@Icons.Material.Filled.Add"
                                OnClick="@(e => { isUpdate = false; editDialogOpen = true; })" Variant="Variant.Filled"
                                Color="Color.Success">@L["New Contact"]</MudButton>
                        </MudPaper>
                    </MudStack>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                <MudTable Items="listGetContactDto" Striped="true" Bordered="true" Dense="true" Hover="true"
                    Elevation="1" T="PurchaseManager.Shared.Dto.Contact.GetContactDto">
                    <ToolBarContent>
                        <MudAutocomplete T="PurchaseManager.Shared.Models.GetVendorDto" ShrinkLabel="true"
                                Label="Tìm theo nhà cung cấp" ShowProgressIndicator="true"
                                @ref="vendorAutoRef"
                                ValueChanged="@(async _ => await  OnSearchContactByVendorName(_))"
                                ToStringFunc="dto => dto == null ? null : string.Concat(dto.Number, ' ', ('-'), ' ', dto.Name)"
                                AdornmentIcon="@Icons.Material.Filled.Clear" AdornmentColor="Color.Error"
                                OnAdornmentClick="@(async _ => {await OnReload(); await vendorAutoRef.ResetAsync();})"
                                ResetValueOnEmptyText SearchFunc="@ItemSearch" Margin="Margin.Dense"
                                Dense="true">
                        </MudAutocomplete>
                        <MudSpacer/>
                        <MudTextField @bind-Value="@contactFilter.Name" @onkeyup="@OnKeyUp" Label=@L["Name"]
                            AdornmentIcon="@Icons.Material.Filled.Person" Adornment="Adornment.End" ></MudTextField>
                            <MudSpacer/>
                        <MudTextField @bind-Value="@contactFilter.Phone" @onkeyup="@OnKeyUp"  Label=@L["Phone"]
                            AdornmentIcon="@Icons.Material.Filled.Phone" Adornment="Adornment.End" ></MudTextField>
                            <MudSpacer/>
                        <MudTextField @bind-Value="@contactFilter.Address" @onkeyup="@OnKeyUp"  Label=@L["Address"]
                            AdornmentIcon="@Icons.Material.Outlined.MapsHomeWork" Adornment="Adornment.End" >
                        </MudTextField>
                        <MudSpacer/>
                        <MudIconButton OnClick="@OnSearchContactByField"
                        Icon="@Icons.Material.Filled.Search" Color="Color.Primary" Size="Size.Medium" />

                        <MudIconButton OnClick="@OnReload" Icon="@Icons.Material.Filled.Refresh" Color="Color.Primary" Size="Size.Medium" />
                    </ToolBarContent>
                    <HeaderContent>
                        <MudTh>@L["Contact Name"]</MudTh>
                        <MudTh>@L["Address"]</MudTh>
                        <MudTh>@L["Email"]</MudTh>
                        <MudTh>@L["Phone"]</MudTh>
                        <MudTh>@L["Tags"]</MudTh>
                        <MudTh>@L["Tax"]</MudTh>
                        <MudTh>@L["Status"]</MudTh>
                        <MudTh>@L["Actions"]</MudTh>
                    </HeaderContent>
                    <RowTemplate Context="Contact">
                        <MudTd>
                            <MudText Inline="false"><b>@Contact.Name</b></MudText>
                        <small><i>@Contact.VendorName</i></small>
                        </MudTd>
                        <MudTd>
                            <div>@Contact.Address</div>
                        </MudTd>
                        <MudTd>
                            <div>@Contact.Email</div>
                        </MudTd>
                        <MudTd>
                            <div>@Contact.Phone</div>
                        </MudTd>
                        <MudTd>
                            <div>
                                @if (@RenderTags(@Contact.Tags).Count() > 0)
                                {
                                    @foreach (var x in RenderTags(@Contact.Tags))
                                    {
                                        <MudChip Size="Size.Small" T="string" Color="Color.Primary"> @x </MudChip>
                                    }
                                }
                            </div>
                        </MudTd>
                        <MudTd>
                            <div>@Contact.Tax</div>
                        </MudTd>
                        <MudTd>
                            @if (@Contact.Block){
                                <MudIcon Icon="@Icons.Material.Outlined.Lock" Color="Color.Error" />
                            }
                            else
                            {
                                <MudIcon Icon="@Icons.Material.Outlined.LockOpen" Color="Color.Primary" />
                            }
                        </MudTd>
                        <MudTd>
                            @if (@Contact.Block){
                            <MudIconButton OnClick="@(async _ => await OnToggleContactStatus(Contact.Number))"
                                    Icon="@Icons.Material.Outlined.LockReset" Color="Color.Primary" Size="Size.Small" />
                            }
                            else
                            {
                            <MudIconButton OnClick="@(async _ => await OnToggleContactStatus(Contact.Number))"
                                    Icon="@Icons.Material.Outlined.Block" Color="Color.Error" Size="Size.Small" />
                            }
                            <MudIconButton
                                OnClick="@(_ => { isUpdate = true; currentContact = Contact; editDialogOpen = true;  })"
                                Icon="@Icons.Material.Filled.Edit" Color="Color.Primary" Size="Size.Small" />
                        </MudTd>
                    </RowTemplate>
                    <PagerContent>
                        <MudTablePager RowsPerPageString=@L["Rows per page"] />
                    </PagerContent>
                    <NoRecordsContent>
                        No Data
                    </NoRecordsContent>
                </MudTable>
            </MudCardContent>
        </MudCard>
    </div>
}
<MudDialog @bind-Visible="@editDialogOpen"
    Options="new DialogOptions() { FullWidth = true, MaxWidth = MaxWidth.Medium, CloseButton = true, }">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-3 mb-n1" />
            @if (isUpdate)
            {
                @L["Edit {0}", currentContact.Name + " -  " + currentContact.VendorName]
            }
            else
            {
                @L["Add new Contact"]
            }
        </MudText>
    </TitleContent>
    <DialogContent>
        <EditForm id="updateContactForm" Model="@currentContact" OnValidSubmit="@SubmitContactAsync">
            <FluentValidationValidator />
            <MudValidationSummary />
            @if (!isUpdate)
            {
                <MudAutocomplete T="PurchaseManager.Shared.Models.GetVendorDto" ShrinkLabel="true"
                    Label="Tìm theo nhà cung cấp" ShowProgressIndicator="true" ValueChanged="@OnSearchByVendorName"
                    ToStringFunc="dto => dto == null ? null : string.Concat(dto.Number, ' ', ('-'), ' ', dto.Name)"
                    Required="true" ResetValueOnEmptyText SearchFunc="@ItemSearch" Margin="Margin.Dense" Dense="true">
                </MudAutocomplete>
            }
            <MudTextField @bind-Value="@currentContact.Name" Label=@L["Name"]
                AdornmentIcon="@Icons.Material.Filled.Person" Adornment="Adornment.End" FullWidth="true" Required="true"
                RequiredError=@L["Required"]></MudTextField>
            <MudTextField @bind-Value="@currentContact.Email" Label=@L["Email"]
                AdornmentIcon="@Icons.Material.Filled.Email" Adornment="Adornment.End" FullWidth="true" Required="true"
                RequiredError=@L["Required"]></MudTextField>
            <MudTextField @bind-Value="@currentContact.Tax" Label=@L["Tax"] AdornmentIcon="@Icons.Material.Filled.Menu"
                Adornment="Adornment.End" FullWidth="true" RequiredError=@L["Required"]></MudTextField>
            <MudTextField @bind-Value="@currentContact.Tags" Label=@L["Tags"] AdornmentIcon="@Icons.Material.Filled.Tag"
                Adornment="Adornment.End" FullWidth="true" RequiredError=@L["Required"]></MudTextField>
            <MudTextField @bind-Value="@currentContact.Phone" Label=@L["Phone"]
                AdornmentIcon="@Icons.Material.Filled.Phone" Adornment="Adornment.End" FullWidth="true" Required="true"
                RequiredError=@L["Required"]></MudTextField>
            <MudTextField @bind-Value="@currentContact.Address" Label=@L["Address"]
                AdornmentIcon="@Icons.Material.Outlined.MapsHomeWork" Adornment="Adornment.End" FullWidth="true"
                RequiredError=@L["Required"]></MudTextField>
        </EditForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(_ => editDialogOpen = false)">@L["Cancel"]</MudButton>
        <MudButton ButtonType="ButtonType.Submit" form="updateContactForm" Variant="Variant.Filled"
            Color="Color.Primary">
            @if (isUpdate)
            {
                @L["Update"]
            }
            else
            {
                @L["Create"]
            }
        </MudButton>
    </DialogActions>
</MudDialog>
