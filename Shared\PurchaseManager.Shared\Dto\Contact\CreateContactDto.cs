using System.ComponentModel.DataAnnotations;

namespace PurchaseManager.Shared.Dto.Contact;
public class CreateContactDto
{
    [Required(ErrorMessage = "Tên là bắt buộc.")]
    [StringLength(50, ErrorMessage = "Tên không được dài quá 50 ký tự.")]
    public string Name { get; set; } = null!;

    [StringLength(255, ErrorMessage = "Địa chỉ không được dài quá 255 ký tự.")]
    public string? Address { get; set; } = null!;

    [Required(ErrorMessage = "Số điện thoại là bắt buộc.")]
    [RegularExpression(@"^(\+84|0)\d{9,10}$", ErrorMessage = "Định dạng số điện thoại không hợp lệ.")]
    [StringLength(20)]
    public string Phone { get; set; } = null!;

    // [Required(ErrorMessage = "Mã số thuế là bắt buộc.")]
    // [RegularExpression(@"^\d{10}(\d{3})?$", ErrorMessage = "Định dạng mã số thuế không hợp lệ.")]
    public string? Tax { get; set; } = null!;

    [Required(ErrorMessage = "Email là bắt buộc.")]
    [EmailAddress(ErrorMessage = "Định dạng địa chỉ email không hợp lệ.")]
    public string Email { get; set; } = null!;

    [StringLength(10, ErrorMessage = "Tags không được dài quá 10 ký tự.")]
    public string? Tags { get; set; } = null!;

    public string VendorNumber { get; set; } = null!;
    public int Type { get; set; }// personal, company
}
