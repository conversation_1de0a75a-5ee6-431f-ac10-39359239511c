FROM mcr.microsoft.com/dotnet/aspnet:5.0.13-buster-slim AS base
WORKDIR /
EXPOSE 443
EXPOSE 80

FROM mcr.microsoft.com/dotnet/sdk:5.0.402-buster-slim AS build
WORKDIR /
COPY . .
RUN dotnet restore "src/Server/PurchaseManager.Server/PurchaseManager.Server.csproj"
WORKDIR "/src/Server/PurchaseManager.Server"
RUN dotnet build "PurchaseManager.Server.csproj" -c Release -o /app/build --no-restore

FROM build AS publish
RUN dotnet publish "PurchaseManager.Server.csproj" -c Release -o /app/publish
RUN dotnet dev-certs https --clean
RUN dotnet dev-certs https -ep /app/publish/aspnetapp.pfx -p Admin123
