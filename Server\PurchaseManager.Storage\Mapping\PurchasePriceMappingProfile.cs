﻿using AutoMapper;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Helpers;
namespace PurchaseManager.Storage.Mapping;

public class PurchasePriceMappingProfile : Profile
{
    public PurchasePriceMappingProfile()
    {
        CreateMap<CreatePurchasePriceDto, PurchasePrice>()
            .ForMember(destinationMember: x => x.PriceBefVat,
            memberOptions: opt
                => opt.MapFrom(src => src.Price))
            .ForMember(destinationMember: x => x.PriceAftVat,
            memberOptions: opt
                => opt.MapFrom(src => PurchasePriceCalculator.CalcPriceAftVat(src.Price, src.VAT)))
            .ForMember(destinationMember: x => x.PriceAftDiscountNoVat,
            memberOptions: opt
                => opt.MapFrom(src => PurchasePriceCalculator.CalcPriceAftDiscountNoVat(src.Price, src.DiscountBySkus)))
            .ForMember(destinationMember: x => x.PriceAftDiscountVat,
            memberOptions: opt => opt.MapFrom(src
                => PurchasePriceCalculator.CalcPriceAftDiscountVat(src.Price, src.VAT, src.DiscountBySkus)))
            .ForMember(destinationMember: dest => dest.RowId, memberOptions: opt => opt.Ignore())
            .ReverseMap();

        CreateMap<UpdatePurchasePriceDto, PurchasePrice>()
            .ForMember(destinationMember: x => x.PriceBefVat,
            memberOptions: opt
                => opt.MapFrom(src => src.Price))
            .ForMember(destinationMember: x => x.PriceAftVat,
            memberOptions: opt
                => opt.MapFrom(src => PurchasePriceCalculator.CalcPriceAftVat(src.Price, src.VAT)))
            .ForMember(destinationMember: x => x.PriceAftDiscountNoVat,
            memberOptions: opt
                => opt.MapFrom(src => PurchasePriceCalculator.CalcPriceAftDiscountNoVat(src.Price, src.DiscountBySkus)))
            .ForMember(destinationMember: x => x.PriceAftDiscountVat,
            memberOptions: opt => opt.MapFrom(src
                => PurchasePriceCalculator.CalcPriceAftDiscountVat(src.Price, src.VAT, src.DiscountBySkus)))
            .ReverseMap();

        CreateMap<PurchasePrice, GetPurchasePriceDto>()
            .ForMember(destinationMember: x => x.VendorName,
            memberOptions: opt
                => opt.MapFrom(src => src.VendorNumberNavigation.Name))
            .ForMember(destinationMember: x => x.ItemName,
            memberOptions: opt
                => opt.MapFrom(src => src.ItemNumberNavigation.Name))
            .ReverseMap();
    }
}
