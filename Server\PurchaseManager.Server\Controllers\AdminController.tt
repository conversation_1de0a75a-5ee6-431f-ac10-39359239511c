﻿<#@ template debug="false" hostspecific="false" language="C#" #>
<#@ assembly name="System.Core" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Text" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ output extension=".cs" #>
<#
var entities = new [] { ("Role", "name"), ("Tenant", "id") };
var actions = new [] { ("Create", "HttpPost"), ("Update", "HttpPut"), ("Delete", "HttpDelete") };
#>
//Autogenerated from AdminController.tt
using AutoMapper;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.Permissions;
using PurchaseManager.Shared.Dto.Admin;
using Finbuckle.MultiTenant;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NSwag.Annotations;
using System.Threading.Tasks;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Controllers
{
    /// <summary>
    /// This controller is the entry API for the platform administration.
    /// </summary>
    [OpenApiIgnore]
    [Route("api/[controller]")]
    [ApiController]
    public class AdminController : ControllerBase
    {
        private readonly IMapper _autoMapper;
        private readonly IAdminManager _adminManager;

        public AdminController(IMapper autoMapper, IAdminManager adminManager)
        {
            _autoMapper = autoMapper;
            _adminManager = adminManager;
        }

        [HttpGet("Tenant")]
        public ApiResponse Get()
            => new ApiResponse(Status200OK, string.Empty,  _autoMapper.Map<TenantDto>(HttpContext.GetMultiTenantContext<TenantInfo>().TenantInfo));

        [HttpGet("Users")]
        [Authorize(Permissions.User.Read)]
        public async Task<ApiResponse> GetUsers([FromQuery] int pageSize = 10, [FromQuery] int pageNumber = 0)
            => await _adminManager.GetUsers(pageSize, pageNumber);

        [HttpGet("Permissions")]
        [Authorize]
        public ApiResponse GetPermissions()
            => _adminManager.GetPermissions();
<# foreach(var entity in entities)
{#>

        #region <#=entity.Item1#>s
        [HttpGet("<#=entity.Item1#>s")]
        [Authorize(Permissions.<#=entity.Item1#>.Read)]
        public async Task<ApiResponse> Get<#=entity.Item1#>s([FromQuery] int pageSize = 10, [FromQuery] int pageNumber = 0)
            => await _adminManager.Get<#=entity.Item1#>sAsync(pageSize, pageNumber);

        [HttpGet("<#=entity.Item1#>/{<#=entity.Item2#>}")]
        [Authorize]
        public async Task<ApiResponse> Get<#=entity.Item1#>Async(string <#=entity.Item2#>)
            => await _adminManager.Get<#=entity.Item1#>Async(<#=entity.Item2#>);

<# foreach(var action in actions)
{#>
        
<# if(action.Item1=="Delete")
{#>
        [<#=action.Item2#>("<#=entity.Item1#>/{<#=entity.Item2#>}")]
        [Authorize(Permissions.<#=entity.Item1#>.<#=action.Item1#>)]
        public async Task<ApiResponse> <#=action.Item1#><#=entity.Item1#>Async(string <#=entity.Item2#>)
            => await _adminManager.<#=action.Item1#><#=entity.Item1#>Async(<#=entity.Item2#>);
<#} else {#>
        [<#=action.Item2#>("<#=entity.Item1#>")]
        [Authorize(Permissions.<#=entity.Item1#>.<#=action.Item1#>)]
        public async Task<ApiResponse> <#=action.Item1#><#=entity.Item1#>Async([FromBody] <#=entity.Item1#>Dto <#=$"{Char.ToLowerInvariant(entity.Item1[0])}{entity.Item1.Substring(1)}"#>Dto)
            => await _adminManager.<#=action.Item1#><#=entity.Item1#>Async(<#=$"{Char.ToLowerInvariant(entity.Item1[0])}{entity.Item1.Substring(1)}"#>Dto);

<#}}#>
        #endregion
<#}#>
    }
}
