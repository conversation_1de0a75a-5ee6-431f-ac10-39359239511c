﻿@using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment
@inherits DemandBase
@page "/po/demand/v2"
@attribute [Authorize]

<MudText Typo="Typo.h4" Class="my-4" Align="Align.Center">@L["Upload Demand v2"]</MudText>

@if (!string.IsNullOrEmpty(GlobalMsg))
{
    <MudAlert Severity="Severity.Warning" ContentAlignment="HorizontalAlignment.Left">
        <MudStack Row Justify="Justify.SpaceBetween">
            <MudText>
                @GlobalMsg
            </MudText>
        </MudStack>
    </MudAlert>
    <MudFileUpload T="IBrowserFile" Class="mt-4" Accept=".xlsx" FilesChanged="UploadFiles">
        <ActivatorContent>
            <MudButton Style="width: 100%" Variant="Variant.Filled" Color="Color.Primary"
                StartIcon="@Icons.Material.Filled.CloudUpload">
                Upload
            </MudButton>
        </ActivatorContent>
    </MudFileUpload>
}
@if (!IsFileRead)
{
    @if (!IsFileLoading)
    {
        <MudAlert Severity="Severity.Info" ContentAlignment="HorizontalAlignment.Left">
            <MudStack Row Spacing="5" Justify="Justify.SpaceAround" AlignItems="AlignItems.Stretch">
                <MudText Class="mud-underline">@L["Upload Demand V2 to create PO"]</MudText>
                <MudText>
                    @L["Download sample file "]
                    <MudLink Underline="Underline.Always" Href="/files/Demand-V2.xlsx">
                        @L["here"]
                    </MudLink>
                </MudText>
            </MudStack>
        </MudAlert>
        <MudFileUpload T="IBrowserFile" Class="mt-4" Accept=".xlsx" FilesChanged="UploadFiles">
            <ActivatorContent>
                <MudButton Style="width: 100%" Variant="Variant.Filled" Color="Color.Primary"
                    StartIcon="@Icons.Material.Filled.CloudUpload">
                    Upload
                </MudButton>
            </ActivatorContent>
        </MudFileUpload>
    }
}
else
{
    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
        <MudButton Variant="Variant.Filled" Color="Color.Error" OnClick="Clear" Disabled="@IsProcessing"
            StartIcon="@Icons.Material.Filled.Clear">
            Clear
        </MudButton>
        @if (!ListDemandDto.Any(x => x.QtyDemand == 0))
        {
            <MudButton Variant="Variant.Filled" Color="Color.Primary"
                OnClick="@(async _ => await CheckPaymentDayByVendorNumber())"
                       Disabled="@(IsCreatePO || DuplicateItems.Any() || ListInvalidItem.Any() || !string.IsNullOrEmpty(GlobalMsg))"
                StartIcon="@Icons.Material.Filled.Add">
                @L["Create POs"]
            </MudButton>
            @* @if (isCreatePO)
    {
    <MudButton Variant="Variant.Filled" Color="Color.Primary" StartIcon="@Icons.Material.Filled.Cancel">
    Cancel
    </MudButton>
    } *@
        }
        @if (ListDemandDto.Any(x => x.QtyDemand == 0))
        {
            <MudAlert Severity="Severity.Warning" ContentAlignment="HorizontalAlignment.Left">
                @L["Duplicate data. Please check again"]
            </MudAlert>
        }
    </MudStack>
}
@if (IsProcessing)
{
    <MudStack class="my-4">
        <MudOverlay @bind-Visible="IsProcessing" LightBackground ZIndex="9999">
            <MudText Typo="Typo.h6">
                @if (IsFileLoading)
                {
                    @L["File reading and validating..."]
                }
                @if (IsCreatePO)
                {
                    @L["Creating PO. Pleaes wait"]
                }
            </MudText>
            <MudProgressLinear Color="Color.Info" Indeterminate="true" />
        </MudOverlay>
    </MudStack>


}
@if (ListDemandDto.Any() && string.IsNullOrEmpty(GlobalMsg))
{
    <MudStack Class="my-2">
        <MudTable Hover Breakpoint="Breakpoint.Sm" Height="680px" FixedHeader="true" Items="@ListDemandDto"
                  GroupBy="@GroupDefinitionUploadItems"
                  GroupHeaderStyle="background-color:var(--mud-palette-background-gray)">
            <ColGroup>
                @if (GroupDefinitionUploadItems.Expandable)
                {
                    <col style="width: 60px;" />
                }
                <col style="width: 60px;" />
                <col />
                <col />
            </ColGroup>
            <HeaderContent>
                <MudTh Style="width: 400px;">ItemNo</MudTh>
                <MudTh>Quantity of Demand</MudTh>
                <MudTh>Unit Of Measure</MudTh>
                <MudTh>Tags</MudTh>
            </HeaderContent>
            <GroupHeaderTemplate>
                <MudTh Class="mud-table-cell-custom-group" colspan="5">
                    <MudText Align="Align.Left">@($"{context.GroupName}: {context.Key}")</MudText>
                </MudTh>
            </GroupHeaderTemplate>
            <RowTemplate>
                <MudTd DataLabel="ItemNo">
                    <MudText Class="pl-4">
                        @context.ItemNo
                        @if (@IsItemDuplicate(context.Index, context.ItemNo, context.StoreId))
                        {
                            <MudTooltip Text="Duplicate data. Please check it again" Placement="Placement.Right">
                                <MudIconButton Class="ml-1" Color="Color.Warning" Size="Size.Small"
                                    Icon="@Icons.Material.Filled.WarningAmber" />
                            </MudTooltip>
                        }
                        @{
                            var foundItem = ListInvalidItem.Where(x => x.ItemNumber == context.ItemNo.ToString()
                            ).FirstOrDefault();
                            @if (foundItem is not null)
                            {
                                <MudText>@foundItem.Msg</MudText>
                            }
                        }
                    </MudText>
                </MudTd>
                <MudTd DataLabel="QtyDemand">@context.QtyDemand</MudTd>
                <MudTd DataLabel="ItemNo">
                    <MudStack Row Justify="Justify.SpaceBetween"> <span>@context.UnitOfMeasure </span>
                        @if (@IsItemDuplicate(context.Index, context.ItemNo, context.StoreId))
                        {
                            <MudIconButton Color="Color.Error" OnClick="@(() => RemoveDuplicateItem(context.Index))"
                                Icon="@Icons.Material.Filled.Delete" Size="Size.Small">
                            </MudIconButton>
                        }
                    </MudStack>
                </MudTd>
                <MudTd DataLabel="QtyDemand">
                    @if (context.Tags.Length > 0)
                    {
                        @foreach (var tag in @context.Tags.Split(",").ToList())
                        {
                            <MudChip Color="Color.Primary" Variant="Variant.Text" Text="@tag" T="string" />
                        }
                    }
                </MudTd>
            </RowTemplate>
            <PagerContent>
                <MudTablePager RowsPerPageString=@L["Rows per page"] />
            </PagerContent>
        </MudTable>
    </MudStack>
}

<MudDialog @bind-Visible="@IsAlertDialogOpen"
    Options="@(new DialogOptions(){MaxWidth = MaxWidth.Medium, FullWidth = true})">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.ReportProblem" Color="Color.Warning" Class="mr-3 mb-n1" />
            @L["There are a number of vendors who have PO available"]
        </MudText>
    </TitleContent>
    <DialogContent>
        <AlertPaymentDay Parameters="@ListCheckPaymentDayResponse"></AlertPaymentDay>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(e => { IsAlertDialogOpen = false; })">@L["Cancel"]</MudButton>
        <MudButton OnClick="@( async ()=> await CreatePO())" Color="Color.Primary">@L["Continue create"]</MudButton>
    </DialogActions>
</MudDialog>
<MudDialog @bind-Visible="@IsAnyItemInvalid"
    Options="@(new DialogOptions(){MaxWidth = MaxWidth.Medium, FullWidth = true})">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.ReportProblem" Color="Color.Warning" Class="mr-3 mb-n1" />
            @L["There are a number of item invalid"]
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudTable Hover T="InvalidItem" Breakpoint="Breakpoint.Sm"
                  Height="680px" FixedHeader="true" GroupBy="@GroupDefinitionInvalidItems"
                  GroupHeaderStyle="background-color:var(--mud-palette-background-gray)" Items="@ListInvalidItem">
            <ColGroup>
                @if (GroupDefinitionInvalidItems.Expandable)
                {
                    <col style="width: 60px;" />
                }
                <col style="width: 60px;" />
            </ColGroup>
            <GroupHeaderTemplate>
                <MudTh Class="mud-table-cell-custom-group" colspan="2">
                    <MudText Align="Align.Left">@($"{context.GroupName}: {context.Key}")</MudText>
                </MudTh>
            </GroupHeaderTemplate>
            <HeaderContent>
                <MudTh>
                    <MudText Align="Align.Center">
                        Errors
                    </MudText>
                </MudTh>
            </HeaderContent>
            <RowTemplate>
                <MudTd DataLabel="Msg">
                    <MudText Class="pl-4">
                        @context.Msg
                    </MudText>
                </MudTd>
            </RowTemplate>
            <PagerContent>
                <MudTablePager RowsPerPageString=@L["Rows per page"] />
            </PagerContent>
        </MudTable>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(e => { IsAnyItemInvalid = false; })">@L["Cancel"]</MudButton>
        <MudButton OnClick="@(  ()=>  {Clear(); })" Color="Color.Primary">@L["Clear, Upload new file"]
        </MudButton>
    </DialogActions>
</MudDialog>