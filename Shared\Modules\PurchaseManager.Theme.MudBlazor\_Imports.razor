﻿@using System.Globalization
@using System.Net.Http
@using System.Net.Http.Json
@using System.Security.Claims
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Http;
@using Microsoft.Extensions.Localization;
@using static Microsoft.AspNetCore.Http.StatusCodes
@using Microsoft.JSInterop
@using PurchaseManager.Shared
@using PurchaseManager.Shared.Dto
@using PurchaseManager.Shared.Models.Account
@using PurchaseManager.Shared.Dto.ExternalAuth
@using PurchaseManager.Infrastructure.AuthorizationDefinitions
@using PurchaseManager.Shared.Extensions
@using PurchaseManager.Shared.Interfaces
@using PurchaseManager.Shared.Providers
@using PurchaseManager.Shared.Services
@using PurchaseManager.Shared.Localizer
@using PurchaseManager.Theme.Material.Shared.Layouts
@using PurchaseManager.Theme.Material.Shared.Components
@using PurchaseManager.UI.Base.Pages.Account
@using PurchaseManager.UI.Base.Pages.ExternalAuth
@using PurchaseManager.UI.Base.Shared.Components
@using PurchaseManager.UI.Base.Shared.Layouts
@using Blazored.FluentValidation
@using Karambolo.Common.Localization
@using Humanizer
@using MudBlazor
@using ExcelDataReader
@using Heron.MudCalendar
@using MudBlazor.Extensions
@using MudBlazor.Extensions.Components
@using MudBlazor.Extensions.Components.ObjectEdit
