using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Localization;
using MudBlazor;
using PurchaseManager.Shared.Dto.Contact;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Contact;

namespace PurchaseManager.Theme.Material.Admin.Pages.Admin;

public class ContactsBase : ComponentBase
{
    protected bool isLoading { get; set; }
    [Inject] public IStringLocalizer<Global> L { get; set; }
    [Inject] protected IViewNotifier viewNotifier { get; set; }
    [Inject] private HttpClient HttpClient { get; set; }
    [Inject] public IApiClient apiClient { get; set; }
    protected ContactFilter contactFilter { get; set; }
    protected MudAutocomplete<GetVendorDto> vendorAutoRef { get; set; } = new MudAutocomplete<GetVendorDto>();
    protected GetContactDto currentContact { get; set; } = new();
    protected bool isUpdate = false;
    protected bool editDialogOpen = false;
    protected IEnumerable<GetContactDto> listGetContactDto { get; set; }
    protected override async Task OnInitializedAsync()
    {
        listGetContactDto = new List<GetContactDto>();
        contactFilter = new();
        await LoadAllContact();
        await base.OnInitializedAsync();
    }
    protected async Task LoadAllContact()
    {
        try
        {
            var resp = await apiClient.GetAllContacts(contactFilter);
            var data = resp.Results.ToList();
            listGetContactDto = data;
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    protected async Task OnToggleContactStatus(string number)
    {
        try
        {
            var resp = await apiClient.BlockOrUnblockContact(number);
            viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation Successfully"]);
            await LoadAllContact();
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    protected async Task<IEnumerable<GetVendorDto>> ItemSearch(string value, CancellationToken token)
    {
        var apiResponse = await apiClient.SearchVendorByNameOrNumber(value, value, token);
        if (apiResponse.IsSuccessStatusCode)
        {
            return apiResponse.Result;
        }
        return new List<GetVendorDto>();
    }
    protected void OnSearchByVendorName(GetVendorDto vendorViewModel)
    {
        if (currentContact is not null && vendorViewModel is not null) currentContact.VendorNumber = vendorViewModel.Number;
    }
    protected async Task OnSearchContactByVendorName(GetVendorDto vendorViewModel)
    {
        if (vendorViewModel is not null)
        {
            contactFilter.VendorNumber = vendorViewModel.Number;
            await LoadAllContact();
        }
    }
    protected async Task OnKeyUp(KeyboardEventArgs e)
    {
        if (e.Code == "Enter") await LoadAllContact();
    }
    protected async Task OnSearchContactByField()
    {
        await LoadAllContact();
    }
    protected async Task OnReload()
    {
        contactFilter = new ContactFilter();
        await vendorAutoRef.ResetAsync();
        await LoadAllContact();
    }
    protected async Task SubmitContactAsync()
    {
        try
        {
            if (isUpdate)
            {
                var updateContact = new UpdateContactDto()
                {
                    Number = currentContact.Number.Trim(),
                    Name = currentContact.Name.Trim(),
                    Address = currentContact.Address.Trim(),
                    Phone = currentContact.Phone.Trim(),
                    Tax = currentContact.Tax.Trim(),
                    Email = currentContact.Email.Trim(),
                    Tags = currentContact.Tags.Trim(),
                    VendorNumber = currentContact.VendorNumber.Trim(),
                };
                var resp = await apiClient.UpdateContact(updateContact);
                if (resp.StatusCode == StatusCodes.Status200OK)
                {
                    viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation Successfully"]);
                    await LoadAllContact();
                    editDialogOpen = false;
                    currentContact = new();
                }
                else viewNotifier.Show(resp.Result.ToString(), ViewNotifierType.Error, L["Operation Failed"]);
            }
            else
            { // create new contact
                var createContact = new CreateContactDto()
                {
                    Name = currentContact.Name.Trim(),
                    Address = currentContact.Address.Trim(),
                    Phone = currentContact.Phone.Trim(),
                    Tax = currentContact.Tax.Trim(),
                    Email = currentContact.Email.Trim(),
                    Tags = currentContact.Tags.Trim(),
                    VendorNumber = currentContact.VendorNumber.Trim(),
                };
                var resp = await apiClient.CreateContact(createContact);
                if (resp.StatusCode == StatusCodes.Status201Created)
                {
                    viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation Successfully"]);
                    await LoadAllContact();
                    editDialogOpen = false;
                    currentContact = new();
                }
                else viewNotifier.Show(resp.Result is not null ? resp.Result.ToString() : resp.Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    protected List<string> RenderTags(string tags)
    {
        if (tags == null) return new List<string>();
        var ts = tags.Split(',').ToList();
        return ts.Count() == 0 ? null : ts;
    }
}
