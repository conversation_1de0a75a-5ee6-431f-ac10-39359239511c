﻿using System.Diagnostics;
using System.Security.Claims;
using Microsoft.AspNetCore.Mvc.Filters;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Factories;
namespace PurchaseManager.Server.Filters;

public class ApiLogFilter : IAsyncActionFilter
{
    private readonly IDbContextFactory _dbContextFactory;

    public ApiLogFilter(IDbContextFactory dbContextFactory)
    {
        _dbContextFactory = dbContextFactory;
    }

    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        var request = context.HttpContext.Request;

        if (request.Method == "GET")
        {
            await next();
            return;
        }

        var watch = Stopwatch.StartNew();

        // Enable buffering for the request stream
        request.EnableBuffering();
        var requestBodyContent = await new StreamReader(request.Body).ReadToEndAsync();
        request.Body.Position = 0;

        // Replace the original response stream with a memory stream
        var originalResponseBodyStream = context.HttpContext.Response.Body;
        using var responseBody = new MemoryStream();
        context.HttpContext.Response.Body = responseBody;

        await next();

        watch.Stop();

        // Read the response body from the memory stream
        context.HttpContext.Response.Body.Seek(0, SeekOrigin.Begin);
        var responseBodyContent = await new StreamReader(context.HttpContext.Response.Body).ReadToEndAsync();
        context.HttpContext.Response.Body.Seek(0, SeekOrigin.Begin);

        var userIdClaim = context.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier);
        var applicationUserId = userIdClaim != null ? Guid.Parse(userIdClaim.Value) : (Guid?)null;

        // Create log entry
        var logEntry = new ApiLogItem
        {
            RequestTime = DateTime.Now,
            ResponseMillis = watch.ElapsedMilliseconds,
            StatusCode = context.HttpContext.Response.StatusCode,
            Method = request.Method,
            Path = request.Path,
            QueryString = request.QueryString.ToString(),
            RequestBody = requestBodyContent,
            ResponseBody = responseBodyContent,
            IPAddress = context.HttpContext.Connection.RemoteIpAddress?.ToString() ?? "::1",
            ApplicationUserId = applicationUserId
        };

        await LogRequestAsync(logEntry);

        // Copy the contents of the memory stream to the original response stream
        context.HttpContext.Response.Body.Seek(0, SeekOrigin.Begin);
        await context.HttpContext.Response.Body.CopyToAsync(originalResponseBodyStream);
        context.HttpContext.Response.Body = originalResponseBodyStream;
    }

    private async Task LogRequestAsync(ApiLogItem logEntry)
    {
        await using var dbContext = _dbContextFactory.CreateDbContext();
        dbContext.ApiLogs.Add(logEntry);
        await dbContext.SaveChangesAsync();
    }

}
