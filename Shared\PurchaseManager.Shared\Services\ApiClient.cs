using System.Linq.Expressions;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using Breeze.Sharp;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Contact;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.Email;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Dto.QueueGenPO;
using PurchaseManager.Shared.Dto.Response;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Models.Contact;
using PurchaseManager.Shared.Models.Email;
using PurchaseManager.Shared.Models.Item;
using PurchaseManager.Shared.Models.PO;
using PurchaseManager.Shared.Models.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Models.Report.PO;
namespace PurchaseManager.Shared.Services;

public class ApiClient : BaseApiClient, IApiClient
{
    private readonly IJSRuntime _jsRuntime;

    public ApiClient(HttpClient httpClient, ILogger<ApiClient> logger, IJSRuntime jsRuntime) : base(httpClient, logger)
    {
        _jsRuntime = jsRuntime;
    }
    public async Task<UserProfile> GetUserProfile()
    {
        return (await entityManager.ExecuteQuery(new EntityQuery<UserProfile>().From("UserProfile"), CancellationToken.None))
            .SingleOrDefault();
    }
    public async Task<QueryResult<TenantSetting>> GetTenantSettings()
    {
        return await GetItems<TenantSetting>("TenantSettings", orderBy: i => i.Key);
    }
    public async Task<QueryResult<ApplicationUser>> GetUsers(Expression<Func<ApplicationUser, bool>> predicate = null,
        int? take = null, int? skip = null)
    {
        return await GetItems("Users", predicate, orderBy: i => i.UserName, null, take, skip);
    }
    public async Task<QueryResult<ApplicationRole>> GetRoles(Expression<Func<ApplicationRole, bool>> predicate = null,
        int? take = null, int? skip = null)
    {
        return await GetItems("Roles", predicate, orderBy: i => i.Name, null, take, skip);
    }
    public async Task<QueryResult<DbLog>> GetLogs(Expression<Func<DbLog, bool>> predicate = null, int? take = null, int? skip = null)
    {
        return await GetItems("Logs", predicate, null, orderByDescending: i => i.TimeStamp, take, skip);
    }
    public async Task<QueryResult<ApiLogItem>> GetApiLogs(Expression<Func<ApiLogItem, bool>> predicate = null, int? take = null,
        int? skip = null)
    {
        return await GetItems("ApiLogs", predicate, null, orderByDescending: i => i.RequestTime, take, skip);
    }
    public async Task<QueryResult<Todo>> GetToDos(ToDoFilter filter, int? take = null, int? skip = null)
    {
        return await GetItems<Todo>("Todos", orderByDescending: i => i.CreatedOn, take: take, skip: skip,
        parameters: filter?.ToDictionary());
    }
    public async Task<QueryResult<UnitOfMeasureDto>> GetAllUnitOfMeasure(Expression<Func<UnitOfMeasureDto, bool>> predicate = null,
        UnitFilter filter = null, int? take = null, int? skip = null)
    {
        return await GetItems("GetAllUnitOfMeasure", predicate,
        orderBy: i => i.RowId, take: take, skip: skip, parameters: filter?.ToDictionary());
    }
    public async Task<QueryResult<ApplicationUser>> GetTodoCreators(ToDoFilter filter)
    {
        return await GetItems<ApplicationUser>("TodoCreators", orderBy: i => i.UserName, parameters: filter?.ToDictionary());
    }
    public async Task<QueryResult<ApplicationUser>> GetTodoEditors(ToDoFilter filter)
    {
        return await GetItems<ApplicationUser>("TodoEditors", orderBy: i => i.UserName, parameters: filter?.ToDictionary());
    }
    public async Task<ApiResponseDto> SendTestEmail(EmailRequestDto email)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("api/Email/SendMail", email);
    }
    public async Task<ApiResponseDto> SendEmail(EmailRequestDto email)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("api/data/SendMail", email);
    }
    public async Task<QueryResult<DetailEmailTemplate>> GetTemplates(EmailTemplateFilter filter = null)
    {
        return await GetItems<DetailEmailTemplate>("GetEmailTemplates", null, orderBy: i => i.EmailTemplatesId,
        orderByDescending: i => i.EmailTemplatesId, null, null, filter?.ToDictionary());
    }

    #region Item apis
    public async Task<ApiResponseDto> UpdateItem(UpdateItemDto item, string number)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>($"api/data/UpdateItem/{number}", item);
    }
    #endregion
    //---------------------------------------------------------------------------------------------------------------------------------------------
    public async Task<QueryResult<NumberSeries>> GetNumberSeries(int? take = null, int? skip = null)
    {
        return await GetItems<NumberSeries>("NumberSeries", orderByDescending: i => i.RowId, take: take, skip: skip);
    }
    public async Task<QueryResult<NumberSeriesLine>> GetNumberSeriesLine(int? take = null, int? skip = null)
    {
        return await GetItems<NumberSeriesLine>("NumberSeriesLine", orderByDescending: i => i.RowId, take: take, skip: skip);
    }
    public async Task<QueryResult<string>> GetNumberCode()
    {
        return await GetItems<string>(from: "SeriesCode");
    }
    public async Task<ApiResponseDto> CreateIUOM(List<CreateItemUnitOfMeasureDto> itemUnitOfMeasure)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/data/InsertItemUnitOfMeasure", itemUnitOfMeasure);
    }
    public async Task<ApiResponseDto> UpdateIUOM(string itemNumber, string unitOfMeasureCode,
        UpdateItemUnitOfMeasureDto itemUnitOfMeasure)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>(
        $"api/data/UpdateItemUnitOfMeasure?itemNumber={itemNumber}&unitOfMeasureCode={unitOfMeasureCode}", itemUnitOfMeasure);
    }
    public async Task<ApiResponseDto> CreateSalePrice(List<CreateSalesPriceDto> salesPrices)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/data/InsertSalesPrice", salesPrices);
    }
    public async Task<ApiResponseDto> UpdateSalePrice(string itemNumber, DateTime startDate, string unitOfMeasure,
        UpdateSalesPriceDto salesPrice)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>(
        $"api/data/UpdateSalesPrice?itemNumber={itemNumber}&startDate={startDate}&unitOfMeasure={unitOfMeasure}", salesPrice);
    }
    public async Task<ApiResponseDto> CreateItem(CreateItemDto createItemDto)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("api/data/InsertItem", createItemDto);
    }
    public async Task<ApiResponseDto> CreateUOM(List<CreateUnitOfMeasureDto> createUnitOfMeasureDto)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("api/data/InsertUnit", createUnitOfMeasureDto);
    }
    public async Task<ApiResponseDto> UpdateUOM(UpdateUnitOfMeasureDto updateUnitOfMeasureDto, string code)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>($"api/data/UpdateUnit/{code}", updateUnitOfMeasureDto);
    }
    public async Task<ApiResponseDto<DetailItemDto>> GetItemByNumber(string itemNumber)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<DetailItemDto>>("api/data/GetItem?itemNumber=" + itemNumber);
    }
    public async Task<ApiResponseDto> CreatePO(List<DetailPoDto> detailPos)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("api/PurchaseOrder/CreatePO", detailPos);
    }
    public async Task<ApiResponseDto> UploadFiles(MultipartFormDataContent content)
    {
        httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        httpClient.DefaultRequestHeaders.ExpectContinue = false;
        return await httpClient.PostFileAsync<ApiResponseDto>("api/data/Upload", content);
    }
    public async Task<HttpResponseMessage> DeleteFile(string fileId)
    {
        return await httpClient.DeleteAsync($"api/data/DeleteFile?fileId={fileId}");
    }
    public async Task<ApiResponseDto> CreateDemand(List<CreateQueueGenPoDemandDto> detailPoDtos)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("api/PurchaseOrder/Demand", detailPoDtos);
    }
    public async Task<QueryResult<Schedule>> GetSchedules(ScheduleFilter filter)
    {
        return await GetItems<Schedule>("GetSchedule", orderBy: i => i.DueDate, parameters: filter?.ToDictionary());
    }
    public async Task<QueryResult<ColorDto>> GetColors()
    {
        return await GetItems<ColorDto>(from: "GetColors");
    }
    public async Task<ApiResponseDto> InsertOrUpdateColors(List<InsertOrUpdateItemColorsDto> colorsDtos)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("api/data/InsertOrUpdateColors", colorsDtos);
    }
    public async Task<ApiResponseDto<DetailPurchaseSuggestedPaymentHeaderDto>> GetPurchaseSuggestedPaymentHeaderByDocumentNo(
        string documentNumber, PurchaseSuggestedPaymentHeaderFilter filter)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<DetailPurchaseSuggestedPaymentHeaderDto>>(
        $"api/data/get-header/{documentNumber}" + (filter != null ? filter.ToQuery() : ""));
    }
    public async Task<ApiResponseDto> UpdatePurchaseSuggestedPaymentLine(
        UpdatePurchaseSuggestedPaymentLineDto updatePurchaseSuggestedPaymentLineDto)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>($"api/data/update-line", updatePurchaseSuggestedPaymentLineDto);
    }
    public async Task<ApiResponseDto> OpenPurchaseSuggestedPayment(string documentNumber)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/data/open-document/{documentNumber}", null);
    }
    public async Task<ApiResponseDto> ClosePurchaseSuggestedPayment(string documentNumber)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/data/close-document/{documentNumber}", null);
    }
    public async Task<ApiResponseDto> ApprovePurchaseSuggestedPayment(string documentNumber)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/data/approve-document/{documentNumber}", null);
    }
    public async Task<ApiResponseDto> InsertDemandReason(CreateDemandReasonDto item)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/data/insert-demand-reason", item);
    }
    public async Task<ApiResponseDto<List<DetailDemandReasonDto>>> GetAllDemandReason()
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<List<DetailDemandReasonDto>>>($"api/data/gets-demand-reason");
    }
    public async Task<ApiResponseDto> UpdateDemandReason(UpdateDemandReasonDto item)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/data/update-demand-reason", item);
    }
    public async Task<ApiResponseDto> BlockDemandReason(string id)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/data/block-demand-reason/{id}", null);
    }
    public async Task<ApiResponseDto> CreatePOFromPsp(List<ApproveDocumentDto> dtos)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("api/PurchaseOrder/Demand-v2", dtos);
    }
    public async Task<QueryResult<GetContactDto>> GetAllContacts(ContactFilter filter)
    {
        return await GetItems<GetContactDto>("contacts", parameters: filter?.ToDictionary());
    }
    public async Task<ApiResponseDto<GetContactDto>> GetContactByNumber(string number)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<GetContactDto>>("api/data/contacts/" + number);
    }
    public async Task<ApiResponseDto> CreateContact(CreateContactDto dto)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("api/data/contact", dto);
    }
    public async Task<ApiResponseDto> UpdateContact(UpdateContactDto dto)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>("api/data/contact", dto);
    }
    public async Task<ApiResponseDto> BlockOrUnblockContact(string number)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/data/contact/{number}/block-unblock", null);
    }
    public async Task<ApiResponseDto<List<GetVendorDto>>> SearchVendorByNameOrNumber(string name, string number,
        CancellationToken token)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<List<GetVendorDto>>>($"api/vendor/Search?number={number}&name={name}",
        token);
    }
    public async Task<ApiResponseDto> GetAllPoReportByParams(PoReportRequest request)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/Report/get-all-po-report-by-params", request);
    }
    public async Task<ApiResponseDto> GetReportVendorAccount()
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/Report/get-report-vendor-account", null);
    }
    public async Task<ApiResponseDto<List<CheckPaymentDayResponse>>> CheckPaymentDayByVendorNumber(List<string> listVendorNumber)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<List<CheckPaymentDayResponse>>>(
        $"api/PurchaseOrder/check-payment-day-by-vendor-number", listVendorNumber);
    }
    public async Task<ApiResponseDto<List<CheckPaymentDayResponse>>> CheckPaymentDayByItemNumber(List<string> listItemNumber)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<List<CheckPaymentDayResponse>>>(
        $"api/PurchaseOrder/check-payment-day-by-item-number", listItemNumber);
    }
    public async Task<ApiResponseDto<List<CheckPaymentDayResponse>>> CheckPaymentDayBySuggestDocumentNumber(
        List<string> listSuggestDocumentNumber)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<List<CheckPaymentDayResponse>>>(
        $"api/PurchaseOrder/check-payment-day-by-suggest-document-number", listSuggestDocumentNumber);
    }

    public async Task<ApiResponseDto> CreateEmailTemplate(CreateEmailTemplateDto createEmailTemplateDto)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("api/data/CreateEmailTemplate", createEmailTemplateDto);
    }
    public async Task<ApiResponseDto> UpdateEmailTemplate(UpdateEmailTemplateDto dto, int templateId)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>($"api/data/UpdateEmailTemplate?emailTemplateId={templateId}", dto);
    }

    public async Task<ApiResponseDto<DetailEmailTemplate>> GetEmailTemplateById(int emailTemplateId)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<DetailEmailTemplate>>(
        "/api/data/GetEmailTemplateById?emailTemplateId=" + emailTemplateId);

    }

    public async Task<ApiResponseDto> SendEmailDetailPOToVendor(EmailInfoInPODetailDto emailInfoInPODetailDto)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("/api/data/SendEmailDetailPOToVendor", emailInfoInPODetailDto);
    }

    public async Task<ApiResponseDto<List<string>>> GetPurchaseSuggestPaymentTags()
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<List<string>>>("api/data/get-tags");

    }

    public async Task<ApiResponseDto<List<InvalidItem>>> ValidateItemsInDemandV2(List<DemandItemInfo> listDemandV2)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<List<InvalidItem>>>("/api/data/validate-items-in-demand-v2",
        listDemandV2);

    }

    public async Task<ApiResponseDto> CreateAccountVendor(RegisterVendorViewModel dto)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("api/Account/create-account-vendor", dto);
    }

    public async Task<ApiResponseDto> CheckTaxCode(string taxCode)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto>("api/Account/check-tax-code/" + taxCode);
    }
    public async Task<ApiResponseDto<PagedResult<UserVendorViewModel>>> GetsAccountVendor(AccountVendorFilter filter)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<PagedResult<UserVendorViewModel>>>("api/Account/get-account-vendor?" +
        filter.ToQuery());
    }

    public async Task<ApiResponseDto> ActiveAccountVendor(string useName)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto>($"api/Account/active-account-vendor/{useName}");
    }

    public async Task<ApiResponseDto> BlockAccountVendor(string useName)//toggle vendor account
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto>($"api/Account/block-account-vendor/{useName}");
    }
    public async Task<ApiResponseDto> ResetVendorPassword(string useName)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/Account/reset-vendor-password", useName);
    }

    public async Task<QueryResult<DetailVendorItemDto>> GetAllVendorItems(VendorItemFilter filter)
    {
        return await GetItems<DetailVendorItemDto>("GetAllVendorItems", orderBy: i => i.RowId, parameters: filter?.ToDictionary());
    }

    public async Task<ApiResponseDto> CreateVendorItem(CreateVendorItemDto item)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/data/CreateVendorItem", item);
    }

    public async Task<ApiResponseDto> UpdateVendorItem(UpdateVendorItemDto item)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/data/UpdateVendorItem", item);
    }

    public async Task<ApiResponseDto<DetailVendorItemDto>> ToggleVendorItemStatus(int rowId)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<DetailVendorItemDto>>($"api/data/ToggleVendorItemStatus?rowId={rowId}",
        rowId);
    }
    public async Task<ApiResponseDto> CreatePurchaseSuggestedPaymentV2(MultipartFormDataContent content)
    {
        try
        {
            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            httpClient.DefaultRequestHeaders.ExpectContinue = false;
            return await httpClient.PostFileAsync<ApiResponseDto>("api/data/create-demand/v2", content);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }
    public async Task<ApiResponseDto<LoginResponseModel>> LoginEmployee(string employeeCode)
    {
        var response = await httpClient.PostJsonAsync<ApiResponseDto<LoginResponseModel>>("api/data/employee-login", employeeCode);

        if (AppState.Runtime != BlazorRuntime.Server)
        {
            return response;
        }
        if (response.IsSuccessStatusCode)
        {
            await SubmitServerForm("/server/login/", new LoginInputModel
            {
                UserName = employeeCode, RememberMe = true
            });
        }

        return response;
    }
    private async Task SubmitServerForm(string path, AccountFormModel model)
    {
        model.__RequestVerificationToken =
            await _jsRuntime.InvokeAsync<string>("interop.getElementByName", "__RequestVerificationToken");

        await _jsRuntime.InvokeAsync<string>("interop.submitForm", path, model);
    }
    public async Task<ApiResponseDto<List<ResponseWithDetailError>>> ValidateFileData(List<CreatePurchasePriceDto> dtos)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<List<ResponseWithDetailError>>>(
        $"api/PurchasePrice/validate-data", dtos);
    }
    public async Task<ApiResponseDto> CreateMultiplePurchasePriceHeaderViaFile(List<CreatePurchasePriceDto> dtos)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/PurchasePrice/import-file-data", dtos);
    }
}
