﻿
@using PurchaseManager.Shared.Dto.PO
@inherits PromotionalBasePage
@page "/po/create-promotional"

@attribute [Authorize(Policies.IsPurchaseUser)]

<PageTitle>Promotional</PageTitle>
@if (IsLoading)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    <MudCard Elevation="0">
        <MudCardHeader>
            <CardHeaderContent>
                <MudStack Row Justify="Justify.SpaceAround">
                    <MudText Typo="Typo.h4">@L["Promotional"]</MudText>
                    <MudSpacer />
                    <MudAutocomplete T="GetVendorDto" ShrinkLabel="true" Label="Tìm theo nhà cung cấp"
                                     ShowProgressIndicator="true" @ref="AutoComplete"
                                     ValueChanged="@( async(value) => await ShowPOByVendorNumber(value))"
                                     ResetValueOnEmptyText
                                     ToStringFunc="dto => dto == null ? null : string.Concat(dto.Number, ' ', ('-'), ' ', dto.Name)" Required="true"
                                     SearchFunc="@ItemSearch" Margin="Margin.Dense" Dense="true">
                        <ProgressIndicatorInPopoverTemplate>
                            <MudList T="string" ReadOnly>
                                <MudListItem>
                                    Loading...
                                </MudListItem>
                            </MudList>
                        </ProgressIndicatorInPopoverTemplate>
                        <ItemTemplate Context="e">
                            <MudText>@e.Name</MudText>
                            <MudText Typo="Typo.body2">@e.Number</MudText>
                        </ItemTemplate>
                    </MudAutocomplete>
                    <MudDateRangePicker @ref="FromToDatePickerRef" Class="ml-6" PickerVariant="PickerVariant.Inline" Margin="Margin.Dense"
                                        PlaceholderStart="Start Date" PlaceholderEnd="End Date" @bind-DateRange="DateRange" Label="Tìm theo ngày tạo"
                                        AutoClose="@false">
                        <PickerActions>
                            <MudButton OnClick="@(() => FromToDatePickerRef.CloseAsync(false))">Cancel</MudButton>
                            <MudButton Color="Color.Primary" OnClick="@(() => FilterByDate())">Ok</MudButton>
                        </PickerActions>
                    </MudDateRangePicker>
                </MudStack>
            </CardHeaderContent>
        </MudCardHeader>
        <MudCardContent Class="pt-0">
            <MudGrid Class="my-4">
                <MudItem xs="3" Class="pt-0">
                    <MudPaper Class="px-4 py-14 pt-4 mt-2 ">
                        <MudStack>
                            <MudText Typo="Typo.h5" Align="Align.Center">@L["List PO of Vendor"] </MudText>
                            @if (SelectedVendor is not null && AutoComplete.Value is not null)
                            {
                                <MudText Typo="Typo.caption" Align="Align.Center">NCC: @SelectedVendor.Name </MudText>
                            }
                        </MudStack>
                        @if (AutoComplete.Value is not null && PurchaseHeaders.Any())
                        {
                            <MudBlazor.Extensions.Components.MudExList @ref="MudExListRef"
                                                                       Style="height: 700px"
                                                                       ToStringFunc="@(x => x.Number + " - " + x.BuyFromVendorName)"
                                                                       ItemCollection="PurchaseHeaders" SearchBox="true" T="POHeaderGetDto">
                                <ItemTemplate Context="subContext">
                                    <MudPaper Style="width: 100%" Elevation="0">

                                        <MudButton FullWidth OnClick="@(() => ShowDetail(subContext))" Variant="Variant.Filled" Color="@( subContext.Number == DocumentNo ? Color.Primary : Color.Default)">
                                            @subContext.Number - @subContext.OrderDate.ToString("dd/MM/yyyy")
                                        </MudButton>
                                    </MudPaper>
                                </ItemTemplate>
                            </MudBlazor.Extensions.Components.MudExList>
                        }
                        else if (AutoComplete.Value is not null && !PurchaseHeaders.Any())
                        {
                            <MudPaper Class="pa-16 my-8" Elevation="0">
                                <MudText Align="Align.Center">Không tìm thấy PO của NCC @AutoComplete.Value.Number - @AutoComplete.Value.Name</MudText>
                            </MudPaper>
                        }
                        else
                        {
                            <MudPaper Class="pa-16 my-8" Elevation="0">
                                <MudText Align="Align.Center">Chọn NCC để xem chứng từ của PO</MudText>
                            </MudPaper>
                        }
                    </MudPaper>
                </MudItem>
                <MudItem xs="9" Class="pt-0">
                    @if (DocumentNo  is not null)
                    {
                            <MudStack  Row Class="pa-3">
                                <MudText Typo="Typo.h6">Mã PO: <b>@PurchaseHeaderSelected.Number</b></MudText>
                                <MudSpacer />
                                <MudText Typo="Typo.h6">NCC: <b>@SelectedVendor.Name</b></MudText>
                                <MudSpacer />
                                <MudButton Class="" Variant="Variant.Filled" OnClick="CreatePromotionalOrders" Color="Color.Primary">Tạo</MudButton>
                            </MudStack>
                    }
                </MudItem>
            </MudGrid>
        </MudCardContent>
    </MudCard>


}