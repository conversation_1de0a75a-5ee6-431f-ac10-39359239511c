﻿@page "/account/login"
@using HorizontalAlignment=MudBlazor.HorizontalAlignment

@layout LoginLayout

<AuthorizeView Context="_">
    <Authorized>
        <LoadingBackground>
            <label>@L["Loading"]</label>
        </LoadingBackground>
    </Authorized>
    <NotAuthorized>
        @if (LoginViewModel == null)
        {
            <LoadingBackground/>
        }
        else
        {
            @if (LoginViewModel.EnableLocalLogin)
            {
                <MudStack Justify="Justify.Center" Style="height: 98vh; min-width: 400px;">
                    @if (IsShowAlertForgetPassword)
                    {
                        <MudAlert Severity="Severity.Info" ShowCloseIcon ContentAlignment="HorizontalAlignment.Center"
                                  CloseIconClicked="@(() => { IsShowAlertForgetPassword = false; })">
                            <strong>@L["Please contact admin system to reset your password"]</strong>
                        </MudAlert>
                    }
                    <MudCard Elevation="2" Class="pa-4">
                        <MudCardHeader>
                            <CardHeaderContent>
                                <div class="logo">
                                    <MudStack Row="true" Spacing="3" Justify="Justify.Center"
                                              AlignItems="AlignItems.Center">
                                        <a href="/" title="@AppState.AppName Home"><img
                                                src=@($"{Module.ContentPath}/images/logo-trungson.png")
                                                style="width:96px;" title="@AppState.AppName Home"
                                                alt="@AppState.AppName"/></a>
                                    </MudStack>
                                    <br/>
                                </div>
                                <MudText Typo="Typo.h5" Align="Align.Center">@L["Login"]</MudText>
                            </CardHeaderContent>
                        </MudCardHeader>
                        <MudCardContent>
                            <div class="d-flex flex-grow-1 gap-4">
                                <MudButton OnClick="() => VendorLogin()" Class="flex-grow-1"
                                           Variant="Variant.Filled">Sử Dụng Tài Khoản
                                </MudButton>
                                <MudButton OnClick="() => TsCareLogin()" Class="flex-grow-1"
                                           Variant="Variant.Filled" Color="Color.Primary">
                                    Đăng Nhập Qua HRM
                                </MudButton>
                            </div>
                        </MudCardContent>
                    </MudCard>
                </MudStack>
            }
        }
    </NotAuthorized>
</AuthorizeView>