﻿@page "/po"
@using PurchaseManager.Shared.Dto.Contact
@using PurchaseManager.Theme.Material.Demo.Shared.Components.PO
@if (IsLoading)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    @if (IsUserAuthorized())
    {
        <MudToolBar Gutters="false" Class="mb-2">
            <MudText Typo="Typo.h6">Purchase Order</MudText>
            <MudSpacer />
            <MudIconButton Icon="@Icons.Material.Outlined.Refresh" OnClick="@(() => Reload())" Size="Size.Small" />
        </MudToolBar>
        <MudGrid Spacing="4" Class="mb-2">
            <MudItem xs="12" sm="6" md="3" lg="2">
                <MudStack Row>
                    <MudDateRangePicker PickerVariant="PickerVariant.Inline" Variant="Variant.Text" PlaceholderStart="Start Date"
                                        Margin="Margin.Dense" PlaceholderEnd="End Date"
                                        DateRangeChanged="@OnCreateDateFilterChanged"
                                        DateRange="@(new DateRange(FromDate, ToDate))" Label="Tìm theo ngày tạo" AutoClose="@false">
                    </MudDateRangePicker>
                    <div class="pt-4 d-sm-none d-block">
                        <MudIconButton Size="Size.Small" Icon="@(IsShowMoreFilters? Icons.Material.Filled.KeyboardArrowUp: @Icons.Material.Filled.KeyboardArrowDown)"
                                       Color="@(IsShowMoreFilters? Color.Error : Color.Default)"
                                       OnClick="@(_ => IsShowMoreFilters = !IsShowMoreFilters)" />
                    </div>
                </MudStack>
            </MudItem>
            <AuthorizeView Policy="@Policies.IsPurchaseUser">
                <MudItem Class="@($"d-{(IsShowMoreFilters ? "block" : "none")} d-sm-block")" xs="12" sm="6" md="3" lg="2">
                    <MudStack Row>
                        <MudAutocomplete T="GetVendorDto" ShrinkLabel Clearable ListItemClass="m-0" ResetValueOnEmptyText
                                         Label="Tìm theo nhà cung cấp" ShowProgressIndicator Value="@VendorFilter"
                                         ValueChanged="@OnSearchByVendor"
                                         ToStringFunc="@(dto => dto.Number is null ? L["ALL"] : string.Concat(dto.Number, " - ", dto.Name))"
                                         SearchFunc="@VendorSearch" Margin="Margin.Dense">
                            <ProgressIndicatorInPopoverTemplate>
                                <MudList T="String" ReadOnly>
                                    <MudListItem>
                                        Loading...
                                    </MudListItem>
                                </MudList>
                            </ProgressIndicatorInPopoverTemplate>
                            <ItemTemplate Context="e">
                                <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween">
                                    <MudText>@e.Name</MudText>
                                    <MudChip Size="Size.Small" Color="@(e.Blocked == 1 ? Color.Default : Color.Success)"
                                             T="String">@(e.Blocked == 1 ? "Block" : "Active")</MudChip>
                                </MudStack>
                                <MudText Typo="Typo.body2"><b>Number: </b>@e.Number</MudText>
                            </ItemTemplate>
                        </MudAutocomplete>
                    </MudStack>
                </MudItem>
                <MudItem Class="@($"d-{(IsShowMoreFilters ? "block" : "none")} d-sm-block")" xs="12" sm="6" md="3" lg="2">
                    <MudStack Row>
                        <MudAutocomplete T="GetContactDto" ShrinkLabel Clearable ListItemClass="m-0" ResetValueOnEmptyText
                                         Label="Tìm theo brand" ShowProgressIndicator Value="@ContactFilter"
                                         ValueChanged="@OnSearchByContact"
                                         ToStringFunc="@(dto => dto.Number is null ? L["ALL"] : string.Concat(dto.Name))"
                                         SearchFunc="@ContactSearch" Margin="Margin.Dense">
                            <ProgressIndicatorInPopoverTemplate>
                                <MudList T="String" ReadOnly>
                                    <MudListItem>
                                        Loading...
                                    </MudListItem>
                                </MudList>
                            </ProgressIndicatorInPopoverTemplate>
                            <ItemTemplate Context="e">
                                <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween">
                                    <MudText>@e.Name</MudText>
                                    <MudChip Size="Size.Small" Color="@(e.Block ? Color.Default : Color.Success)"
                                             T="String">@(e.Block ? "Block" : "Active")</MudChip>
                                </MudStack>
                                <MudText Typo="Typo.body2"><b>Vendor number: </b>@e.VendorNumber</MudText>
                            </ItemTemplate>
                        </MudAutocomplete>
                    </MudStack>
                </MudItem>
            </AuthorizeView>
            <MudItem Class="@($"d-{(IsShowMoreFilters ? "block" : "none")} d-sm-block")" xs="6" sm="6" md="3" lg="2">
                <MudStack Row>
                    <MudTextField T="String" ShrinkLabel Variant="Variant.Text" Margin="Margin.Dense"
                                  Label="@L["DescriptionFilter"]" Value="@DescriptionQuery" Clearable
                                  ValueChanged="@OnSearchByDescription" />
                </MudStack>
            </MudItem>
            <MudItem Class="@($"d-{(IsShowMoreFilters ? "block" : "none")} d-sm-block")" xs="6" sm="6" md="3" lg="2">
                <AuthorizeView Policy="@Policies.IsPurchaseUser">
                    <MudStack Row>
                        <MudSelect Clearable T="PurchaseOrderFilerEnum" Value="SelectedStatus"
                                   OnClearButtonClick="@OnClearFilterByApproveStatus" ValueChanged="@OnSearchByStatus"
                                   Label="Status"
                                   Margin="Margin.Dense">
                            @foreach (var status in
                                                Enum.GetValues(typeof(PurchaseOrderFilerEnum)).Cast<PurchaseOrderFilerEnum>())
                            {
                                <MudSelectItem Value="@status">@GetMarginLabel(status)</MudSelectItem>
                            }
                        </MudSelect>
                    </MudStack>
                </AuthorizeView>
            </MudItem>
            <MudItem xs="12" sm="6" md="3" lg="2">
                <MudStack Row>
                    <MudTextField T="String" ShrinkLabel Variant="Variant.Text" Margin="Margin.Dense"
                                  Label="@L["PONumberFilter"]" Value="@PONumberQuery" Clearable
                                  ValueChanged="@OnSearchByPONumber" />
                </MudStack>
            </MudItem>
        </MudGrid>
        @if (IsParamsLoading)
        {
            <div class="">
                <MudStack Row Justify="Justify.FlexEnd">
                    <MudSkeleton Width="100px" Height="50px" />
                </MudStack>
                <MudSkeleton Height="30px" />
                @for (var i = 0; i < 39; i++)
                {
                    <MudSkeleton Height="15px" />
                }
            </div>
        }
        else
        {
            <ListPOByUser @ref="ListPoByUserRef" FromDate="@FromDate" ToDate="@ToDate" UserName="@UserViewModel.UserName"
                          ContactNumber="@ContactNumberQuery" VendorNumber="@VendorNumberQuery"
                          DescriptionFilter="@DescriptionQuery"
                          PONumberFilter="@PONumberQuery" StatusFilter="@StatusFilter" />
        }
    }
    else
    {
        <UserNotAuthorized />
    }
}
