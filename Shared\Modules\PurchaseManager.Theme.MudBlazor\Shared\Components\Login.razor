﻿@using Microsoft.AspNetCore.Components
@inject NavigationManager navigationManager
@inject AuthenticationStateProvider authStateProvider
@inject AppState appState
@inject IStringLocalizer<Global> L

<AuthorizeView Context="AuthorizeContext">
    <Authorized>
        <MudTooltip Text="@L["Logout"]" Placement="Placement.End">
            <MudButton StartIcon="@Icons.Material.Filled.Logout" Color="Color.Inherit" OnClick="LogoutClick">
                @L["Logout"]
            </MudButton>
        </MudTooltip>
    </Authorized>
    <NotAuthorized>
        <MudTooltip Text="@L["Login"]" Placement="Placement.End">
            <MudButton StartIcon="@Icons.Material.Filled.AccountBox" Color="Color.Inherit" Href="@PurchaseManager.Constants.Settings.LoginPath">
                @if (ShowLogInLabel)
                {@L["Login"]}
            </MudButton>
        </MudTooltip>
    </NotAuthorized>
</AuthorizeView>

@code {
    [Parameter]
    public bool ShowLogOutLabel { get; set; }

    [Parameter]
    public bool ShowLogInLabel { get; set; }

    async Task LogoutClick()
    {
        appState.ClearUserProfile();
        await ((IdentityAuthenticationStateProvider)authStateProvider).Logout();
    }
}
