using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.Contact;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Contact;
using PurchaseManager.Storage;
using static Microsoft.AspNetCore.Http.StatusCodes;
namespace PurchaseManager.Server.Managers;
public class ContactManager : IContactManager
{
    private const string Business = "CONTACT";
    private const string Branch = "AL";
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<ContactManager> _logger;
    private readonly IMapper _mapper;
    private readonly IAdminManager _adminManager;
    private readonly IStringLocalizer<Global> L;
    public ContactManager(ApplicationDbContext dbContext, ILogger<ContactManager> logger, IMapper mapper, IStringLocalizer<Global> l, IAdminManager adminManager)
    {
        _dbContext = dbContext;
        _logger = logger;
        _mapper = mapper;
        L = l;
        _adminManager = adminManager;
    }
    public async Task<ApiResponse> BlockOrUnblockContactAsync(string number)
    {
        try
        {
            var foundContact = _dbContext.Contacts.FirstOrDefault(c => c.Number == number);
            if (foundContact == null)
            {
                return new ApiResponse(Status404NotFound, "Contact could not found");
            }
            foundContact.Block = !foundContact.Block;
            await _dbContext.SaveChangesAsync();
            return new ApiResponse(Status200OK, L["Contact has been {0} Successfully", !foundContact.Block ? "Unblock" : "Block"]);
        }
        catch (Exception ex)
        {
            _logger.LogError("Lock or unlock contact has error(s): " + ex.GetBaseException().Message);
            return new ApiResponse(Status500InternalServerError, ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> CreateContactAsync(CreateContactDto dto)
    {
        try
        {
            bool emailExists = _dbContext.Contacts.AsNoTracking().Any(c => c.Email == dto.Email);
            bool phoneExists = _dbContext.Contacts.AsNoTracking().Any(c => c.Phone == dto.Phone);
            if (emailExists)
            {
                return new ApiResponse(Status400BadRequest, L["Email of contact already existed "]);
            }
            if (phoneExists)
            {
                return new ApiResponse(Status400BadRequest, L["Phone of contact already existed "]);
            }
            var contactNumber = await _adminManager.CreateNumberSeries(Business, Branch);
            var newContact = new Contact
            {
                Number = contactNumber,
                Name = dto.Name,
                Address = dto.Address,
                Phone = dto.Phone,
                Tax = dto.Tax,
                Email = dto.Email,
                Type = dto.Type,
                Block = false,
                CreatedAt = DateTime.Now,
                CreatedBy = _adminManager.GetUserLogin(),
                Tags = dto.Tags,
                VendorNumber = dto.VendorNumber,
            };
            _dbContext.Contacts.Add(newContact);
            await _dbContext.SaveChangesAsync();
            return new ApiResponse(Status201Created, L["Success"]);
        }
        catch (Exception ex)
        {
            _logger.LogError("Create contact has error(s): " + ex.GetBaseException().Message);
            return new ApiResponse(Status500InternalServerError, ex.GetBaseException().Message);
        }
    }
    public IQueryable<GetContactDto> GetAllContacts(ContactFilter filter)
    {
        try
        {
            var query = _dbContext.Contacts.AsNoTracking().Include(x => x.VendorNumberNavigation)
            .Where(c =>
                (filter.Phone == null || c.Phone.Contains(filter.Phone)) &&
                (filter.Name == null || c.Name.Contains(filter.Name)) &&
                (filter.Address == null || c.Address.Contains(filter.Address)) &&
                (filter.Email == null || c.Email.Contains(filter.Email)) &&
                (filter.VendorNumber == null || c.VendorNumber.Contains(filter.VendorNumber)) &&
                (filter.Number == null || c.Number.Contains(filter.Number))
            );
            if (filter.Skip is not null)
            {
                query = query.Skip(filter.Skip.Value);
            }
            if (filter.Take is not null)
            {
                query = query.Take(filter.Take.Value);
            }
            var rs = _mapper.ProjectTo<GetContactDto>(query);
            return rs;
        }
        catch (Exception ex)
        {
            _logger.LogError("Get all contact has error(s): " + ex.GetBaseException().Message);
            return null;
        }
    }
    public ApiResponse GetAllContactsByVendorNumber(string vendorNumber)
    {
        try
        {
            var foundContacts = _dbContext.Contacts.AsNoTracking().Where(v => v.VendorNumber == vendorNumber);
            return !foundContacts.Any()
                ? new ApiResponse(Status404NotFound, L["Contact could not found by Vendor number"])
                : new ApiResponse(Status200OK, L["Get Contact by Vendor Number Successfully"],
                _mapper.ProjectTo<GetContactDto>(foundContacts));
        }
        catch (Exception ex)
        {
            _logger.LogError("Get all contact  by vendor number has error(s): " + ex.GetBaseException().Message);
            return new ApiResponse(Status500InternalServerError, ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> SearchAutocomplete(string number, string name, string queryString)
    {
        try
        {
            var query = _dbContext.Contacts.Include(x => x.VendorNumberNavigation).AsNoTracking();

            switch (string.IsNullOrEmpty(number))
            {
                case false when !string.IsNullOrEmpty(name):
                    query = query.Where(item => item.Number.Contains(number) || item.Name.Contains(name));
                    break;
                case false:
                    query = query.Where(item => item.Number.Contains(number) || item.Name.Contains(number));
                    break;
                default:
                {
                    if (!string.IsNullOrEmpty(name))
                    {
                        query = query.Where(item => item.Name.Contains(name) || item.Number.Contains(name));
                    }
                    break;
                }
            }

            if (!queryString!.Contains("take"))
            {
                query = query.Take(100);
            }

            if (!queryString!.Contains("skip"))
            {
                query = query.Skip(0);
            }

            var result = await query.ToListAsync();

            var data = _mapper.Map<List<GetContactDto>>(result);
            return new ApiResponse(Status200OK, L["Search contact successfully"], data);
        }
        catch (Exception ex)
        {
            return new ApiResponse(Status500InternalServerError, ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse<GetContactDto>> GetContactByNumberAsync(string number)
    {
        try
        {
            var foundContact = await _dbContext.Contacts.AsNoTracking().FirstOrDefaultAsync(c => c.Number == number);
            if (foundContact is null)
            {
                return new ApiResponse(Status404NotFound, L["Contact could not found"]);
            }
            return new ApiResponse(Status200OK, L["Get Contact Successfully"], _mapper.Map<GetContactDto>(foundContact));
        }
        catch (Exception ex)
        {
            _logger.LogError("Get all contact by number has error(s): " + ex.GetBaseException().Message);
            return new ApiResponse(Status500InternalServerError, ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> UpdateContactAsync(UpdateContactDto dto)
    {
        try
        {
            var foundContact = _dbContext.Contacts.AsNoTracking().FirstOrDefault(c => c.Number == dto.Number && c.VendorNumber == dto.VendorNumber);
            if (foundContact is null)
            {
                return new ApiResponse(Status404NotFound, L["Contact could not found"]);
            }
            foundContact.Number = dto.Number;
            foundContact.Name = dto.Name;
            foundContact.Address = dto.Address;
            foundContact.Phone = dto.Phone;
            foundContact.Tax = dto.Tax;
            foundContact.Type = dto.Type;
            foundContact.Email = dto.Email;
            foundContact.Tags = dto.Tags;
            foundContact.HasAccount = dto.HasAccount;
            foundContact.LastModifiedAt = DateTime.Now;
            foundContact.LastModifiedBy = _adminManager.GetUserLogin();
            foundContact.VendorNumber = dto.VendorNumber;
            await _dbContext.SaveChangesAsync();
            return new ApiResponse(Status200OK, L["Contact Updated Successfully"]);
        }
        catch (Exception ex)
        {
            _logger.LogError("Update contact has error(s): " + ex.GetBaseException().Message);
            return new ApiResponse(Status500InternalServerError, ex.GetBaseException().Message);
        }
    }
}
