@inherits ListEmailPage
@layout AdminLayout
@page "/admin/settings/email/list-email"
@if (isLoading)
{
    <LoadingBackground />
}
else
{
    <MudCard Elevation="0">
        <MudCardHeader Class="">
            <CardHeaderContent>
                <div>
                    <MudText Typo="Typo.h5">Email</MudText>
                    <MudText Typo="Typo.caption">List Email</MudText>
                </div>
            </CardHeaderContent>
            <CardHeaderActions>
                <MudButton StartIcon="@Icons.Material.Filled.Add" OnClick="@(() => {navigation.NavigateTo("/item/Add");})"
                           ButtonType="ButtonType.Submit" Variant="Variant.Filled" Color="Color.Primary" Class="">
                    Add
                </MudButton>
                <MudIconButton Icon="@Icons.Material.Filled.Refresh" OnClick="@(() => Reload())" Size="Size.Medium"
                               Class="ma-2" />
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent>
            <MudTable T="PurchaseManager.Shared.Dto.Email.DetailEmailDto"
                      ServerData="@(new Func<TableState, CancellationToken, Task<TableData<PurchaseManager.Shared.Dto.Email.DetailEmailDto>>>(ServerReload))"
                      Striped Dense Bordered Outlined Hover Class=" pa-2" Elevation="0" LoadingProgressColor="Color.Info"
                      Loading="@isBusy" RowClass="" @ref="table">
                @*<ToolBarContent>
                    <MudSpacer />
                    <MudTextField T="string" ValueChanged="@(s=>OnSearch(s))" Placeholder="Tìm theo tên hoặc mã SKU"
                            @ref="SearchRef" Adornment="Adornment.Start" AdornmentIcon="@Icons.Material.Filled.Search"
                            IconSize="Size.Small" Class="mt-0">
                        </MudTextField>
                    <MudIconButton Icon="@Icons.Material.Filled.Clear" Color="Color.Error"
                            OnClick="@(async() => await SearchRef.Clear())" Size="Size.Medium" Class="ma-2" />
                        <MudSpacer />
                </ToolBarContent>*@
                <HeaderContent>
                    <MudTh>
                        <MudTableSortLabel SortLabel="Subject" T="PurchaseManager.Shared.Dto.Email.DetailEmailDto">Subject</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="EmailType" T="PurchaseManager.Shared.Dto.Email.DetailEmailDto">EmailType</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="CreatedOn" T="PurchaseManager.Shared.Dto.Email.DetailEmailDto">
                            CreatedOn
                        </MudTableSortLabel>
                    </MudTh>
                </HeaderContent>
                <RowTemplate Context="row">
                    <MudTd DataLabel="Subject"><MudLink>@row.Subject</MudLink> </MudTd>
                    <MudTd DataLabel="EmailType">@row.Email</MudTd>
                    <MudTd DataLabel="CreatedOn">@row.CreatedOn</MudTd>
                </RowTemplate>
                <PagerContent>
                    <MudTablePager RowsPerPageString=@L["Rows per page"] />
                </PagerContent>
            </MudTable>
        </MudCardContent>
    </MudCard>
}