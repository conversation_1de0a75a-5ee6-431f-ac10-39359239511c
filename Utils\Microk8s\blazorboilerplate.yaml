apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: kompose --file docker-compose.yml convert
    kompose.version: 1.24.0 (4a2a0458)
  creationTimestamp: null
  labels:
    io.kompose.service: purchasemanager
  name: purchasemanager
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: purchasemanager
  strategy:
    type: Recreate
  template:
    metadata:
      annotations:
        kompose.cmd: kompose --file docker-compose.yml convert
        kompose.version: 1.24.0 (4a2a0458)
      creationTimestamp: null
      labels:
        io.kompose.service: purchasemanager
    spec:
      containers:
      - image: localhost:32000/purchasemanager:v1.1
        imagePullPolicy: "Always"
        name: purchasemanager
        ports:
          - containerPort: 80
          - containerPort: 443
        resources: {}
        envFrom:
          - configMapRef:
              name: purchasemanager-config
      restartPolicy: Always
status: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    kompose.cmd: kompose --file docker-compose.yml convert
    kompose.version: 1.24.0 (4a2a0458)
  creationTimestamp: null
  labels:
    io.kompose.service: purchasemanager
  name: purchasemanager
spec:
  type: NodePort
  ports:
    - name: "53415"
      port: 53415
      targetPort: 80
      nodePort: 30880
    - name: "53443"
      port: 53443
      targetPort: 443
      nodePort: 30883
  selector:
    io.kompose.service: purchasemanager
status:
  loadBalancer: {}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  creationTimestamp: null
  labels:
    io.kompose.service: dbdata
  name: dbdata
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Mi
status: {}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kompose.cmd: kompose --file docker-compose.yml convert
    kompose.version: 1.24.0 (4a2a0458)
  creationTimestamp: null
  labels:
    io.kompose.service: sqlserver
  name: sqlserver
spec:
  replicas: 1
  selector:
    matchLabels:
      io.kompose.service: sqlserver
  strategy:
    type: Recreate
  template:
    metadata:
      annotations:
        kompose.cmd: kompose --file docker-compose.yml convert
        kompose.version: 1.24.0 (4a2a0458)
      creationTimestamp: null
      labels:
        io.kompose.service: sqlserver
    spec:
      containers:
      - image: mcr.microsoft.com/mssql/server
        name: sqlserver
        ports:
          - containerPort: 1433
        resources: {}
        volumeMounts:
          - mountPath: /var/opt/mssql
            name: dbdata
        envFrom:
        - configMapRef:
            name: sqlserver-config
      restartPolicy: Always
      volumes:
        - name: dbdata
          persistentVolumeClaim:
            claimName: dbdata
      
status: {}
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    kompose.cmd: kompose --file docker-compose.yml convert
    kompose.version: 1.24.0 (4a2a0458)
  creationTimestamp: null
  labels:
    io.kompose.service: sqlserver
  name: sqlserver
spec:
  type: NodePort
  ports:
    - name: "1433"
      port: 1433
      targetPort: 1433
      nodePort: 31433
  selector:
    io.kompose.service: sqlserver
status:
  loadBalancer: {}