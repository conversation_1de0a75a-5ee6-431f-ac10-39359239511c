using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using MudBlazor;
namespace PurchaseManager.Theme.Material.Demo.Shared.Components;
public partial class Dialog : ComponentBase
{
    [Inject] public IDialogService DialogService { get; set; } = default!;
    [Parameter]
    public Func<Task> OnClose { get; set; }
    [Parameter]
    public Func<Task> OnOk { get; set; }
    [Parameter]
    public DialogReference DialogReference { get; set; }
    [Parameter]
    public string ContentText { get; set; }
    [Parameter]
    public string OkText { get; set; }
    [Parameter]
    public string CancelText { get; set; }
    [Parameter]
    public Color OkColor { get; set; }
    private async Task Submit()
    {
        // DialogReference.Close(DialogResult.Ok(true));
        await OnOk.Invoke();
    }
    private async Task Cancel()
    {
        await OnClose.Invoke();
    }
}
