namespace PurchaseManager.Infrastructure.Storage.DataModels.Base;

/// <summary>
/// Full tracking entity with creation, modification and status tracking capabilities
/// </summary>
public abstract class FullTrackingEntity : EntityBase, ICreationTracking, IModificationTracking, IStatusTracking
{
    /// <summary>
    /// User who created this record
    /// </summary>
    public string CreatedBy { get; set; } = null!;

    /// <summary>
    /// Date and time when this record was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// User who last modified this record
    /// </summary>
    public string? LastModifiedBy { get; set; }

    /// <summary>
    /// Date and time when this record was last modified
    /// </summary>
    public DateTime? LastModifiedAt { get; set; }

    /// <summary>
    /// Modification status of the record (Active, Inactive, Deleted, etc.)
    /// </summary>
    public int ModificationStatus { get; set; } = (int)ModificationStatusEnum.ACTIVE;
}
