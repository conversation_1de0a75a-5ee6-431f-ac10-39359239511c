syntax = "proto3";

option csharp_namespace = "TS.GRPC";
import "google/protobuf/timestamp.proto";
package grpc.share;

message SyncReq {
  string  clientName = 1;
}
message SyncVendorReply {
  repeated SyncVendor listVendor = 1;
  bool  isSuccess = 2;
  string msg = 3;
}


message ReSyncVendorReq {
  string  clientName = 1;
  repeated SyncVendor listVendor = 2;
}

message SyncVendor {
  string number = 1;
  string name = 2;
  string search_name = 3;
  string address = 4;
  string city = 5;
  string contact = 6;
  string phone = 7;
  string telex = 8;
  string vendor_posting_group = 9;
  string currency_code = 10;
  string purchaser_code = 11;
  string shipment_method_code = 12;
  string shipping_agent_code = 13;
  string country_code = 14;
  int32 blocked = 15;
  string pay_to_vendor_number = 16;
  string payment_method_code = 17;
  google.protobuf.Timestamp last_date_modified = 18;
  string fax = 19;
  string vatregistration_number = 20;
  string post_code = 21;
  string county = 22;
  string email = 23;
  string home_page = 24;
  string vatbusiness_posting_group = 25;
  int32 vendor_group = 26;
  google.protobuf.Timestamp create_date = 27;
  string certificate_of_registration = 28;
  google.protobuf.Timestamp registration_of_expiration_date = 29;
  google.protobuf.Timestamp license_date = 30;
  string last_user_modified = 31;
  int32 internal = 32;
  int32 status = 33;
  google.protobuf.Timestamp last_modified_time = 34;
  google.protobuf.Timestamp exprire_license_date = 35;
}

message SyncIUOMReply{
  repeated SyncIUOM listIUOM = 1;
  bool  isSuccess = 2;
  string msg = 3;
}

message SyncIUOM {
  string item_number = 1;
  string code = 2;
  sint32 quantity_per_unit_of_measure = 4;
  string description = 5;
  int32 type = 6;
  int32 status = 7;
  google.protobuf.Timestamp last_date_modified = 8;
  string login_id = 9;
  int32 block = 10;
}

message SyncUnitOfMeasureReply{
  repeated SyncUnitOfMeasure listUOM = 1;
  bool  isSuccess = 2;
  string msg = 3;
}

message SyncUnitOfMeasure {
  string Code = 1;
  string Description = 2;
  int32 Status = 3;
}