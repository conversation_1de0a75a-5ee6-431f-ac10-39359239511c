@page "/UploadFileInModal"
<MudDialog  Visible="true">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-3" /> Chose file(s)
        </MudText>
    </TitleContent>
    <DialogContent>
        <p>How awesome are inline dialogs?</p>
        @* <MudRating @bind-SelectedValue="_rating" Class="mt-3" /> *@
    </DialogContent>
    <DialogActions>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="@(_ => { })" Class="px-10">Close</MudButton>
    </DialogActions>
</MudDialog>