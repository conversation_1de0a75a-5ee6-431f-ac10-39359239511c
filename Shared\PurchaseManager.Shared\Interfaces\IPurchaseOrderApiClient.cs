﻿using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.QueueGenPO;
using PurchaseManager.Shared.Dto.User;
using PurchaseManager.Shared.Models.PO;
namespace PurchaseManager.Shared.Interfaces;

public interface IPurchaseOrderApiClient
{
    /// <summary>
    ///     Create a header by vendor number
    /// </summary>
    /// <param name="vendorNumber"></param>
    /// <param name="contactNumber"></param>
    /// <returns></returns>
    Task<ApiResponseDto<POHeaderGetDto>> CreateHeaderAsync(string vendorNumber, string contactNumber);
    Task<ApiResponseDto<List<CheckPaymentDayResponse>>> CheckPaymentDayByVendorNumberAsync(List<string> listVendorNumber);
    Task<ApiResponseDto<List<CheckPaymentDayResponse>>> CheckPaymentDayByItemNumberAsync(List<string> listItemNumber);
    Task<ApiResponseDto<List<CheckPaymentDayResponse>>> CheckPaymentDayBySuggestedNumberAsync(List<string> listSuggestedNumber);
    Task<ApiResponseDto<POHeaderGetDto>> GetHeader(string purchaseOrderNumber);
    Task<ApiResponseDto<List<POLineGetDto>>> GetLines(string purchaseOrderNumber);
    Task<ApiResponseDto<int>> UpdatePurchaseOrderLine(POLineAddOrUpdate updateLine);
    Task<ApiResponseDto<POLineGetDto>> AddLine(POLineAddOrUpdate detailPos);
    Task<ApiResponseDto<List<POLineGetDto>>> AddMultipleLines(List<POLineAddOrUpdate> detailPos);
    Task<ApiResponseDto> DeleteLine(string documentNumber, string itemNumber, int rowId);
    Task<ApiResponseDto<GetLatestPriceDto>> GetLatestPrice(string itemCode, string unit, string vendorCode);
    Task<ApiResponseDto<int>> UpdateHeader(UpdatePOHeaderDto header);
    Task<ApiResponseDto<int>> ConfirmHeader(string purchaseOrderNumber);
    Task<ApiResponseDto<int>> ApproveHeader(string purchaseOrderNumber);
    Task<ApiResponseDto<int>> RejectHeader(string purchaseOrderNumber);
    Task<ApiResponseDto<int>> CloseDocument(string purchaseOrderNumber);
    Task<ApiResponseDto<int>> OpenDocument(string purchaseOrderNumber);
    Task<ApiResponseDto<int>> StockOrderOpenDocument(string purchaseOrderNumber);
    Task<ApiResponseDto<int>> CompletedHeader(string purchaseOrderNumber, bool condition);
    Task<ApiResponseDto<int>> ChangeDocumentStatus(string documentNumber, int status);
    Task<ApiResponseDto> DownloadPo(string purchaseOrderNumber);
    Task<ApiResponseDto<UserInfoDto>> GetUserCreatedPOByPONumberAsync(string poNumber);
    Task<ApiResponseDto<PagedResult<POHeaderGetDto>>> GetPoHeadersAsync(PurchaseOrderFilter filter);
    Task<ApiResponseDto> DeleteMultipleAsync(List<string> purchaseCode);
    Task<ApiResponseDto> CreateDemand(List<CreateQueueGenPoDemandDto> detailPoDtos);
    Task<ApiResponseDto> UpdatePurchaseOrder(UpdatePoDto updatePoDto);
}
