﻿@inherits EditItemPage
@using System.Text.RegularExpressions
@using System.ComponentModel.DataAnnotations
@using MudBlazor;
@using PurchaseManager.Theme.Material.Demo.Shared.Components
@using PurchaseManager.Shared.Interfaces;
@using PurchaseManager.Shared.Localizer;
@using PurchaseManager.Shared.Models.Account;
@using PurchaseManager.Shared.Providers;
@using PurchaseManager.Shared.Services;
@using Microsoft.AspNetCore.Components;
@using PurchaseManager.Shared.Dto.Item;
@using Microsoft.AspNetCore.Components.Authorization;
@using Microsoft.Extensions.Localization;
@page "/item/{DetailItemDtoId}/create"
@page "/item/{DetailItemDtoId}"
@attribute [Authorize]

<PageTitle>Item List - Server Side Filtering, Sorting and Pagination example</PageTitle>
@if (isLoading)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else if (isNotFound)
{
    <PageNotFound />
}
else
{
    <div class="relative mb-3">
        <MudButton Color="Color.Tertiary" StartIcon="@Icons.Material.Filled.ArrowBack" Class="absolute"
                   OnClick="@(() => { navigation.NavigateTo("/items"); })" Variant="Variant.Text">
            Back to list
        </MudButton>
        <MudText Typo="Typo.h5" Align="Align.Center" Class="mx-auto">
            @(isCreate ? "Create New" : "") Item
            @(isCreate ? "" : "Detail")
        </MudText>
    </div>
    <div class="mb-4">
        <EditForm EditContext="editContext">
            <DataAnnotationsValidator />
            <MudCard>
                <MudCardHeader Class="@((isEditing ? "justify-space-between" : "justify-end") + " mt-2 ")">
                    @if (isEditing)
                    {
                        <MudButton StartIcon="@Icons.Material.Filled.DisabledByDefault" ButtonType="ButtonType.Submit"
                                   Variant="Variant.Filled" OnClick="@ResetForm" Color="Color.Error" Class="m-auto">
                            @L["Discard"]
                        </MudButton>
                        @if (isItemSaving)
                        {
                            <div>
                                <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                                <MudText Class="ms-2">@L["Saving"]</MudText>
                            </div>
                        }
                        else
                        {
                            <MudButton StartIcon="@Icons.Material.Filled.Save"
                                       Disabled="@((!editContext.IsModified()) && !editContext.Validate())" ButtonType="ButtonType.Submit"
                                       Variant="Variant.Filled" OnClick="OnSaveItemClick" Color="Color.Primary" Class="m-auto ">
                                <MudText>@L["Save"]</MudText>
                            </MudButton>
                        }

                    }
                    @if (!isEditing)
                    {
                        <MudButton StartIcon="@Icons.Material.Filled.ModeEditOutline" ButtonType="ButtonType.Button"
                                   Variant="Variant.Filled" Color="Color.Surface" Class="m-auto" OnClick="OnEditClick">
                            @L["Edit"]
                        </MudButton>
                    }
                </MudCardHeader>
                <MudGrid>
                    <MudItem sm="4">
                        <MudCardContent>
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="Name"
                                          @bind-Value="item.Name" For="@(() => item.Name)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="Description"
                                          @bind-Value="item.Description" For="@(() => item.Description)" />
                            <MudTextField ReadOnly="true" TextUpdateSuppression="false" Label="BaseUnitOfMeasure"
                                          @bind-Value="item.BaseUnitOfMeasure" For="@(() => item.BaseUnitOfMeasure)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="InventoryPostingGroup"
                                          @bind-Value="item.InventoryPostingGroup" For="@(() => item.InventoryPostingGroup)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="ItemCategoryCode"
                                          @bind-Value="item.ItemCategoryCode" For="@(() => item.ItemCategoryCode)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="VatProductPostingGroup"
                                          @bind-Value="item.VatProductPostingGroup" For="@(() => item.VatProductPostingGroup)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="ItemDiscountGroup"
                                          @bind-Value="item.ItemDiscountGroup" For="@(() => item.ItemDiscountGroup)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="ManufacturerCode"
                                          @bind-Value="item.ManufacturerCode" For="@(() => item.ManufacturerCode)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="CountryOfOriginCode"
                                          @bind-Value="item.CountryOfOriginCode" For="@(() => item.CountryOfOriginCode)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="SalaryGroup"
                                          @bind-Value="item.SalaryGroup" For="@(() => item.SalaryGroup)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="TaxGroupCode"
                                          @bind-Value="item.TaxGroupCode" For="@(() => item.TaxGroupCode)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="CommissionGroup"
                                          @bind-Value="item.CommissionGroup" For="@(() => item.CommissionGroup)" />
                            <MudNumericField Format="N2" T="Int32?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="StatisticsGroup"
                                             @bind-Value="item.StatisticsGroup" For="@(() => item.StatisticsGroup)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="LotSize"
                                             @bind-Value="item.LotSize" For="@(() => item.LotSize)" />
                            <MudNumericField Format="N2" T="Int32?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="AllowInvoiceDiscount"
                                             @bind-Value="item.AllowInvoiceDiscount" For="@(() => item.AllowInvoiceDiscount)" />
                            <MudNumericField Format="N2" T="Int32?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="PriceProfitCalculation"
                                             @bind-Value="item.PriceProfitCalculation" For="@(() => item.PriceProfitCalculation)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="ProfitPercent"
                                             @bind-Value="item.ProfitPercent" For="@(() => item.ProfitPercent)" />
                            <MudNumericField Format="N2" T="Int32?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="CostingMethod"
                                             @bind-Value="item.CostingMethod" For="@(() => item.CostingMethod)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="UnitPrice"
                                             @bind-Value="item.UnitPrice" For="@(() => item.UnitPrice)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="UnitCost"
                                             @bind-Value="item.UnitCost" For="@(() => item.UnitCost)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="StandardCost"
                                             @bind-Value="item.StandardCost" For="@(() => item.StandardCost)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="LeadTimeCalculation"
                                          @bind-Value="item.LeadTimeCalculation" For="@(() => item.LeadTimeCalculation)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="ReorderPoint"
                                             @bind-Value="item.ReorderPoint" For="@(() => item.ReorderPoint)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="MaximumInventory"
                                             @bind-Value="item.MaximumInventory" For="@(() => item.MaximumInventory)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="ReorderQuantity"
                                             @bind-Value="item.ReorderQuantity" For="@(() => item.ReorderQuantity)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="AlternativeItemNo"
                                          @bind-Value="item.AlternativeItemNo" For="@(() => item.AlternativeItemNo)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="ImportLicenseNumber"
                                          @bind-Value="item.ImportLicenseNumber" For="@(() => item.ImportLicenseNumber)" />
                        </MudCardContent>
                    </MudItem>
                    <MudItem sm="4">
                        <MudCardContent>
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="GrossWeight"
                                             @bind-Value="item.GrossWeight" For="@(() => item.GrossWeight)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="NetWeight"
                                             @bind-Value="item.NetWeight" For="@(() => item.NetWeight)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="UnitsPerParcel"
                                             @bind-Value="item.UnitsPerParcel" For="@(() => item.UnitsPerParcel)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="UnitVolume"
                                             @bind-Value="item.UnitVolume" For="@(() => item.UnitVolume)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="VendorNumber"
                                          @bind-Value="item.VendorNumber" For="@(() => item.VendorNumber)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="VendorItemNumber"
                                          @bind-Value="item.VendorItemNumber" For="@(() => item.VendorItemNumber)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="FreightType"
                                          @bind-Value="item.FreightType" For="@(() => item.FreightType)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="TariffNumber"
                                          @bind-Value="item.TariffNumber" For="@(() => item.TariffNumber)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="BudgetQuantity"
                                             @bind-Value="item.BudgetQuantity" For="@(() => item.BudgetQuantity)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="BudgetedAmount"
                                             @bind-Value="item.BudgetedAmount" For="@(() => item.BudgetedAmount)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="BudgetProfit"
                                             @bind-Value="item.BudgetProfit" For="@(() => item.BudgetProfit)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="Blocked"
                                          @bind-Value="item.Blocked" For="@(() => item.Blocked)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="MinimumOrderQuantity"
                                             @bind-Value="item.MinimumOrderQuantity" For="@(() => item.MinimumOrderQuantity)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="MaximumOrderQuantity"
                                             @bind-Value="item.MaximumOrderQuantity" For="@(() => item.MaximumOrderQuantity)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="SafetyStockQuantity"
                                             @bind-Value="item.SafetyStockQuantity" For="@(() => item.SafetyStockQuantity)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="OrderMultiple"
                                             @bind-Value="item.OrderMultiple" For="@(() => item.OrderMultiple)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="SafetyLeadTime"
                                          @bind-Value="item.SafetyLeadTime" For="@(() => item.SafetyLeadTime)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="SalesUnitOfMeasure"
                                          @bind-Value="item.SalesUnitOfMeasure" For="@(() => item.SalesUnitOfMeasure)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="PurchaseUnitOfMeasure"
                                          @bind-Value="item.PurchaseUnitOfMeasure" For="@(() => item.PurchaseUnitOfMeasure)" />
                            <MudNumericField Format="N2" T="Int32?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="ManufacturingPolicy"
                                             @bind-Value="item.ManufacturingPolicy" For="@(() => item.ManufacturingPolicy)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="ExpirationCalculation"
                                          @bind-Value="item.ExpirationCalculation" For="@(() => item.ExpirationCalculation)" />
                            <MudNumericField Format="N2" T="Int32?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="NonStock"
                                             @bind-Value="item.NonStock" For="@(() => item.NonStock)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="QuotaNumber"
                                          @bind-Value="item.QuotaNumber" For="@(() => item.QuotaNumber)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="QuotaQuantity"
                                             @bind-Value="item.QuotaQuantity" For="@(() => item.QuotaQuantity)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="QuotaAddedNumber"
                                          @bind-Value="item.QuotaAddedNumber" For="@(() => item.QuotaAddedNumber)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="QuotaAddedQuantity"
                                             @bind-Value="item.QuotaAddedQuantity" For="@(() => item.QuotaAddedQuantity)" />
                            <MudSelect T="ColorDto" 
                                       ReadOnly="@(!isEditing)" 
                                       Label="SKU Attributes"
                                       ToStringFunc="converterColorDto"
                                       MultiSelection="true"
                                       @bind-SelectedValues="selectedColor">
                                @foreach (var state in listColorDto)
                                {
                                    <MudSelectItem T="ColorDto" Value="@state"><span class="py-2 px-4 mr-3" style="@($"background-color: {state.ColorCode}")"></span> @state.ColorName</MudSelectItem>
                                }
                            </MudSelect>
                        </MudCardContent>
                    </MudItem>
                    <MudItem sm="4">
                        <MudCardContent>
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="Name2"
                                          @bind-Value="item.Name2" For="@(() => item.Name2)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="Name3"
                                          @bind-Value="item.Name3" For="@(() => item.Name3)" />
                            <MudDatePicker ReadOnly="@(!isEditing)" Label="VisaIssuedDate"
                                           @bind-Date="itemVisaIssuedDate" />
                            <MudDatePicker ReadOnly="@(!isEditing)" Label="ExpirationDate"
                                           @bind-Date="itemExpirationDate" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="Standard"
                                          @bind-Value="item.Standard" For="@(() => item.Standard)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="VendorAuthorizationNumber"
                                          @bind-Value="item.VendorAuthorizationNumber" For="@(() => item.VendorAuthorizationNumber)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="CompanyRegistration"
                                          @bind-Value="item.CompanyRegistration" For="@(() => item.CompanyRegistration)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="QualityMeasureCode"
                                          @bind-Value="item.QualityMeasureCode" For="@(() => item.QualityMeasureCode)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="SearchName"
                                          @bind-Value="item.SearchName" For="@(() => item.SearchName)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="VisaIssuedNumber"
                                          @bind-Value="item.VisaIssuedNumber" For="@(() => item.VisaIssuedNumber)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="PlaceOfOriginCode"
                                          @bind-Value="item.PlaceOfOriginCode" For="@(() => item.PlaceOfOriginCode)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="ImportLicenseQuantity"
                                             @bind-Value="item.ImportLicenseQuantity" For="@(() => item.ImportLicenseQuantity)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="MinimumInventory"
                                             @bind-Value="item.MinimumInventory" For="@(() => item.MinimumInventory)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="CategoryNo"
                                          @bind-Value="item.CategoryNo" For="@(() => item.CategoryNo)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="CategoryTypeNo"
                                          @bind-Value="item.CategoryTypeNo" For="@(() => item.CategoryTypeNo)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="CategoryGroupNo"
                                          @bind-Value="item.CategoryGroupNo" For="@(() => item.CategoryGroupNo)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="Importer"
                                          @bind-Value="item.Importer" For="@(() => item.Importer)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="Visa"
                                          @bind-Value="item.Visa" For="@(() => item.Visa)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="Label"
                                          @bind-Value="item.Label" For="@(() => item.Label)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="ItemLocation"
                                          @bind-Value="item.ItemLocation" For="@(() => item.ItemLocation)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="UnitPriceRegis"
                                             @bind-Value="item.UnitPriceRegis" For="@(() => item.UnitPriceRegis)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="MainIngredient"
                                          @bind-Value="item.MainIngredient" For="@(() => item.MainIngredient)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="RegistrationNo"
                                          @bind-Value="item.RegistrationNo" For="@(() => item.RegistrationNo)" />
                            <MudTextField ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="BinCode"
                                          @bind-Value="item.BinCode" For="@(() => item.BinCode)" />
                            <MudNumericField Format="N2" T="Int32?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="Status"
                                             @bind-Value="item.Status" For="@(() => item.Status)" />
                            <MudNumericField Format="N2" T="Int32?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="Type"
                                             @bind-Value="item.Type" For="@(() => item.Type)" />
                            <MudNumericField Format="N2" T="Decimal?" Min="1" HideSpinButtons ReadOnly="@(!isEditing)" TextUpdateSuppression="false" Label="ImportLicenseQuantity"
                                             @bind-Value="item.ImportLicenseQuantity" For="@(() => item.ImportLicenseQuantity)" />
                        </MudCardContent>
                    </MudItem>
                    <MudItem sm="12" Class="pt-0">
                        <MudCardContent Class="pt-0">
                            <div class="mb-3">
                                <MudTable CanCancelEdit="true" ReadOnly="@(!isEditing)" Context="IUOMContext" T="DetailItemUnitOfMeasureDto" Items="@listDetailItemUnitOfMeasureDto"
                                          Striped="true" Hover="true" Class="rounded-0 pa-2" Elevation="4"
                                          LoadingProgressColor="Color.Info" RowClass="" @bind-SelectedItem="detailItemUnitOfMeasureDtoSelected"
                                          RowEditPreview="BackupIUOM" RowEditCancel="ResetIUOMToOriginalValues"
                                          RowEditCommit="IUOMHasBeenCommitted" Dense="true"
                                          SortLabel="Sort By" CommitEditTooltip="@(L["Save"])" IsEditRowSwitchingBlocked
                                          ApplyButtonPosition="TableApplyButtonPosition.Start"
                                          EditButtonPosition="TableEditButtonPosition.Start"
                                          EditTrigger="TableEditTrigger.EditButton">
                                    <ToolBarContent>
                                        <MudText Typo="Typo.h6">@L["List Item unit of measure"]</MudText>
                                        <MudSpacer />
                                        @if (isEditing)
                                        {
                                            <MudFab OnClick="ShowUOMFrom" Icon="@Icons.Material.Filled.Add" Size="Size.Small"
                                                    Color="Color.Tertiary" />
                                        }
                                    </ToolBarContent>
                                    <HeaderContent>
                                        <MudTh>
                                            <MudTableSortLabel SortLabel="Code" T="DetailItemUnitOfMeasureDto">
                                                Code
                                            </MudTableSortLabel>
                                        </MudTh>
                                        <MudTh>
                                            <MudTableSortLabel SortLabel="QuantityPerUnitOfMeasure"
                                                               T="DetailItemUnitOfMeasureDto">
                                                QuantityPerUnitOfMeasure
                                            </MudTableSortLabel>
                                        </MudTh>
                                        <MudTh>
                                            <MudTableSortLabel SortLabel="Description" T="DetailItemUnitOfMeasureDto">
                                                Description
                                            </MudTableSortLabel>
                                        </MudTh>
                                        <MudTh>
                                            <MudTableSortLabel SortLabel="Block" T="DetailItemUnitOfMeasureDto">
                                                Block
                                            </MudTableSortLabel>
                                        </MudTh>
                                    </HeaderContent>
                                    <RowTemplate Context="row">
                                        <MudTd DataLabel="Code">@row.Code</MudTd>
                                        <MudTd DataLabel="QuantityPerUnitOfMeasure">@row.QuantityPerUnitOfMeasure.ToString("N2")</MudTd>
                                        <MudTd DataLabel="Description">@row.Description</MudTd>
                                        <MudTd DataLabel="Block">
                                            @if (@row.Block == 0)
                                            {
                                                <MudIconButton Icon="@Icons.Material.Filled.Check" Size="Size.Small"
                                                               Color="Color.Primary" />
                                            }
                                        </MudTd>
                                    </RowTemplate>
                                    <RowEditingTemplate>
                                        <MudTd DataLabel="Code">@IUOMContext.Code</MudTd>
                                        <MudTd DataLabel="QuantityPerUnitOfMeasure">
                                            <MudNumericField Format="N2" Min="1" HideSpinButtons @bind-Value="@IUOMContext.QuantityPerUnitOfMeasure" Required />
                                        </MudTd>
                                        <MudTd>
                                            @IUOMContext.Description
                                        </MudTd>
                                        <MudTd DataLabel="Block">
                                            @if (@IUOMContext.Block == 0)
                                            {
                                                <MudIconButton Icon="@Icons.Material.Filled.Check" Size="Size.Small"
                                                               Color="Color.Primary" />
                                            }
                                        </MudTd>
                                    </RowEditingTemplate>
                                    <NoRecordsContent>
                                        <MudText>@L["No matching records found"]</MudText>
                                    </NoRecordsContent>
                                    <LoadingContent>
                                        <MudText>@L["Loading..."]</MudText>
                                    </LoadingContent>
                                    <PagerContent>
                                        <MudTablePager RowsPerPageString=@L["Rows per page"] />
                                    </PagerContent>
                                </MudTable>
                            </div>
                            <MudTable CanCancelEdit="true" ReadOnly="@(!isEditing)" Context="salePriceContext" T="DetailSalesPriceDto"
                                      Items="@listSalesPrices" Striped="true" @bind-SelectedItem="salePriceSelected"
                                      RowEditPreview="BackupSalePrice" RowEditCancel="ResetSalePriceToOriginalValues"
                                      RowEditCommit="SalePriceHasBeenCommitted" Dense="true" Hover="true" Class="rounded-0 pa-2"
                                      Elevation="4" LoadingProgressColor="Color.Info" RowClass="" SortLabel="Sort By"
                                      CommitEditTooltip="@(L["Save"])" IsEditRowSwitchingBlocked
                                      ApplyButtonPosition="TableApplyButtonPosition.Start"
                                      EditButtonPosition="TableEditButtonPosition.Start"
                                      EditTrigger="TableEditTrigger.EditButton">
                                <ToolBarContent>
                                    <MudText Typo="Typo.h6">@L["List Sale Price"]</MudText>
                                    <MudSpacer />
                                    <MudSpacer />
                                    @if (isEditing && listDetailItemUnitOfMeasureDto.Any())
                                    {
                                        <MudFab OnClick="ShowSalePriceForm" Icon="@Icons.Material.Filled.Add" Size="Size.Small" Color="Color.Tertiary" />
                                    }
                                </ToolBarContent>
                                <HeaderContent>
                                    <MudTh>
                                        <MudTableSortLabel Style="width:200px" SortLabel="UnitPrice" T="DetailSalesPriceDto">
                                            Unit Price
                                        </MudTableSortLabel>
                                    </MudTh>
                                    <MudTh>
                                        <MudTableSortLabel SortLabel="Quantity" T="DetailSalesPriceDto">
                                            Quantity
                                        </MudTableSortLabel>
                                    </MudTh>
                                    <MudTh>
                                        <MudTableSortLabel SortLabel="UnitOfMeasureCode" T="DetailSalesPriceDto">
                                            UnitOfMeasureCode
                                        </MudTableSortLabel>
                                    </MudTh>
                                    <MudTh>
                                        <MudTableSortLabel SortLabel="StartingDate" T="DetailSalesPriceDto">
                                            StartingDate
                                        </MudTableSortLabel>
                                    </MudTh>
                                    <MudTh>
                                        <MudTableSortLabel SortLabel="EndingDate" T="DetailSalesPriceDto">
                                            EndingDate
                                        </MudTableSortLabel>
                                    </MudTh>
                                    <MudTh>
                                        <MudTableSortLabel SortLabel="Description" T="DetailSalesPriceDto">
                                            Description
                                        </MudTableSortLabel>
                                    </MudTh>
                                    <MudTh>
                                        <MudTableSortLabel SortLabel="QuantityPerUnitOfMeasure" T="DetailSalesPriceDto">
                                            QuantityPerUnitOfMeasure
                                        </MudTableSortLabel>
                                    </MudTh>
                                    <MudTh>
                                        <MudTableSortLabel SortLabel="Block" T="DetailSalesPriceDto">
                                            Block
                                        </MudTableSortLabel>
                                    </MudTh>
                                </HeaderContent>
                                <RowTemplate Context="row">
                                    <MudTd DataLabel="UnitPrice">@row.UnitPrice.ToString("N2")</MudTd>
                                    <MudTd DataLabel="Quantity">@row.Quantity.ToString("N2")</MudTd>
                                    <MudTd DataLabel="UnitOfMeasureCode">@row.UnitOfMeasureCode</MudTd>
                                    <MudTd DataLabel="StartingDate">@row.StartingDate</MudTd>
                                    <MudTd DataLabel="EndingDate">@row.EndingDate</MudTd>
                                    <MudTd DataLabel="Description">@row.Description</MudTd>
                                    <MudTd DataLabel="QuantityPerUnitOfMeasure">@row.QuantityPerUnitOfMeasure.ToString("N2")</MudTd>
                                    <MudTd DataLabel="Block">
                                        @if (@row.Block == 0)
                                        {
                                            <MudIconButton Icon="@Icons.Material.Filled.Check" Size="Size.Small"
                                                           Color="Color.Primary" />
                                        }
                                    </MudTd>
                                </RowTemplate>
                                <RowEditingTemplate>
                                    <MudTd Style="width:200px" DataLabel="UnitPrice">
                                        <MudNumericField Format="N2" Min="1" HideSpinButtons @bind-Value="@salePriceContext.UnitPrice" Required />
                                    </MudTd>
                                    <MudTd DataLabel="Quantity">@salePriceContext.Quantity.ToString("N2")</MudTd>
                                    <MudTd DataLabel="UnitOfMeasureCode">@salePriceContext.UnitOfMeasureCode</MudTd>
                                    <MudTd DataLabel="StartingDate">@salePriceContext.StartingDate</MudTd>
                                    <MudTd DataLabel="EndingDate">@salePriceContext.EndingDate</MudTd>
                                    <MudTd DataLabel="Description"><MudTextField @bind-Value="@salePriceContext.Description" Required /></MudTd>
                                    <MudTd DataLabel="QuantityPerUnitOfMeasure">
                                        @salePriceContext.QuantityPerUnitOfMeasure.ToString("N2")
                                    </MudTd>
                                    <MudTd DataLabel="Block">@salePriceContext.Block </MudTd>
                                </RowEditingTemplate>
                                <NoRecordsContent>
                                    <MudText>@L["No matching records found"]</MudText>
                                </NoRecordsContent>
                                <LoadingContent>
                                    <MudText>@L["Loading..."]</MudText>
                                </LoadingContent>
                                <PagerContent>
                                    <MudTablePager RowsPerPageString=@L["Rows per page"] />
                                </PagerContent>
                                <NoRecordsContent>
                                    <MudText>@L["No matching records found"]</MudText>
                                </NoRecordsContent>
                                <LoadingContent>
                                    <MudText>@L["Loading..."]</MudText>
                                </LoadingContent>
                                <EditButtonContent Context="button">
                                    <MudIconButton Size="@Size.Small" Icon="@Icons.Material.Outlined.Edit" Class="pa-0"
                                                   OnClick="@button.ButtonAction" Disabled="@button.ButtonDisabled" />
                                </EditButtonContent>
                            </MudTable>
                        </MudCardContent>
                    </MudItem>
                </MudGrid>
            </MudCard>
        </EditForm>
    </div>

    <MudDialog @bind-Visible="@isShowAddUOMForm">
        <TitleContent>
            <MudText Typo="Typo.h6">Add new Unit Of Measure for Item <b class="text-primary">@item.Name</b></MudText>
        </TitleContent>
        <DialogContent>
            <MudSelect T="UnitOfMeasureDto" ToStringFunc="@converter" Label="Other Unit Of Measure"
                       AnchorOrigin="Origin.BottomCenter"
                       @bind-Value="unitOfMeasureSelected" Clearable>
                @foreach (var o in listUnitOfMeasureWithoutDefault)
                {
                    <MudSelectItem Value="@o" />
                }
            </MudSelect>
            <MudNumericField Format="N2" Min="1" Label="Quantity per unit of measure" HideSpinButtons
                             @bind-Value="QuantityPerUnitOfMeasureToAdd" Required />
        </DialogContent>
        <DialogActions>

            <MudButton OnClick="@OnSaveNewUOM" Variant="Variant.Filled" Color="Color.Primary">@L["Save"]</MudButton>
        </DialogActions>
    </MudDialog>
    <MudDialog @bind-Visible="@isShowAddSalePriceForm" Options="@salePriceDialogOptions">
        <TitleContent>
            <MudText Typo="Typo.h6">Add new Sale Price for Item <b class="text-primary">@item.Name</b></MudText>
        </TitleContent>
        <DialogContent>
            <SalesPricePage ListUnitOfMeasureSelected="listUnitOfMeasureSelected" SalesPrice="@salePriceToAdd" />
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="@OnSaveNewSalePrice" Disabled="@isSalePriceSaving" Variant="Variant.Filled" Color="Color.Primary">
                @if (isSalePriceSaving)
                {
                    <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                    <MudText Class="ms-2">@L["Saving"]</MudText>
                }
                else
                {
                    <MudText>@L["Save"]</MudText>
                }
            </MudButton>
        </DialogActions>
    </MudDialog>
}
@code {


    //private City value { get; set; } = new City() { Name = "FU" };
    //private IEnumerable<City> options { get; set; } = new HashSet<City>() { new City() { Name = "FU" } };

    //private City[] states =
    //{
    //   new City(){Name ="FU"},
    //   new City(){Name="FUU"},
    //   new City(){Name="FFUU"}
    //};









}