﻿namespace PurchaseManager.Shared.Dto.PO;

public class DetailPoDto
{
    public string VendorNumber { get; set; } = null!;

    public string ItemNumber { get; set; } = null!;

    public string? ItemName { get; set; }
    public string? VendorName { get; set; }

    public int RowId { get; set; }

    public int Min { get; set; }

    public int Max { get; set; }

    public int? Stock { get; set; }

    public int Suggest { get; set; }
    public int TotalOrder { get; set; }

    public int Quantity { get; set; }

    public DateTime CreateDate { get; set; }

}
