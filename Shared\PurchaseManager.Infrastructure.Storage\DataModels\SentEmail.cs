using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

public class SentEmail
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }

    public string Body { get; set; }

    [Required]
    [StringLength(100)]
    public string SentBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime SentAt { get; set; }

    /// <summary>
    /// DetailPO
    /// Confirmation
    /// Generic
    /// Password
    /// ...
    /// </summary>
    public int Type { get; set; }

    [StringLength(10)]
    public string VendorNumber { get; set; }

    [StringLength(100)]
    public string PoNumber { get; set; }
}
