﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Account;
namespace PurchaseManager.Theme.Material.Admin.Pages.Admin;

public partial class VendorsPage : ComponentBase
{
    [Inject]
    private IViewNotifier viewNotifier { get; set; }
    [Inject]
    private IApiClient apiClient { get; set; }

    [Inject] protected IStringLocalizer<Global> L { get; set; }
    protected bool isLoading { get; set; }
    protected bool isDownloading { get; set; }
    protected bool isAllBlocked { get; set; }
    protected bool isAllDeactivate { get; set; }
    protected bool showConfirmActivate { get; set; }
    protected bool showToggleBlock { get; set; }
    protected bool isShowConfirmResetPass { get; set; }
    [Inject] public IJSRuntime jSRuntime { get; set; }

    protected UserVendorViewModel selectedVendorUser { get; set; } = new UserVendorViewModel();
    protected AccountVendorFilter filter { get; set; } = new AccountVendorFilter();

    protected List<UserVendorViewModel> listVendorUser { get; set; } = new List<UserVendorViewModel>();

    protected int pageSize { get; set; } = 10;
    protected int pageIndex { get; set; } = 0;
    protected int totalItemsCount { get; set; } = 0;
    protected async Task OnPage(int index, int size)
    {
        filter.PageSize = size;
        filter.PageIndex = index;

        await LoadAllVendorUser();
    }

    protected virtual async Task Reload() => await LoadAllVendorUser();
    protected async Task OnShowAllDeactivateAccountClick(bool value)
    {
        try
        {
            isLoading = true;

            isAllDeactivate = value;
            filter.AllDeactivate = value ? true : null;
            await Reload();
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        finally
        {
            isLoading = false;
        }
    }
    protected async Task OnShowAllBlockedAccountClick(bool value)
    {
        try
        {
            isLoading = true;
            isAllBlocked = value;
            filter.AllBlocked = value ? true : null;// null is get all
            await Reload();
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        finally
        {
            isLoading = false;
        }
    }

    protected async Task LoadAllVendorUser()
    {
        try
        {
            isLoading = true;
            var resp = await apiClient.GetsAccountVendor(filter);
            if (resp.IsSuccessStatusCode)
            {
                listVendorUser = resp.Result.Data.ToList();
                totalItemsCount = resp.Result.RowCount;
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["UserCreationFailed"]);
        }
        finally
        {
            isLoading = false;
        }
    }
    protected async Task VerifyUser()
    {
        isLoading = true;
        try
        {
            var resp = await apiClient.ActiveAccountVendor(selectedVendorUser.UserName);
            viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation Successfully"]);
            await Reload();
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["UserCreationFailed"]);
        }
        finally
        {
            isLoading = false;
            showConfirmActivate = false;
        }
    }

    protected async Task ResetVendorAccountPassword()
    {
        try
        {
            var resp = await apiClient.ResetVendorPassword(selectedVendorUser.UserName);
            viewNotifier.Show(resp.Message, resp.IsSuccessStatusCode ? ViewNotifierType.Success : ViewNotifierType.Error,
            resp.IsSuccessStatusCode ? L["Operation Successfully"] : L["Operation Failed"]);
            await Reload();
            isShowConfirmResetPass = false;
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["ResetPassUserFailed"]);
        }
        finally
        {
            isLoading = false;
        }
    }


    protected async Task GetReportVendorAccount()
    {
        try
        {
            isDownloading = true;
            var fileName = "PO_Vendor_Account_" + DateTime.UtcNow.ToString("dd-MM-yyyy") + "_" + DateTime.Now.ToString("HH-mm-ss") +
                           ".xlsx";

            var resp = await apiClient.GetReportVendorAccount();
            await jSRuntime.InvokeVoidAsync("downloadFileFromBase64", fileName, resp.Result);
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["ToggleUserFailed"]);
        }
        finally
        {
            isDownloading = false;
        }
    }
    protected async Task ToggleUser()
    {
        isLoading = true;
        try
        {
            var resp = await apiClient.BlockAccountVendor(selectedVendorUser.UserName);
            viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation Successfully"]);
            await Reload();
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["ToggleUserFailed"]);
        }
        finally
        {
            isLoading = false;
            showToggleBlock = false;
        }
    }

}
