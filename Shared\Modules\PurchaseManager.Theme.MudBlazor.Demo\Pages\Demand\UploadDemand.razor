﻿@page "/po/demand"
@using PurchaseManager.Shared.Dto.QueueGenPO
<MudText Typo="Typo.h4" Class="my-4" Align="Align.Center">@L["Upload Demand"]</MudText>
@if (!IsFileRead)
{
    @if (IsFileLoading)
    {
        <MudAlert Severity="Severity.Info" ContentAlignment="HorizontalAlignment.Left">Progressing...</MudAlert>
    }
    else
    {
        <MudAlert Severity="Severity.Info" ContentAlignment="HorizontalAlignment.Left">
            <MudStack Row Spacing="5" Justify="Justify.SpaceAround" AlignItems="AlignItems.Stretch">
                <MudText Class="mud-underline">@L["Upload Demand to create PO"]</MudText>
                <MudText>
                    @L["Download sample file "]
                    <MudLink Underline="Underline.Always" Href="/files/Import_Demand_V1_SampleData_10062025.xlsx">
                        @L["here"]
                    </MudLink>
                </MudText>
            </MudStack>
        </MudAlert>
        <MudFileUpload T="IBrowserFile" Class="mt-4" Accept=".xlsx" MaximumFileCount="1" FilesChanged="UploadFiles">
            <ActivatorContent>
                <MudButton Style="width: 100%" Variant="Variant.Filled" Color="Color.Primary"
                    StartIcon="@Icons.Material.Filled.CloudUpload">
                    @L["Upload"]
                </MudButton>
            </ActivatorContent>
        </MudFileUpload>
    }
}
else
{
    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
        <MudButton Variant="Variant.Filled" Color="Color.Error" OnClick="Clear" Disabled="@IsCreatePO"
            StartIcon="@Icons.Material.Filled.Clear">
            @L["Clear"]
        </MudButton>
        @if (DetailPoDtos.All(x => x.Quantity != 0) && !HasUploadError)
        {
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="CheckPaymentDayByVendorNumber"
                Disabled="@(IsCreatePO)" StartIcon="@Icons.Material.Filled.Add">
                @L["Create PO"]
            </MudButton>
        }
        @if (DetailPoDtos.Any(x => x.Quantity == 0))
        {
            <MudAlert Severity="Severity.Warning" ContentAlignment="HorizontalAlignment.Left">
                @L["Duplicate data. Please check again"]
            </MudAlert>
        }
    </MudStack>
}
@if (IsCreatePO)
{
    <MudStack Row Justify="Justify.Center" class="mt-4">
        <MudOverlay @bind-Visible="IsCreatePO" LightBackground ZIndex="9999">
            <MudText Typo="Typo.h6">@L["Creating PO. Please wait"]</MudText>
            <MudProgressLinear Color="Color.Info" Indeterminate="true" />
        </MudOverlay>
    </MudStack>
}
@if (DetailPoDtos.Any())
{
    <MudStack Class="my-2">
        <MudTable T="CreateQueueGenPoDemandDto" Items="@DetailPoDtos" FixedHeader="true" FixedFooter="true" Hover="true"
            Breakpoint="Breakpoint.Sm" LoadingProgressColor="Color.Info">
            <HeaderContent>
                <MudTh>
                    <MudTableSortLabel SortLabel="VendorNumber" T="CreateQueueGenPoDemandDto">Vendor Number
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="BrandCode" T="CreateQueueGenPoDemandDto">Brand code
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="ItemNumber" T="CreateQueueGenPoDemandDto">Item Number
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="Quantity" T="CreateQueueGenPoDemandDto">Quantity
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="PurchaseUnitOfMeasure" T="CreateQueueGenPoDemandDto">Purchase Unit Of
                        Measure
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="Type" T="CreateQueueGenPoDemandDto">
                        Type
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="Price(-VAT)" T="CreateQueueGenPoDemandDto">
                        Price(-VAT)
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="VAT" T="CreateQueueGenPoDemandDto">
                        VAT
                    </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="Description" T="CreateQueueGenPoDemandDto">
                        Description
                    </MudTableSortLabel>
                </MudTh>
            </HeaderContent>
            <RowTemplate Context="row">
                <MudTd DataLabel="VendorNumber">
                    <p>@row.VendorNumber</p>
                </MudTd>
                <MudTd DataLabel="BrandCode">
                    <p>@row.BrandCode</p>
                </MudTd>
                <MudTd DataLabel="ItemNumber">
                    <MudText>
                        @row.ItemNumber
                        @if (_duplicateItems.Find(x => x.ItemNumber == row.ItemNumber) is not null)
                        {
                            <MudIconButton Size="Size.Small" Icon="@Icons.Material.Filled.Info" Color="Color.Error">
                            </MudIconButton>
                        }
                    </MudText>
                </MudTd>
                <MudTd DataLabel="Quantity">
                    @if (row.Quantity == 0)
                    {
                        <MudAlert ShowCloseIcon="false" Severity="Severity.Error" Dense="true"> @row.Quantity</MudAlert>
                    }
                    else
                    {
                        @row.Quantity
                    }
                </MudTd>
                <MudTd DataLabel="Purchase Unit Of Measure">
                    <MudText>@row.PurchaseUnitOfMeasure</MudText>
                </MudTd>
                <MudTd DataLabel="Type">
                    @switch (row.DocumentType)
                    {
                        case (Int32)DocNoOccurrenceEnum.Order:
                            <MudText Color="Color.Primary">@L["Order"]</MudText>
                            break;
                        case (Int32)DocNoOccurrenceEnum.Consigned:
                            <MudText Color="Color.Secondary">@L["Consigned"]</MudText>
                            break;
                        default:
                            <MudText Color="Color.Error">@L["Promotional"]</MudText>
                            break;
                    }
                </MudTd>
                <MudTd DataLabel="Price(-VAT)">
                    <MudText>@row.PriceB4VAT</MudText>
                </MudTd>
                <MudTd DataLabel="VAT">
                    <MudText>@row.VAT</MudText>
                </MudTd>
                <MudTd DataLabel="Description">
                    <MudText>@row.Description</MudText>
                </MudTd>
            </RowTemplate>
        </MudTable>
    </MudStack>
}
<MudDialog @bind-Visible="@IsAlertDialogOpen" Options="@(new DialogOptions
                                              {
                                                  MaxWidth = MaxWidth.Medium,
                                                  FullWidth = true
                                              })">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.ReportProblem" Color="Color.Warning" Class="mr-3 mb-n1" />
            @L["There are a number of vendors who have PO available"]
        </MudText>
    </TitleContent>
    <DialogContent>
        <AlertPaymentDay Parameters="@ListCheckPaymentDayResponse"></AlertPaymentDay>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(_ => { IsAlertDialogOpen = false; })">@L["Cancel"]</MudButton>
        <MudButton OnClick="@(async () => await CreatePO())" Color="Color.Primary">@L["Continue create"]</MudButton>
    </DialogActions>
</MudDialog>
<MudDialog @bind-Visible="@IsVatErrorDialogOpen" Options="@(new DialogOptions
                                                 {
                                                     MaxWidth = MaxWidth.Medium,
                                                     FullWidth = true
                                                 })">
    <TitleContent>
        <MudText Typo="Typo.h6" Align="Align.Center">
            <MudIcon Icon="@Icons.Material.Filled.ReportProblem" Color="Color.Error" Class="mr-3 mb-n1" />
            Upload Demand Error
        </MudText>
    </TitleContent>
    <DialogContent>
        <MudGrid>
            <MudItem xs="6">
                <MudText Style="font-weight:bold;font-size:16px;white-space: pre-line;" Align="Align.Center"
                    Visible="@(!string.IsNullOrWhiteSpace(DuplicateErrorMessage))">Duplicate Item
                </MudText>
                @if (!string.IsNullOrWhiteSpace(DuplicateErrorMessage))
                {
                    <MudText Style="white-space: pre-line;" Html="true">@DuplicateErrorMessage</MudText>
                }
            </MudItem>
            <MudItem xs="6">
                <MudText Style="font-weight:bold;font-size:16px;white-space: pre-line;" Align="Align.Center"
                    Visible="@(!string.IsNullOrWhiteSpace(VatExceptionErrorMessage))">Exception VAT
                </MudText>
                @if (!string.IsNullOrWhiteSpace(VatExceptionErrorMessage))
                {
                    <MudText Style="white-space: pre-line;" Html="true">@VatExceptionErrorMessage</MudText>
                }
            </MudItem>
        </MudGrid>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(() => IsVatErrorDialogOpen = false)" Color="Color.Primary">OK</MudButton>
    </DialogActions>
</MudDialog>
