﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.AuthorizationDefinitions;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Providers;
using PurchaseManager.Shared.Services;
using Settings=PurchaseManager.Constants.Settings;
namespace PurchaseManager.Theme.Material.Pages.Account;

public class LoginVendorPage : ComponentBase
{
    [Inject]
    private NavigationManager navigationManager { get; set; }
    [Inject]
    private AuthenticationStateProvider authStateProvider { get; set; }
    [Inject] protected AppState appState { get; set; }
    [Inject] protected HttpClient httpClient { get; set; }
    [Inject] protected IStringLocalizer<Global> L { get; set; }
    [Inject]
    private IViewNotifier viewNotifier { get; set; }

    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; }

    private string navigateTo = null;
    protected bool isShowAlertForgetPassword { get; set; }
    private IdentityAuthenticationStateProvider identityAuthenticationStateProvider;
    protected bool forgotPasswordToggle = false;
    protected LoginViewModel loginViewModel;
    protected ForgotPasswordViewModel forgotPasswordViewModel { get; set; } = new ForgotPasswordViewModel();

    private string ReturnUrl;

    protected override async Task OnInitializedAsync()
    {
        if (navigationManager.TryGetQueryString("ReturnUrl", out string url))
        {
            ReturnUrl = url;
        }

        var user = (await authenticationStateTask).User;

        if (user.Identity.IsAuthenticated)
        {
            navigationManager.NavigateTo(ReturnUrl ?? "/");
        }
        else
        {
            identityAuthenticationStateProvider = (IdentityAuthenticationStateProvider)authStateProvider;

            try
            {
                var apiResponse = await identityAuthenticationStateProvider.BuildLoginViewModel(ReturnUrl);

                if (apiResponse.IsSuccessStatusCode)
                {
                    loginViewModel = apiResponse.Result;

                    if (loginViewModel.IsExternalLoginOnly)
                    {
                        if (!string.IsNullOrEmpty(ReturnUrl))
                        {
                            ReturnUrl = Uri.EscapeDataString(ReturnUrl);
                        }
                        // we only have one option for logging in and it's an external provider
                        navigationManager.NavigateTo(
                        $"{httpClient.BaseAddress}api/externalauth/challenge/{loginViewModel.ExternalLoginScheme}/{ReturnUrl}", true);
                    }
                }
                else
                {
                    viewNotifier.Show(apiResponse.Message, ViewNotifierType.Error, L["LoginFailed"]);
                }
            }
            catch (Exception ex)
            {
                viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["LoginFailed"]);
            }
        }
    }

    protected void ShowForgetPassword() => isShowAlertForgetPassword = true;

    protected void SignInWith(ExternalProvider provider)
    {
        if (!string.IsNullOrEmpty(ReturnUrl))
        {
            ReturnUrl = Uri.EscapeDataString(ReturnUrl);
        }

        navigationManager.NavigateTo($"{httpClient.BaseAddress}api/externalauth/challenge/{provider.AuthenticationScheme}/{ReturnUrl}",
        true);
    }

    protected void Register() => navigationManager.NavigateTo("/account/register");

    protected async Task SubmitLogin()
    {
        try
        {
            loginViewModel.ReturnUrl = ReturnUrl;
            var response = await identityAuthenticationStateProvider.Login(loginViewModel);

            if (response.IsSuccessStatusCode)
            {
                if (AppState.Runtime == BlazorRuntime.WebAssembly)
                {
                    if (response.Result?.RequiresTwoFactor == true)
                    {
                        var par = string.IsNullOrEmpty(ReturnUrl) ? string.Empty : $"?returnurl={Uri.EscapeDataString(ReturnUrl)}";
                        navigationManager.NavigateTo($"{Settings.LoginWith2faPath}{par}", true);
                    }
                    else
                    {
                        if (string.IsNullOrEmpty(ReturnUrl))
                        {
                            try
                            {
                                var userProfile = await appState.GetUserProfile();
                                navigateTo = navigationManager.BaseUri + (!string.IsNullOrEmpty(userProfile?.LastPageVisited)
                                    ? userProfile?.LastPageVisited : "/dashboard");
                            }
                            catch (Exception)
                            {
                                viewNotifier.Show("Could not load User Profile", ViewNotifierType.Error);
                            }
                        }
                        else
                        {
                            navigateTo = ReturnUrl;
                        }


                        navigationManager.NavigateTo(navigateTo);
                    }
                }
            }
            else
            {
                viewNotifier.Show(response.Message, ViewNotifierType.Error);
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["LoginFailed"]);
        }
    }

    protected async Task ForgotPassword()
    {
        try
        {
            await identityAuthenticationStateProvider.ForgotPassword(forgotPasswordViewModel);
            viewNotifier.Show(L["ForgotPasswordEmailSent"], ViewNotifierType.Success);
            forgotPasswordViewModel.Email = string.Empty;
            forgotPasswordToggle = false;
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["ResetPasswordFailed"]);
        }
    }
}
