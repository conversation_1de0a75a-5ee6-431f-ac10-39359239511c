using System.Globalization;
using AutoMapper;
using Breeze.AspNetCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using PurchaseManager.Constants.PurchaseSuggestedPayment;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.PurchaseSuggestedPayment;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Controllers;

[Route("api/data/")]
[ApiController]
public partial class PurchaseSuggestedPaymentController : ControllerBase
{
    private readonly IPurchaseSuggestedPaymentManager _purchaseSuggestedPaymentManager;
    private readonly ApplicationPersistenceManager _persistenceManager;
    private readonly ApplicationDbContext _context;
    private readonly IStringLocalizer<Global> L;

    private readonly IMapper _mapper;
    public PurchaseSuggestedPaymentController(IPurchaseSuggestedPaymentManager purchaseSuggestedPaymentManager,
        ApplicationPersistenceManager persistenceManager, IMapper mapper, IStringLocalizer<Global> l, ApplicationDbContext context)
    {
        _purchaseSuggestedPaymentManager = purchaseSuggestedPaymentManager;
        _persistenceManager = persistenceManager;
        _mapper = mapper;
        L = l;
        _context = context;

    }
    [HttpPost("create")]
    public async Task<ApiResponse> CreatePurchaseSuggestedPaymentAsync(List<CreatePOFromInputDemandDto> createPOFromInputDemand)
    {
        return await _purchaseSuggestedPaymentManager.CreatePurchaseSuggestedPaymentAsync(createPOFromInputDemand);
    }

    [AllowAnonymous]
    [HttpPost("create-demand/v2")]
    public async Task<ApiResponse> CreateDemandV2Async(IFormFile file)
        => await _purchaseSuggestedPaymentManager.CreateDemandV2Async(file);

    [AllowAnonymous]
    [HttpGet("get-header")]
    [BreezeQueryFilter]
    public IQueryable<DetailPurchaseSuggestedPaymentHeaderDto> GetPurchaseSuggestedPaymentHeaderAsync([FromQuery] PurchaseSuggestedPaymentHeaderFilter filter)
    {
        var query = _persistenceManager.GetEntities<PurchaseSuggestedPaymentHeader>().AsNoTracking()
            .Where(i =>
                (filter.Month == null || i.DueDate.Month == filter.Month.Value.Month && i.DueDate.Year == filter.Month.Value.Year) &&
                (filter.Vendor == null || i.BuyFromVendorNumber.ToLower().Contains(filter.Vendor.ToLower())) ||
                (filter.Vendor == null || i.PayToName.ToLower().Contains(filter.Vendor.ToLower())) &&
                ((filter.Query == null || i.BuyFromVendorNumber.ToLower().Contains(filter.Query.ToLower())) ||
                 (filter.Query == null || i.PayToName.ToLower().Contains(filter.Query.ToLower()))))
            .OrderByDescending(i => i.RowId);
        return _mapper.ProjectTo<DetailPurchaseSuggestedPaymentHeaderDto>(query);
    }

    [AllowAnonymous]
    [HttpGet("get-line/{documentNumber}")]
    [BreezeQueryFilter]
    public IQueryable<DetailPurchaseSuggestedPaymentLineDto> GetPurchaseSuggestedPaymentLineByDocNumberAsync(
        string documentNumber, [FromQuery] PurchaseSuggestedPaymentLineFilter filter)
    {
        IQueryable<PurchaseSuggestedPaymentLine> query = _persistenceManager.GetEntities<PurchaseSuggestedPaymentLine>()
            .AsNoTracking()
            .Where(i => i.DocumentNumber == documentNumber &&
                        (filter.Query == null || i.DocumentNumber.ToLower().Contains(filter.Query.ToLower())))
            .Include(h => h.ItemNumberNavigation)
            .Include(h => h.ReasonCodeNavigation);

        if (string.IsNullOrWhiteSpace(filter.Tag))
        {
            return _mapper.ProjectTo<DetailPurchaseSuggestedPaymentLineDto>(query.OrderByDescending(i => i.RowId));
        }
        {
            var uiTags = filter.Tag.Split(',').Select(tag => tag.Trim().ToLower()).ToList();
            query = uiTags.Aggregate(query, func: (current, tempTag) => current.Where(i => i.Tags.ToLower().Contains(tempTag)));
        }
        return _mapper.ProjectTo<DetailPurchaseSuggestedPaymentLineDto>(query.OrderByDescending(i => i.RowId));
    }

    [AllowAnonymous]
    [HttpGet("get-header/{documentNumber}")]
    [BreezeQueryFilter]
    public ApiResponse GetPurchaseSuggestedPaymentHeaderByDocNumberAsync(string documentNumber,
            [FromQuery] PurchaseSuggestedPaymentLineFilter filter)
    {
        var query = _persistenceManager.GetEntities<PurchaseSuggestedPaymentHeader>().AsNoTracking()
            .Where(i => i.Number == documentNumber &&
                        (filter.Query == null || i.Number.ToLower().Contains(filter.Query.ToLower())))
            .OrderByDescending(i => i.RowId).ToList().FirstOrDefault();
        var resp = _mapper.Map<DetailPurchaseSuggestedPaymentHeaderDto>(query);
        return new ApiResponse(200, "", resp);
    }


    [HttpPut("update-header")]
    public async Task<ApiResponse> UpdatePurchaseSuggestedPaymentHeaderAsync(UpdatePurchaseSuggestedPaymentHeaderDto updatePurchaseSuggestedPaymentHeaderDto)
    {
        return await _purchaseSuggestedPaymentManager.UpdatePurchaseSuggestedPaymentHeaderAsync(updatePurchaseSuggestedPaymentHeaderDto);
    }
    [HttpPost("validate-items-in-demand-v2")]
    public async Task<ApiResponse<List<InvalidItem>>> ValidateItemsInDemandV2(List<DemandItemInfo> listDemandV2)
        => await _purchaseSuggestedPaymentManager.ValidateItemsInDemandV2(listDemandV2);

    [HttpPut("update-line")]
    public async Task<ApiResponse> UpdatePurchaseSuggestedPaymentLineAsync(UpdatePurchaseSuggestedPaymentLineDto updatePurchaseSuggestedPaymentLineDto)
    {
        if (!ModelState.IsValid) return new ApiResponse(StatusCodes.Status400BadRequest, L["InvalidData"], ModelState.Select(x => x.Value.Errors.Select(y => y.ErrorMessage)));
        else return await _purchaseSuggestedPaymentManager.UpdatePurchaseSuggestedPaymentLineAsync(updatePurchaseSuggestedPaymentLineDto);
    }

    [HttpPost("open-document/{documentNumber}")]
    public async Task<ApiResponse> OpenPurchaseSuggestedPaymentAsync(string documentNumber)
    {
        return await _purchaseSuggestedPaymentManager.HandlePurchaseSuggestedPaymentAsync(documentNumber, HandlePurchaseSuggestedPaymentEnum.Open);
    }
    /// <summary>
    /// After 45 minutes of inactivity, the document will be closed automatically
    /// </summary>
    /// <param name="documentNumber"></param>
    /// <returns></returns>
    [HttpPost("close-document/{documentNumber}")]
    public async Task<ApiResponse> ClosePurchaseSuggestedPaymentAsync(string documentNumber)
    {
        return await _purchaseSuggestedPaymentManager.HandlePurchaseSuggestedPaymentAsync(documentNumber, HandlePurchaseSuggestedPaymentEnum.Close);
    }

    [HttpPost("approve-document/{documentNumber}")]
    public async Task<ApiResponse> ApprovePurchaseSuggestedPaymentAsync(string documentNumber)
    {
        return await _purchaseSuggestedPaymentManager.HandlePurchaseSuggestedPaymentAsync(documentNumber, HandlePurchaseSuggestedPaymentEnum.Approve);
    }

    [HttpGet("get-approved-documents")]
    [BreezeQueryFilter]
    public IQueryable<ApproveDocumentDto> GetApprovedDocuments()
    {
        var headerApproved = _persistenceManager.GetEntities<PurchaseSuggestedPaymentHeader>().AsNoTracking()
            .Where(i => i.Status == (int)PurchaseSuggestedPaymentEnum.Approve);
        return _mapper.ProjectTo<ApproveDocumentDto>(headerApproved);
    }

    [HttpGet("demand-v2-report")]
    [BreezeQueryFilter]
    public IQueryable<PurchaseSuggestedReport> PurchaseSuggestedReports([FromQuery] PurchaseSuggestedPaymentReportFilter filter)
    {
        var queryString = HttpContext.Request.QueryString.ToString();

        IQueryable<PurchaseSuggestedPaymentLine> report = _persistenceManager.GetEntities<PurchaseSuggestedPaymentLine>()
            .AsNoTracking()
            .Include(h => h.ItemNumberNavigation)
            .Include(h => h.ReasonCodeNavigation)
            .Where(i =>
                (filter.ItemNumber == null || i.Number.ToLower().Contains(filter.ItemNumber.ToLower())) &&
                (filter.LocationCode == null || i.LocationCode.ToLower().Contains(filter.LocationCode.ToLower())) &&
                (filter.Query == null || i.Number.ToLower().Contains(filter.Query.ToLower())))
            .OrderByDescending(i => i.LocationCode);

        if (!string.IsNullOrWhiteSpace(filter.Tag))
        {
            var uiTags = filter.Tag.Split(',').Select(tag => tag.Trim().ToLower()).ToList();
            report = uiTags.Aggregate(report, func: (current, tempTag) => current.Where(i => i.Tags.ToLower().Contains(tempTag)));
        }
        if (filter.ByItemNumber && !filter.ByLocationCode)
        {
            report = report
                .GroupBy(i => new
                {
                    i.Number, i.ItemNumberNavigation.Name, i.UnitOfMeasure
                })
                .Select(g => new PurchaseSuggestedPaymentLine
                {
                    Number = g.Key.Number,
                    UnitOfMeasure = g.Key.UnitOfMeasure,
                    ItemNumberNavigation = new Item
                    {
                        Name = g.Key.Name
                    },
                    Quantity = g.Sum(i => i.Quantity),
                    RequestQuantity = g.Sum(h => h.RequestQuantity),
                    ConfirmQuantity = g.Sum(h => h.ConfirmQuantity)
                }).AsQueryable();
        }
        else if (filter.ByLocationCode && !filter.ByItemNumber)
        {
            report = report
                .GroupBy(i => new
                {
                    i.LocationCode
                })
                .Select(g => new PurchaseSuggestedPaymentLine
                {
                    LocationCode = g.Key.LocationCode,
                    Quantity = g.Sum(i => i.Quantity),
                    RequestQuantity = g.Sum(h => h.RequestQuantity),
                    ConfirmQuantity = g.Sum(h => h.ConfirmQuantity)
                }).AsQueryable();
        }
        else if (filter.ByItemNumber && filter.ByLocationCode)
        {
            report = report
                .GroupBy(i => new
                {
                    i.Number, i.LocationCode, i.ItemNumberNavigation.Name
                })
                .Select(g => new PurchaseSuggestedPaymentLine
                {
                    Number = g.Key.Number,
                    LocationCode = g.Key.LocationCode,
                    ItemNumberNavigation = new Item
                    {
                        Name = g.Key.Name
                    },
                    Quantity = g.Sum(i => i.Quantity),
                    RequestQuantity = g.Sum(h => h.RequestQuantity),
                    ConfirmQuantity = g.Sum(h => h.ConfirmQuantity)
                }).AsQueryable();
        }



        if (!queryString!.Contains("take"))
        {
            report = report.Take(10);
        }

        if (!queryString!.Contains("skip"))
        {
            report = report.Skip(0);
        }

        return _mapper.ProjectTo<PurchaseSuggestedReport>(report);
    }

    [HttpGet("get-tags")]
    public async Task<ApiResponse> GetTags()
    {
        var users = await _context.PurchaseSuggestedPaymentLines.AsNoTracking().ToListAsync();
        var uniqueTags = users
            .SelectMany(user => user.Tags.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(tag => tag.Trim().ToLower()))
            .Distinct()
            .Select(tag => CultureInfo.CurrentCulture.TextInfo.ToTitleCase(tag))
            .OrderBy(tag => tag)
            .ToList();
        return new ApiResponse(200, "", uniqueTags);
    }
}
