﻿using System.IdentityModel.Tokens.Jwt;
using System.Web;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Providers;
using PurchaseManager.Shared.Services;
namespace PurchaseManager.Theme.Material.Pages.Account;

public partial class LoginInternal : ComponentBase
{
    [Inject]
    private NavigationManager NavigationManager { get; set; }
    [Inject]
    private AuthenticationStateProvider AuthStateProvider { get; set; }

    [Inject]
    protected IApiClient ApiClient { get; set; }
    [Inject]
    private IAccountApiClient AccountApiClient { get; set; }
    private IdentityAuthenticationStateProvider IdentityAuthenticationStateProvider { get; set; }
    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; }
    public string Token { get; set; }
    [Inject] protected HttpClient HttpClient { get; set; }
    [Inject] protected AppState AppState { get; set; }
    public string ReturnUrl { get; set; }
    protected LoginViewModel LoginViewModel;
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    private string _navigateTo = string.Empty;


    public IDictionary<string, string> TokenClaims
    {
        get;
    } = new Dictionary<string, string>();
    protected override async Task OnInitializedAsync()
    {
        if (NavigationManager.TryGetQueryString("ReturnUrl", out string url))
        {
            ReturnUrl = url;
        }

        var user = (await AuthenticationStateTask).User;

        if (user.Identity!.IsAuthenticated)
        {
            NavigationManager.NavigateTo(ReturnUrl ?? "/");
        }
        else
        {
            IdentityAuthenticationStateProvider = (IdentityAuthenticationStateProvider)AuthStateProvider;

            try
            {
                var apiResponse = await IdentityAuthenticationStateProvider.BuildLoginViewModel(ReturnUrl);

                if (apiResponse.IsSuccessStatusCode)
                {
                    LoginViewModel = apiResponse.Result;

                    if (LoginViewModel.IsExternalLoginOnly)
                    {
                        if (!string.IsNullOrEmpty(ReturnUrl))
                        {
                            ReturnUrl = Uri.EscapeDataString(ReturnUrl);
                        }
                        // we only have one option for logging in and it's an external provider
                        NavigationManager.NavigateTo(
                        $"{HttpClient.BaseAddress}api/externalauth/challenge/{LoginViewModel.ExternalLoginScheme}/{ReturnUrl}", true);
                    }
                }
                else
                {
                    ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Error, "LoginFailed");
                }
            }
            catch (Exception ex)
            {
                ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, "LoginFailed");
            }
        }

        var uri = new Uri(NavigationManager.Uri);
        var query = HttpUtility.ParseQueryString(uri.Query);

        Token = query.Get("token");

        if (!string.IsNullOrEmpty(Token))
        {
            Console.WriteLine($"Received token: {Token}");
            // Validate token
            await DecodeToken(Token);

            //NavigationManager.NavigateTo("/");
        }
        else
        {
            Console.WriteLine("Token is missing or invalid.");
            NavigationManager.NavigateTo("/error");
        }
    }
    private async Task DecodeToken(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();

            if (tokenHandler.CanReadToken(token))
            {
                var jwtToken = tokenHandler.ReadJwtToken(token);

                var userCode = jwtToken.Claims.First(claim => claim.Type == "EmployeeCode").Value;
                var input = new LoginInputModel
                {
                    UserName = userCode, Password = "", RememberMe = true
                };
                var response = await IdentityAuthenticationStateProvider.LoginInternal(input.UserName);

                if (response.IsSuccessStatusCode)
                {
                    if (AppState.Runtime == BlazorRuntime.WebAssembly)
                    {
                        if (response.Result?.RequiresTwoFactor == true)
                        {
                            var par = string.IsNullOrEmpty(ReturnUrl) ? string.Empty : $"?returnurl={Uri.EscapeDataString(ReturnUrl)}";
                            NavigationManager.NavigateTo($"{Settings.LoginWith2faPath}{par}", true);
                        }
                        else
                        {
                            if (string.IsNullOrEmpty(ReturnUrl))
                            {
                                try
                                {
                                    var userProfile = await AppState.GetUserProfile();
                                    _navigateTo = NavigationManager.BaseUri + (!string.IsNullOrEmpty(userProfile?.LastPageVisited)
                                        ? userProfile?.LastPageVisited : "/dashboard");
                                }
                                catch (Exception)
                                {
                                    ViewNotifier.Show("Could not load User Profile", ViewNotifierType.Error);
                                }
                            }
                            else
                            {
                                _navigateTo = ReturnUrl;
                            }
                            NavigationManager.NavigateTo(_navigateTo);
                        }
                    }
                }
                else
                {
                    Console.WriteLine("Error logging in user.");
                }

                Console.WriteLine($"User code: {userCode}");
                Console.WriteLine("Token decoded successfully:");
            }
            else
            {
                Console.WriteLine("Invalid token format.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error decoding token: {ex.Message}");
        }
    }

    // protected async Task SubmitLogin()
    //     {
    //         try
    //         {
    //             var response = await IdentityAuthenticationStateProvider.Login(loginViewModel);
    //
    //             if (response.IsSuccessStatusCode)
    //             {
    //                 if (AppState.Runtime == BlazorRuntime.WebAssembly)
    //                 {
    //                     if (response.Result?.RequiresTwoFactor == true)
    //                     {
    //                         var par = string.IsNullOrEmpty(ReturnUrl) ? string.Empty : $"?returnurl={Uri.EscapeDataString(ReturnUrl)}";
    //                         navigationManager.NavigateTo($"{Settings.LoginWith2faPath}{par}", true);
    //                     }
    //                     else
    //                     {
    //                         if (string.IsNullOrEmpty(ReturnUrl))
    //                         {
    //                             try
    //                             {
    //                                 var userProfile = await appState.GetUserProfile();
    //                                 navigateTo = navigationManager.BaseUri + (!string.IsNullOrEmpty(userProfile?.LastPageVisited) ? userProfile?.LastPageVisited : "/dashboard");
    //                             }
    //                             catch (Exception)
    //                             {
    //                                 viewNotifier.Show("Could not load User Profile", ViewNotifierType.Error);
    //                             }
    //                         }
    //                         else
    //                             navigateTo = ReturnUrl;
    //
    //
    //                         navigationManager.NavigateTo(navigateTo);
    //                     }
    //                 }
    //             }
    //             else
    //                 viewNotifier.Show(response.Message, ViewNotifierType.Error);
    //         }
    //         catch (Exception ex)
    //         {
    //             viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["LoginFailed"]);
    //         }
    //     }

}
