﻿using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Server.Managers;
using PurchaseManager.Shared.Dto.Email;
using PurchaseManager.Storage;
using Newtonsoft.Json;

namespace PurchaseManager.Server.Services
{
    public class EmailService : BackgroundService
    {
        private readonly IServiceScopeFactory _scopeFactory;
        private readonly ILogger<EmailService> _logger;
        public EmailService(
            IServiceScopeFactory scopeFactory,
            ILogger<EmailService> logger)
        {
            _scopeFactory = scopeFactory;
            _logger = logger;
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                await Program.Sync.WaitAsync(stoppingToken);

                _logger.LogInformation($"EmailService starting...");

                do
                {
                    using var scope = _scopeFactory.CreateScope();
                    {
                        var dbContext = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
                        var emailManager = scope.ServiceProvider.GetRequiredService<IEmailManager>();

                        foreach (var email in dbContext.QueuedEmails.Where(i => i.SentOn == null).OrderBy(i => i.CreatedOn).ToArray())
                        {
                            // add data in queue 
                            var vendorNumber = email.VendorNumber;
                            var PoNumber = email.PoNumber;
                            var data = new EmailDataDto
                            {
                                VendorNumber = vendorNumber,
                                PONumber = PoNumber,
                            };
                            var response = await emailManager.SendEmail(JsonConvert.DeserializeObject<EmailMessageDto>(email.Email), data, email.EmailType);

                            if (response.IsSuccessStatusCode)
                            {
                                email.SentOn = DateTime.Now;

                                await dbContext.SaveChangesAsync(stoppingToken);
                            }

                            if (stoppingToken.IsCancellationRequested)
                                return;
                        }
                    }

                    await EmailManager.QueueSync.WaitAsync(60000, stoppingToken);

                } while (!stoppingToken.IsCancellationRequested);
            }
            catch (Exception ex)
            {
                if (ex is not OperationCanceledException)
                    _logger.LogError($"EmailService: ExecuteAsync {ex.GetBaseException()}");
            }
        }
    }
}
