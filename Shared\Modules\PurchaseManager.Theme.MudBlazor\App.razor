﻿@using Microsoft.AspNetCore.Components.WebAssembly.Services
@using NavigationManagerExtensions = PurchaseManager.Shared.Extensions.NavigationManagerExtensions
@using NavigationContext = Microsoft.AspNetCore.Components.Routing.NavigationContext
@inject LazyAssemblyLoader assemblyLoader
@inject NavigationManager navigationManager
@inject IStringLocalizer<Global> L

<CascadingAuthenticationState>
<MudThemeProvider Theme="@mudTheme"/>
<MudPopoverProvider/>
<MudDialogProvider/>
<MudSnackbarProvider/>

<Router AppAssembly="typeof(App).Assembly" AdditionalAssemblies="ModuleProvider.AssembliesWithPages" OnNavigateAsync="@OnNavigateAsync">
    <Found Context="routeData">
        <AuthorizeRouteView RouteData="@routeData" DefaultLayout="typeof(MainLayout)">
            <Authorizing>
                <AuthorizingInProgress/>
            </Authorizing>
            <NotAuthorized>
                @if (context.User.Identity.IsAuthenticated==false)
                {
                    <RedirectToLogin/>
                }
                else
                {
                    <UserNotAuthorized/>
                }
            </NotAuthorized>
        </AuthorizeRouteView>
    </Found>
    <NotFound>
        <CascadingAuthenticationState>
            <LayoutView Layout="typeof(MainLayout)">
                <PageNotFound/>
            </LayoutView>
        </CascadingAuthenticationState>
    </NotFound>
</Router>
</CascadingAuthenticationState>

@code
{

    MudTheme mudTheme = new MudTheme()
    {
        PaletteLight = new PaletteLight()
        {
            Primary = Colors.Indigo.Default,
            Secondary = "#FCFCFC",
            AppbarBackground = "#FFF", //5D2E8F
            AppbarText = "#0f0f0f",
           // Background= "rgb(247, 247, 247)",
       },
        PaletteDark = new PaletteDark()
        {
        },
        Typography = new Typography()
        {
            Default = new Default()
            {
                FontSize = ".875rem"
            },
            Input = new Input()
            {
                FontSize = ".875rem", //1rem
                FontWeight = 400,
                LineHeight = 1.1876,
                LetterSpacing = ".00938em",
            },
            Button = new Button()
            {
                TextTransform = "capitalize",
            },
            Body1 = new Body1()
            {
                FontSize = ".875rem"
            }
        },
        LayoutProperties = new LayoutProperties()
        {
            DefaultBorderRadius = "6px",
            DrawerMiniWidthLeft = "279px",
            DrawerWidthLeft = "280px"
            
        }
    };

    private async Task OnNavigateAsync(NavigationContext args)
    {
        if (NavigationManagerExtensions.IsWebAssembly())
            if (args.Path.Equals("admin", StringComparison.OrdinalIgnoreCase))
            {
                var assemblies = await assemblyLoader.LoadAssembliesAsync(
                    new List<string>() { "PurchaseManager.Theme.MudBlazor.Admin.dll", "Blazored.TextEditor.dll" });

                ModuleProvider.AddModules(assemblies);
            }
    }
}