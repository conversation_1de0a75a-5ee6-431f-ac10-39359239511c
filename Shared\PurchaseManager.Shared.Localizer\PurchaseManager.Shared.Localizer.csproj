﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Karambolo.PO" Version="1.11.1" />
        <PackageReference Include="Microsoft.AspNetCore.Components" Version="8.0.6" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Localization.Abstractions" Version="8.0.6" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\PurchaseManager.Shared.DataInterfaces\PurchaseManager.Shared.DataInterfaces.csproj"/>
    </ItemGroup>

</Project>
