using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using NetTopologySuite.Utilities;
using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Controllers;

[Route("api/[controller]/[action]")]
[ApiController]
public class DashboardController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly IStringLocalizer<Global> L;

    public DashboardController(ApplicationDbContext context, IStringLocalizer<Global> l)
    {
        _context = context;
        L = l;
    }
    [HttpGet]
    public async Task<ApiResponse> CountOrderTypes()
    {
        var orderTypeCounts = await _context.PurchaseOrderHeaders
            .Where(x => x.OrderType == 0 || x.OrderType == 1)
            .GroupBy(x => x.OrderType)
            .Select(g => new
            {
                OrderType = g.Key, Count = g.Count()
            })
            .ToListAsync();

        var result = new CountOrderTypeDto
        {
            AutoOrder = orderTypeCounts.FirstOrDefault(x => x.OrderType == 1)?.Count ?? 0, 
            ManualOrder = orderTypeCounts.FirstOrDefault(x => x.OrderType == 0)?.Count ?? 0
        };

        return new ApiResponse(200, L["Operation Successful"], result);
    }

    [HttpGet]
    public async Task<ApiResponse> GetsDocumentApprove([FromQuery] DateTime dateTime)
    {
        var result = await _context.PurchaseOrderHeaders
            .Where(x => x.DueDate == dateTime && x.Status == (int)PurchaseOrderEnum.Approve)
            .ToListAsync();
        return new ApiResponse(200, L["Operation Successful"], result);
    }

    [HttpGet]
    public async Task<ApiResponse> CountPoCompleted()
    {
        var result = await _context.PurchaseOrderHeaders
            .Where(x => x.Status == (int)PurchaseOrderEnum.Completed)
            .CountAsync();
        return new ApiResponse(200, L["Operation Successful"], result);
    }

    [HttpGet]
    public async Task<ApiResponse> CountPoDocNoOccurrence()
    {
        var listDoc = await _context.PurchaseOrderHeaders
            .Where(x => x.DocNoOccurrence == (int)DocNoOccurrenceEnum.Order
                        || x.DocNoOccurrence == (int)DocNoOccurrenceEnum.Consigned 
                        || x.DocNoOccurrence == (int)DocNoOccurrenceEnum.Promotional)
            .GroupBy(x => x.DocNoOccurrence)
            .Select(g => new
            {
                DocNoOccurrence = g.Key, Count = g.Count()
            })
            .ToListAsync();

        var result = new PoDocNoOccurrenceDto
        {
            Order = listDoc.FirstOrDefault(x => x.DocNoOccurrence == (int)DocNoOccurrenceEnum.Order)?.Count ?? 0, 
            Consigned = listDoc.FirstOrDefault(x => x.DocNoOccurrence == (int)DocNoOccurrenceEnum.Consigned)?.Count ?? 0, 
            Promotional = listDoc.FirstOrDefault(x => x.DocNoOccurrence == (int)DocNoOccurrenceEnum.Promotional)?.Count ?? 0, 
        };
        return new ApiResponse(200, L["Operation Successful"], result);
    }
}
