@page "/po/purchase-suggest-payment/create-po"
@inherits CreatePurchaseSuggestedPaymentBase
@if (isLoading)
{
    <MudProgressLinear Indeterminate="@isLoading" />
}
else
{
    @if (isCreatePO)
    {
        <MudStack class="my-4">
            <MudOverlay @bind-Visible="isCreatePO" LightBackground ZIndex="9999">
                <MudText Typo="Typo.h6">@L["Creating PO. Pleaes wait"]</MudText>
                <MudProgressLinear Color="Color.Info" Indeterminate="true" />
            </MudOverlay>
        </MudStack>
    }
    <MudStack Row Class="my-3" Justify="Justify.SpaceBetween">
        <MudText Typo="Typo.h6" Class="mb-3" Align="Align.Center">@L["Create PO from Purchase Suggested Payment"] </MudText>
        <MudBadge Origin="Origin.TopRight" Content="@(selectedItems.Count())" Color="Color.Primary" Overlap="true"
            Bordered="@true">
            <MudButton StartIcon="@Icons.Material.Filled.Add" Disabled="@(!selectedItems.Any() || isCreatePO)"
                OnClick="@(async _ => await CheckPaymentDayBySuggestDocumentNumber())" ButtonType="ButtonType.Button"
                Variant="Variant.Filled" Color="Color.Success" Class="">
                @L["Create PO"]
            </MudButton>
        </MudBadge>
    </MudStack>
    <MudTable Hover Breakpoint="Breakpoint.Sm" Height="680px" FixedHeader="true" @bind-SelectedItems="selectedItems"
        AllowUnsorted LoadingProgressColor="Color.Info" Loading="@isBusy"
        T="PurchaseManager.Shared.Dto.PurchaseSuggestedPayment.ApproveDocumentDto"
        ServerData="@(new Func<TableState, CancellationToken, Task<TableData<PurchaseManager.Shared.Dto.PurchaseSuggestedPayment.ApproveDocumentDto>>>(ServerReload))"
        MultiSelection>
        <HeaderContent>
            <MudTh Style="width: 400px;">Demand Number</MudTh>
            <MudTh>Location</MudTh>
            <MudTh>Create Date</MudTh>
        </HeaderContent>
        <GroupHeaderTemplate>
            <MudTh Class="mud-table-cell-custom-group" colspan="5">
                <MudText Align="Align.Center">@($"{context.GroupName}: {context.Key}")</MudText>
            </MudTh>
        </GroupHeaderTemplate>
        <RowTemplate>
            <MudTd DataLabel="ItemNo">
                <MudText>
                    <MudLink Href="@($"/po/purchase-suggest-payment/{context.Number}/detail")"> @context.Number </MudLink>
                </MudText>
            </MudTd>
            <MudTd DataLabel="QtyDemand">
                <MudText>
                    @context.SourceCode
                </MudText>
            </MudTd>
            <MudTd DataLabel="ItemNo">
                <MudText>
                    @context.OrderDate.ToString("dd/MM/yyyy HH:mm:ss")
                </MudText>
            </MudTd>
        </RowTemplate>
    </MudTable>
}
<MudDialog @bind-Visible="@isAlertDialogOpen"
    Options="@(new DialogOptions(){MaxWidth = MaxWidth.Medium, FullWidth = true})">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.ReportProblem" Color="Color.Warning" Class="mr-3 mb-n1" />
            @L["There are a number of vendors who have PO available"]
        </MudText>
    </TitleContent>
    <DialogContent>
        <AlertPaymentDay Parameters="@listCheckPaymentDayResponse"></AlertPaymentDay>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(e => { isAlertDialogOpen = false; })">@L["Cancel"]</MudButton>
        <MudButton OnClick="@( async ()=> await CreatePO())" Color="Color.Primary">@L["Continue create"]</MudButton>
    </DialogActions>
</MudDialog>