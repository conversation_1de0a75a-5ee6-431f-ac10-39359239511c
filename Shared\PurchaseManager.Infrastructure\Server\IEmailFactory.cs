﻿using PurchaseManager.Shared.Dto.Email;

namespace PurchaseManager.Infrastructure.Server
{
    public interface IEmailFactory
    {
        EmailMessageDto BuildEmail(EmailRequestDto emailDto);
        EmailMessageDto GetPlainTextTestEmail(DateTime date);
        EmailMessageDto BuildNewUserConfirmationEmail(string fullName, string userName, string callbackUrl);
        EmailMessageDto BuildNewUserEmail(string fullName, string userName, string emailAddress, string password);
        EmailMessageDto BuildNewUserNotificationEmail(string creator, string name, string userName, string company, string roles);
        EmailMessageDto BuildForgotPasswordEmail(string name, string callbackUrl, string token);
        EmailMessageDto BuildPasswordResetEmail(string userName);
    }
}
