﻿using AutoMapper;
using PurchaseManager.Shared.Dto.FileStorage;
using FileStorage=PurchaseManager.Infrastructure.Storage.DataModels.FileStorage;
namespace PurchaseManager.Storage.Mapping;

public class FileStorageMappingProfile : Profile
{
    public FileStorageMappingProfile()
    {
        CreateMap<FileStorage, CreateFileDto>().ReverseMap();
        CreateMap<FileStorage, UpdateFileDto>().ReverseMap();
        CreateMap<FileStorage, DetailFileDto>().ReverseMap();
    }
}
