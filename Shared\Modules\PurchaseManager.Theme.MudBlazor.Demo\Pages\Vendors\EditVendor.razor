@page "/vendors/{VendorNumberParam}/detail"
@attribute [Authorize]
@using Breeze.Sharp.Core
@if (isLoad)
{
    <MudProgressLinear Indeterminate="@isLoad" />
}
else
{
    <PageTitle>@L["Edit"] @_vendor.Number </PageTitle>
    @if (isAuthorized)
    {
        <VendorForm currentVendor="_vendor" OnToggleStatus="@(async _ => await OnToggleStatusCallback(_))" />
    }
    else
    {
        <UserNotAuthorized />
    }
}
