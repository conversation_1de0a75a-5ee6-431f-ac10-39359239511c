﻿using System.Net.Mail;
using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Email;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Models.Email;

namespace PurchaseManager.Infrastructure.Server
{
    public interface IEmailManager
    {
        Task<ApiResponse> SendTestEmail(EmailRequestDto parameters);
        Task<ApiResponse> QueueEmail(EmailMessageDto emailMessage, EmailType emailType);
        Task<ApiResponse> SendEmail(EmailMessageDto emailMessage, EmailDataDto emailDataDto = null, EmailType emailType = EmailType.Common);
        IQueryable<DetailEmailDto> GetEmails(EmailFilter filter);

        //Email Templates
        IQueryable<DetailEmailTemplate> GetEmailTemplates(EmailTemplateFilter filter);
        DetailEmailTemplate GetEmailTemplateById(int emailTemplateId);
        Task<ApiResponse> CreateEmailTemplate(CreateEmailTemplateDto createEmailTemplateDto);
        Task<ApiResponse> UpdateEmailTemplate(int emailTemplateId, UpdateEmailTemplateDto updateEmailTemplateDto);
        public Task<ApiResponse> SendEmailDetailPOToVendor(EmailInfoInPODetailDto emailInfoInPODetailDto);
        public IQueryable<SentEmailDto> GetAllSentEmail(SentEmailFilter filter);

    }
}
