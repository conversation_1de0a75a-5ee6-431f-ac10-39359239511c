﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Localization;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Dto.Response;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.PurchasePrice;
using PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder.Services;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchasePrices;

public partial class PurchasePriceUploadFile : ComponentBase
{
    [Parameter]
    public string Label { get; set; } = "Upload ";
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    [Inject]
    private IApiClient ApiClient { get; set; }
    [Inject]
    private IStringLocalizer<Global> L { get; set; }
    private bool IsAlertDialogOpen { get; set; }
    private bool IsFileRead { get; set; }
    private bool IsFileLoading { get; set; }
    private bool IsCreatePO { get; set; }
    private List<CreatePurchasePriceDto> PurchaseFileHeaderImportDtos { get; set; } = [];
    private List<CreatePurchasePriceDto> PurchasePriceHeaderCheckDtos { get; set; } = [];
    private List<ResponseWithDetailError> ResponseWithDetailError { get; set; } = [];
    private List<DuplicatePurchasePriceItemDto> ListCheckPaymentDayResponse { get; set; } = [];

    private async Task UploadFiles(IBrowserFile file)
    {
        ClearAllDataInList();
        IsFileLoading = true;
        if (file == null)
        {
            return;
        }
        if (Path.GetExtension(file.Name).ToLower() != ".xlsx" && Path.GetExtension(file.Name).ToLower() != ".xls")
        {
            ViewNotifier.Show("Only Excel file", ViewNotifierType.Warning, "Operation Failed");
        }
        else
        {
            PurchaseFileHeaderImportDtos = [];
            await using var stream = file.OpenReadStream();

            var createPurchasePriceList = await ExcelExportExtension.ReadExcel(stream,
            mapFunction: row => new CreatePurchasePriceDto
            {
                VendorNumber = row["VendorNumber"],
                ItemNumber = row["ItemNumber"],
                PurchasingUnit = row["PurchasingUnit"],
                Price = decimal.Parse(row["Price"]),
                VAT = int.Parse(row["VAT"]),
                DiscountBySkus = int.Parse(row["DiscountBySkus"]),
                GroupProduct = row["GroupProduct"],
                FoCPromotion = row["FoCPromotion"]
            });

            PurchasePriceHeaderCheckDtos = createPurchasePriceList;
            var duplicated = PurchasePriceHeaderCheckDtos.GetDuplicateDetails();
            if (duplicated.Count != 0)
            {
                IsAlertDialogOpen = true;
                ListCheckPaymentDayResponse = duplicated;
                ViewNotifier.Show($"Có {duplicated.Count} dòng bị trùng Vendor, Item, Price.", ViewNotifierType.Warning,
                "Duplicate Detected");
            }
            else
            {
                PurchaseFileHeaderImportDtos = createPurchasePriceList;
                ListCheckPaymentDayResponse.Clear();
            }
            IsFileRead = true;
        }
    }
    private void ClearAllDataInList()
    {
        PurchaseFileHeaderImportDtos.Clear();
        ResponseWithDetailError.Clear();
        PurchasePriceHeaderCheckDtos.Clear();
        IsFileRead = false;
        IsFileLoading = false;
    }
    public async Task CheckPaymentDayByVendorNumber()
    {
        try
        {
            var resp = await ApiClient.ValidateFileData(PurchasePriceHeaderCheckDtos);

            if (resp.Result.Count > 0)
            {
                ResponseWithDetailError = resp.Result;
            }
            else
            {
                await CreatePurchasePrice();
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }

    private async Task CreatePurchasePrice()
    {
        IsCreatePO = true;
        IsAlertDialogOpen = false;
        try
        {
            var resp = await ApiClient.CreateMultiplePurchasePriceHeaderViaFile(PurchaseFileHeaderImportDtos);
            if (!resp.IsSuccessStatusCode)
            {
                ViewNotifier.Show(resp.Message + resp.Result, ViewNotifierType.Error, L["Operation failed"]);
            }
            else
            {
                ViewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation successful"]);
                ClearAllDataInList();
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error, L["Operation failed"]);
        }
        finally
        {
            IsCreatePO = false;

        }
    }
}
