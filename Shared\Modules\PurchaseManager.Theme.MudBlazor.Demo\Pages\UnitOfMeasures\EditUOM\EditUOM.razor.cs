using AutoMapper;

using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Localization;

using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
namespace PurchaseManager.Theme.Material.Demo.Pages.UnitOfMeasures.EditUOM;
public partial class EditUOMPage : ComponentBase
{
    [Parameter] public string UnitOfMeasureCode { get; set; }
    [Inject] protected IStringLocalizer<Global> L { get; set; }
    [Inject] protected NavigationManager navigation { get; set; }
    [Inject] protected IMapper _mapper { get; set; }
    [Inject] IViewNotifier notifier { get; set; }
    [Inject] protected IApiClient apiClient { get; set; }
    protected bool isCreate { get; set; }
    protected bool isSaving { get; set; }
    protected bool isNotFound { get; set; }
    protected bool isLoading { get; set; } = true;
    protected UnitOfMeasureDto item = new UnitOfMeasureDto();
    protected EditContext editContext;
    protected override async Task OnInitializedAsync()
    {
        item ??= new();
        editContext = new(item);
        CheckViewAction();
        if (!isCreate)
        {
            await LoadUOM();
        }
        await base.OnInitializedAsync();
        isLoading = false;
    }
    protected void CheckViewAction()
    {
        var uri = new Uri(navigation.Uri);
        var path = uri.LocalPath;
        isCreate = path.Contains("create");
    }
    protected async Task LoadUOM()
    {
        var resp = await apiClient.GetAllUnitOfMeasure((i => i.Code == UnitOfMeasureCode), null, null, null);
        if (!resp.Results.Any())
        {
            isNotFound = true;
            return;
        }
        item = resp.Results.FirstOrDefault();
    }
    protected async Task OnSubmitForm()
    {
        isSaving = true;
        item.Code = item.Code.Trim().ToUpper();
        item.Description = item.Description.Trim();
        if (isCreate)
        {
            item.Type = 0;
            var respParam = _mapper.Map<CreateUnitOfMeasureDto>(item);
            var resp = await apiClient.CreateUOM(new List<CreateUnitOfMeasureDto> { respParam });
            if (!resp.IsSuccessStatusCode) notifier.Show(resp.Message, ViewNotifierType.Error, L["SaveFailure"]);
            else
            {
                notifier.Show(resp.Message, ViewNotifierType.Success, L["SaveSuccessfully"]);
                navigation.NavigateTo("/Unit-Of-Measures");
            }
        }
        else //update
        {
            var respParam = _mapper.Map<UpdateUnitOfMeasureDto>(item);
            var resp = await apiClient.UpdateUOM(respParam, item.Code);
            if (!resp.IsSuccessStatusCode) notifier.Show(resp.Message, ViewNotifierType.Error, L["SaveFailure"]);
            else notifier.Show(resp.Message, ViewNotifierType.Success, L["SaveSuccessfully"]);
        }
        isSaving = false;
    }
}
