﻿using AutoMapper;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Storage.Mapping;

public class PurchaseMappingProfile : Profile
{
    public PurchaseMappingProfile()
    {
        CreateMap<POLineAddOrUpdate, PurchaseOrderLine>().ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Desc)).ReverseMap();
        CreateMap<POLineGetDto, PurchaseOrderLine>().ReverseMap();
        CreateMap<POHeaderGetDto, PurchaseOrderHeader>().ReverseMap();

        CreateMap<POLineGetDto, POLineAddOrUpdate>().ForMember(dest => dest.Desc, opt => opt.MapFrom(src => src.Description)).ReverseMap();
    }
}
