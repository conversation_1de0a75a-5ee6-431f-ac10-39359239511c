using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Localization;
using MudBlazor;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Item;

namespace PurchaseManager.Theme.Material.Demo.Pages.Items;

public partial class EditVendorItemBase : ComponentBase
{

    [Parameter]
    public string ItemNumber { get; set; }
    [Parameter]
    public string VendorNumber { get; set; }
    [Inject]
    protected IStringLocalizer<Global> L { get; set; }
    [Inject]
    protected IApiClient apiClient { get; set; }
    [Inject]
    IViewNotifier viewNotifier { get; set; }
    [Inject]
    protected NavigationManager navigation { get; set; }
    protected List<UnitOfMeasureDto> listUnitOfMeasureDto { get; set; } = new List<UnitOfMeasureDto>();

    protected bool isEditing { get; set; }
    protected bool isLoading { get; set; }
    protected bool isItemBlocked { get; set; }
    protected EditContext editContext { get; set; }

    protected MudForm form;

    protected VendorItemFilter vendorItemFilter { get; set; } = new VendorItemFilter();

    protected DetailVendorItemDto currentVendorItemDto { get; set; } = new DetailVendorItemDto();


    protected override async Task OnInitializedAsync()
    {
        isLoading = true;
        await LoadVendorItemByRowId();
        await LoadAllUOM();
        isLoading = false;
        await base.OnInitializedAsync();
    }
    protected async Task LoadVendorItemByRowId()
    {
        try
        {
            vendorItemFilter.VendorNumber = VendorNumber;
            vendorItemFilter.ItemNumber = ItemNumber;
            var resp = await apiClient.GetAllVendorItems(vendorItemFilter);
            if (resp.Count() > 0)
            {
                currentVendorItemDto = resp.FirstOrDefault();
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["CreateNewVendorItemFailed"]);
        }
    }
    protected async Task LoadAllUOM()
    {
        try
        {
            var resp = await apiClient.GetAllUnitOfMeasure(null, null, null);
            listUnitOfMeasureDto = resp.ToList();
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["LoadUOMFailed"]);
        }
    }

    protected async Task OnToggleVendorItemStatus(int rowId)
    {
        try
        {
            var resp = await apiClient.ToggleVendorItemStatus(rowId);
            if (resp.IsSuccessStatusCode)
            {
                currentVendorItemDto = resp.Result;
                viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation successful"]);
            }

        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["ToggleVendorItemStatusFailed"]);
        }
    }
    protected async Task OnUpdateVendorItemSubmit()
    {
        try
        {
            isLoading = true;
            var req = new UpdateVendorItemDto();
            //prepare req
            req.VendorNumber = VendorNumber;
            req.Name = currentVendorItemDto.Name;
            req.ItemNumber = currentVendorItemDto.ItemNumber;
            req.UnitOfMeasure = currentVendorItemDto.UnitOfMeasure;
            req.Price = currentVendorItemDto.Price.Value;
            req.Vat = currentVendorItemDto.Vat.Value;
            req.VendorNumber = VendorNumber;
            req.VendorNumber = VendorNumber;

            var resp = await apiClient.UpdateVendorItem(req);
            if (resp.IsSuccessStatusCode) viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation successful"]);

        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["UpdateVendorItemFailed"]);
        }
        finally
        {
            isLoading = false;
        }
    }
}
