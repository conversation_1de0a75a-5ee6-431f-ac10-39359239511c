 async function downloadFileFromBase64(fileName, base64) {
        const link = document.createElement('a');
        link.href = `data:application/octet-stream;base64,${base64}`;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    //        const arrayBuffer = await contentStreamReference.arrayBuffer();
    // const blob = new Blob([arrayBuffer]);
    // const url = URL.createObjectURL(blob);
    // const anchorElement = document.createElement('a');
    // anchorElement.href = url;
    // anchorElement.download = fileName ?? '';
    // anchorElement.click();
    // anchorElement.remove();
    // URL.revokeObjectURL(url);
    }
//  window.downloadFileFromStream = async (fileName, contentStreamReference) => {
//     const arrayBuffer = await contentStreamReference.arrayBuffer();
//     const blob = new Blob([arrayBuffer]);
//     const url = URL.createObjectURL(blob);
//     const anchorElement = document.createElement('a');
//     anchorElement.href = url;
//     anchorElement.download = fileName ?? '';
//     anchorElement.click();
//     anchorElement.remove();
//     URL.revokeObjectURL(url);
//   }