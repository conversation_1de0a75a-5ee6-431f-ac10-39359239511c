﻿using System.ComponentModel.DataAnnotations;

namespace PurchaseManager.Shared.Dto.Item;

public class CreateItemUnitOfMeasureDto
{
    [StringLength(100)]
    public string ItemNumber { get; set; }
    [StringLength(100)]
    public string Code { get; set; } = null!;
    public decimal QuantityPerUnitOfMeasure { get; set; }
    [StringLength(160)]
    public string? Description { get; set; }
    public int? Type { get; set; }
}
