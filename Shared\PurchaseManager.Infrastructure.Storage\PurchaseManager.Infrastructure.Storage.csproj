﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Google.Protobuf" Version="3.18.0" />
        <PackageReference Include="Grpc.Net.Client" Version="2.52.0" />
        <PackageReference Include="Grpc.AspNetCore" Version="2.32.0" />
        <PackageReference Include="Grpc.Tools" Version="2.40.0">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="Finbuckle.MultiTenant" Version="6.13.1" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.6" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.6" />
        <PackageReference Include="NetTopologySuite" Version="2.5.0"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Utils\PurchaseManager.SourceGenerator\PurchaseManager.SourceGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false"/>
        <ProjectReference Include="..\PurchaseManager.Constants\PurchaseManager.Constants.csproj"/>
        <ProjectReference Include="..\PurchaseManager.Shared.DataInterfaces\PurchaseManager.Shared.DataInterfaces.csproj"/>
        <ProjectReference Include="..\PurchaseManager.Shared\PurchaseManager.Shared.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <Protobuf Include="Protos\po-client.proto" GrpcServices="Client" />
        <Protobuf Include="Protos\po-vendor.proto" GrpcServices="Client" />
        <Protobuf Include="Protos\po-iuom.proto" GrpcServices="Client" />
        <Protobuf Include="Protos\po-unit-of-measure.proto" GrpcServices="Client" />
	    <Protobuf Include="Protos\common.proto" GrpcServices="None" />
    </ItemGroup>
    <ItemGroup>
        <Compile Update="Permissions\Permissions.cs">
            <DependentUpon>Permissions.tt</DependentUpon>
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <None Update="Permissions\Permissions.tt">
            <LastGenOutput>Permissions.cs</LastGenOutput>
            <Generator>TextTemplatingFileGenerator</Generator>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}"/>
    </ItemGroup>

    <Target Name="PostBuild" AfterTargets="PostBuildEvent">
        <Exec Command="dotnet build &quot;$(SolutionDir)Shared\PurchaseManager.Shared\PurchaseManager.Shared.csproj&quot;"/>
    </Target>

</Project>
