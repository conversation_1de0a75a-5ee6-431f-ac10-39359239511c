﻿@inherits ListFileBasePage
@if (isLoading)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    <MudTable @ref="@table" T="PurchaseManager.Shared.Dto.FileStorage.DetailFileDto"
              @bind-SelectedItems="selectedItems"
              ServerData="@( new Func<TableState,CancellationToken,Task<TableData<PurchaseManager.Shared.Dto.FileStorage.DetailFileDto>>>(ServerReload))" 
              MultiSelection="@IsAllowDelete"
              Hover="true"
              SelectOnRowClick ="@IsAllowDelete">
        <ToolBarContent>
            <MudText Typo="Typo.h6">@Caption</MudText>
            <MudSpacer />
            @if (selectedItems.Any() && IsAllowDelete)
            {
                <MudButton StartIcon="@Icons.Material.Filled.DeleteSweep" Color="Color.Error" OnClick="@DeleteAllFileAsync" Class="ml-4 mt-1">Delete @selectedItems.Count files</MudButton>
            }
            <MudIconButton Icon="@Icons.Material.Filled.Refresh" OnClick="@(() => Reload())" Size="Size.Medium" Class="ma-2" />
        </ToolBarContent>
        <HeaderContent>
            <MudTh>FileName</MudTh>
            @if (IsAllowDelete)
            {
                <MudTh>Actions</MudTh>
            }
        </HeaderContent>
        <RowTemplate>
            <MudTd DataLabel="FileUrl"><MudLink  @onclick="() =>ShowFilePreview(context)">@context.FileName</MudLink></MudTd>
            @if (IsAllowDelete)
            {
                <MudTd DataLabel="Actions">
                    <MudIconButton Icon="@Icons.Material.Filled.DeleteForever" OnClick="@(() => DeleteFile(context.FileId))" Size="Size.Medium" Variant="Variant.Text" Color="Color.Error" Class="ma-2" />
                </MudTd>
            }
        </RowTemplate>
        <PagerContent>
            <MudTablePager PageSizeOptions="new int[] { 10, 25, 50, 100 }" />
        </PagerContent>
        <NoRecordsContent>
         Không có dữ liệu để hiển thị
        </NoRecordsContent>
    </MudTable>

    <MudDialog Visible="isShowPreviewFile" Options="@(new DialogOptions() {  FullScreen = true } )">
        <DialogContent>
            <MudStack Row Justify="Justify.SpaceEvenly">
                <MudText Class="mt-3" Typo="Typo.h5" >@selectedFile.FileName</MudText>
                <MudIconButton Icon="@Icons.Material.Filled.Close" Color="Color.Error" OnClick="@(() =>isShowPreviewFile = false)" />
            </MudStack>
            <div style="height: 91vh" class="d-flex flex-column py-1">
                <MudBlazor.Extensions.Components.MudExFileDisplay FileName="NameOfYourFile.pdf" ContentType="@selectedFile.FileType" Url="@selectedFile.FileUrl"></MudBlazor.Extensions.Components.MudExFileDisplay>
            </div>
        </DialogContent>
    </MudDialog>



}