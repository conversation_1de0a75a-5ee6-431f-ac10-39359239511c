﻿using PurchaseManager.Shared.Dto.Email;
using PurchaseManager.Shared.Interfaces;
using System.Security.Authentication;

namespace PurchaseManager.UI.Base.Pages.Admin.Settings
{
    public class EmailPage : SettingsBase
    {
        protected bool isSendEmailDialogOpen = false;
        protected EmailRequestDto email { get; set; } = new EmailRequestDto();
        protected string[] sslProtocols = ((SslProtocols[])Enum.GetValues(typeof(SslProtocols))).Select(i => i.ToString()).ToArray();

        protected override async Task OnInitializedAsync()
        {
            await LoadSettings("EmailConfiguration_");
        }

        protected async Task SendTestEmail()
        {
            try
            {
                var apiResponse = await apiClient.SendTestEmail(email);

                if (apiResponse.IsSuccessStatusCode)
                {
                    viewNotifier.Show(apiResponse.Message, ViewNotifierType.Success);
                }
                else
                {
                    viewNotifier.Show(apiResponse.Message + " : " + apiResponse.StatusCode, ViewNotifierType.Error, L["Operation Failed"]);
                }
            }
            catch (Exception ex)
            {
                viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
        }
    }
}
