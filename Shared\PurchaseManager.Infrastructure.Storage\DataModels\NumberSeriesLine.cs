﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

[PrimaryKey("NumberSeriesCode", "Code")]
[Table("NumberSeriesLine")]
public partial class NumberSeriesLine
{
    [Key]
    [StringLength(100)]
    public string NumberSeriesCode { get; set; } = null!;

    [Key]
    [StringLength(100)]
    public string Code { get; set; } = null!;

    [StringLength(100)]
    public string StartingNumber { get; set; } = null!;

    public int EndingNumber { get; set; }

    public int WarningNumber { get; set; }

    public int IncrementByNumber { get; set; }

    public int LastNumberUsed { get; set; }

    [StringLength(100)]
    public string ProposalNumber { get; set; } = null!;

    public int Open { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime LastDateUsed { get; set; }

    [StringLength(100)]
    public string? SourceCode { get; set; }

    [StringLength(100)]
    public string? Description { get; set; }

    [StringLength(100)]
    public string? UserId { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? FromDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? ToDate { get; set; }

    public int? Type { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }

    [ForeignKey("NumberSeriesCode")]
    [InverseProperty("NumberSeriesLines")]
    public virtual NumberSeries NumberSeriesCodeNavigation { get; set; } = null!;
}
