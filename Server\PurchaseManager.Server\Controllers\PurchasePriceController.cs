using Microsoft.AspNetCore.Mvc;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Dto.Response;
using PurchaseManager.Shared.Models.PurchasePrice;
namespace PurchaseManager.Server.Controllers;

[SecurityHeaders]
[Route("api/[controller]")]
[ApiController]
public class PurchasePriceController : ControllerBase
{
    private readonly IPurchasePriceManager _purchasePriceManager;
    public PurchasePriceController(IPurchasePriceManager purchasePriceManager)
    {
        _purchasePriceManager = purchasePriceManager;
    }

    [HttpPost("validate-data")]
    public async Task<ApiResponse<List<ResponseWithDetailError>>> ValidateData(List<CreatePurchasePriceDto> fileUploaded)
        => await _purchasePriceManager.ValidateData(fileUploaded);

    [HttpPost("import-file-data")]
    public async Task<ApiResponse> ImportFile(List<CreatePurchasePriceDto> fileUploaded)
        => await _purchasePriceManager.ImportFileData(fileUploaded);

    [HttpPost("create")]
    public async Task<ApiResponse> CreatePurchasePrice([FromBody] CreatePurchasePriceDto createPurchasePriceDto)
        => await _purchasePriceManager.CreatePurchasePrice(createPurchasePriceDto);

    [HttpGet("gets")]
    public async Task<ApiResponse> GetPurchasePrices([FromQuery] PurchasePriceFilter filter)
        => await _purchasePriceManager.GetPurchasePrices(filter);

    [HttpGet("get/{priceNumber}")]
    public async Task<ApiResponse> GetPurchasePrice(string priceNumber)
        => await _purchasePriceManager.GetPurchasePrice(priceNumber);

    [HttpPut("update")]
    public async Task<ApiResponse> UpdatePurchasePrice([FromBody] UpdatePurchasePriceDto updatePurchasePriceDto)
        => await _purchasePriceManager.UpdatePurchasePrice(updatePurchasePriceDto);

    [HttpDelete("delete/{priceNumber}")]
    public async Task<ApiResponse> DeletePurchasePrice(string priceNumber)
        => await _purchasePriceManager.DeletePurchasePrice(priceNumber);

    [HttpGet("get-price-and-unit/{itemNumber}/{vendorNumber}")]
    public async Task<ApiResponse<List<GetPurchasePriceDto>>> GetPriceAndUnit(string itemNumber, string vendorNumber)
        => await _purchasePriceManager.GetPriceAndUnit(itemNumber, vendorNumber);

}
