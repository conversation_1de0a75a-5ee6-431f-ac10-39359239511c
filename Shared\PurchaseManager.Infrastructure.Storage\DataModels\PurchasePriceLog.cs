﻿namespace PurchaseManager.Infrastructure.Storage.DataModels;

public class PurchasePriceLog
{
    public int LogId { get; set; }

    public string LogAction { get; set; }

    public DateTime LogDate { get; set; }

    public string LogBy { get; set; }

    public string Number { get; set; }

    public string VendorNumber { get; set; }

    public string ItemNumber { get; set; }

    public DateTime? CreateAt { get; set; }

    public DateTime? LastUpdateAt { get; set; }

    public string CreateBy { get; set; }

    public string LastUpdateBy { get; set; }

    public int? Status { get; set; }

    public string PurchasingUnit { get; set; }

    public string PriceBefVat { get; set; }

    public string PriceAftVat { get; set; }

    public string FoCpromotion { get; set; }

    public string PriceAftDiscountNoVat { get; set; }

    public string PriceAftDiscountVat { get; set; }

    public string Pic { get; set; }

    public int? DiscountBySkus { get; set; }

    public string ItemCategory { get; set; }

    public string GroupProduct { get; set; }

    public int? Vat { get; set; }
}
