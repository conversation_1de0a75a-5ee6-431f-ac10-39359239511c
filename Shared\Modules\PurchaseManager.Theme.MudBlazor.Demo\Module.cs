﻿using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models;
using PurchaseManager.Theme.Material.Demo.Shared.Components;
using PurchaseManager.Theme.Material.Demo.TagHelpers;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Microsoft.Extensions.DependencyInjection;

namespace PurchaseManager.Theme.Material.Demo
{
    public class Module : BaseModule
    {
        public override string Description => "PurchaseManager demo";

        public override int Order => 2;

        private void Init(IServiceCollection services)
        {
            services.AddSingleton<IDynamicComponent, NavMenu>();
            services.AddSingleton<IDynamicComponent, Footer>();
            services.AddSingleton<IDynamicComponent, DrawerFooter>();
            services.AddSingleton<IDynamicComponent, TopRightBarSection>();
        }

        public override void ConfigureServices(IServiceCollection services)
        {
            services.AddTransient<ITagHelperComponent, ThemeTagHelperComponent>();
            Init(services);
        }

        public override void ConfigureWebAssemblyServices(IServiceCollection services)
        {
            Init(services);
        }
    }
}
