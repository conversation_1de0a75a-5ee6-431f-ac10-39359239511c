﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Table("FileStorages")]
public class FileStorage
{
    [Key]
    [StringLength(50)]
    public string FileId { get; set; } = null!;
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }
    [StringLength(50)]
    public string FileName { get; set; }
    [StringLength(50)]
    public string FileType { get; set; }
    [StringLength(500)]
    public string FileUrl { get; set; }
    public DateTime UploadDate { get; set; }
    [StringLength(500)]
    public string Description { get; set; }
    [StringLength(100)]
    public string LoginId { get; set; }
    public bool Blocked { get; set; }
    public int Status { get; set; }
    [StringLength(50)]
    public string LastUserModified { get; set; }
    public DateTime? LastDateModified { get; set; }
    [StringLength(250)]
    public string DocumentNo { get; set; }
    [StringLength(500)]
    public string Prefix { get; set; }
}
