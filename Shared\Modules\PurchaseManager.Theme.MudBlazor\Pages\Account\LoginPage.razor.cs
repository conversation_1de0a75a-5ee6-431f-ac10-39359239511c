﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Providers;
using PurchaseManager.Shared.Services;
namespace PurchaseManager.Theme.Material.Pages.Account;

public partial class LoginPage : ComponentBase
{
    [Inject]
    private NavigationManager NavigationManager { get; set; }
    [Inject]
    private AuthenticationStateProvider AuthStateProvider { get; set; }
    [Inject] protected AppState AppState { get; set; }
    [Inject] protected HttpClient HttpClient { get; set; }
    [Inject] protected IStringLocalizer<Global> L { get; set; }
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }

    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; }

    protected bool IsShowAlertForgetPassword { get; set; }
    private IdentityAuthenticationStateProvider _identityAuthenticationStateProvider;
    protected LoginViewModel LoginViewModel;
    protected ForgotPasswordViewModel ForgotPasswordViewModel { get; set; } = new ForgotPasswordViewModel();

    private string _returnUrl;

    protected override async Task OnInitializedAsync()
    {
        if (NavigationManager.TryGetQueryString("ReturnUrl", out string url))
        {
            _returnUrl = url;
        }

        var user = (await AuthenticationStateTask).User;

        if (user.Identity is { IsAuthenticated: true })
        {
            NavigationManager.NavigateTo(_returnUrl ?? "/");
        }
        else
        {
            _identityAuthenticationStateProvider = (IdentityAuthenticationStateProvider)AuthStateProvider;

            try
            {
                var apiResponse = await _identityAuthenticationStateProvider.BuildLoginViewModel(_returnUrl);

                if (apiResponse.IsSuccessStatusCode)
                {
                    LoginViewModel = apiResponse.Result;

                    if (LoginViewModel.IsExternalLoginOnly)
                    {
                        if (!string.IsNullOrEmpty(_returnUrl))
                        {
                            _returnUrl = Uri.EscapeDataString(_returnUrl);
                        }
                        // we only have one option for logging in and it's an external provider
                        NavigationManager.NavigateTo(
                        $"{HttpClient.BaseAddress}api/externalauth/challenge/{LoginViewModel.ExternalLoginScheme}/{_returnUrl}", true);
                    }
                }
                else
                {
                    ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Error, L["LoginFailed"]);
                }
            }
            catch (Exception ex)
            {
                ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["LoginFailed"]);
            }
        }
    }


    private void VendorLogin() => NavigationManager.NavigateTo("/account-login");
    private void TsCareLogin() => NavigationManager.NavigateTo("https://tshrm.trungsonpharma.com/redirect-erp/auth");
}
