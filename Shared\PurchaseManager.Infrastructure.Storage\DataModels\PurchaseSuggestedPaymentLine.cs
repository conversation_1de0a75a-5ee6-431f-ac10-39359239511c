﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

[PrimaryKey("DocumentType", "DocumentNumber", "LineNumber", "Number", "UnitOfMeasure")]
[Table("PurchaseSuggestedPaymentLine")]
public class PurchaseSuggestedPaymentLine
{
    [Key]
    public int DocumentType { get; set; }

    [Key]
    [StringLength(100)]
    public string DocumentNumber { get; set; } = string.Empty;

    [Key]
    public int LineNumber { get; set; }

    [Required]
    [StringLength(100)]
    public string? BuyFromVendorNumber { get; set; }

    [StringLength(2500)]
    public string Tags { get; set; } = string.Empty;

    [StringLength(50)]
    public string Rate { get; set; } = "0";

    public int Type { get; set; }

    [Key]
    [StringLength(100)]
    public string Number { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string LocationCode { get; set; } = string.Empty;

    [Column(TypeName = "datetime")]
    public DateTime ExpectedReceiptDate { get; set; }

    [Required]
    [StringLength(600)]
    public string Description { get; set; } = string.Empty;

    [Key]
    [StringLength(100)]
    public string UnitOfMeasure { get; set; } = string.Empty;

    [Column(TypeName = "decimal(28, 10)")]
    public decimal Quantity { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal OutstandingQuantity { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal QtyToInvoice { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal QtyToReceive { get; set; }

    [Column("VAT")]
    public string? Vat { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal LineDiscount { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal LineDiscountAmount { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal Amount { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal GrossWeight { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal NetWeight { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal UnitsPerParcel { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal UnitVolume { get; set; }

    [Required]
    [StringLength(100)]
    public string JobNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string PhaseCode { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string TaskCode { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string StepCode { get; set; } = string.Empty;

    [Column(TypeName = "decimal(28, 10)")]
    public decimal OutstandingAmount { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal QuantityReceived { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal QuantityInvoiced { get; set; }

    [Required]
    [StringLength(100)]
    public string ReceiptNumber { get; set; } = string.Empty;

    [Column(TypeName = "decimal(28, 10)")]
    public decimal Profit { get; set; }

    [Required]
    [StringLength(100)]
    public string PayToVendorNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string TransactionType { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string TransportMethod { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string EntryPoint { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string Area { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string TransactionSpecification { get; set; } = string.Empty;

    [Required]
    [Column("VATBusPostingGroup")]
    [StringLength(100)]
    public string VatbusPostingGroup { get; set; } = string.Empty;

    [Required]
    [Column("VATProdPostingGroup")]
    [StringLength(100)]
    public string VatprodPostingGroup { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string CurrencyCode { get; set; } = string.Empty;

    [Column("VATAmount", TypeName = "decimal(28, 10)")]
    public decimal Vatamount { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal UnitCost { get; set; }

    public int Status { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal LineAmount { get; set; }

    [Column("VATBaseAmount", TypeName = "decimal(28, 10)")]
    public decimal VatbaseAmount { get; set; }

    [Required]
    [StringLength(100)]
    public string ProdOrderNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string VariantCode { get; set; } = string.Empty;

    public int QtyPerUnitOfMeasure { get; set; }

    [Required]
    [StringLength(100)]
    public string CrossReferenceNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string PurchasingCode { get; set; } = string.Empty;

    [Column(TypeName = "datetime")]
    public DateTime RequestedReceiptDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime PromisedReceiptDate { get; set; }

    [Required]
    [StringLength(64)]
    public string LeadTimeCalculation { get; set; } = string.Empty;

    [Required]
    [Column("InboundWahouesHandling Time")]
    [StringLength(64)]
    public string InboundWahouesHandlingTime { get; set; } = string.Empty;

    [Column(TypeName = "datetime")]
    public DateTime PlannedReceiptDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime OrderDate { get; set; }

    [Required]
    [StringLength(100)]
    public string ReturnReasonCode { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string RoutingNumbre { get; set; } = string.Empty;

    [Required]
    [Column("OperationNo_")]
    [StringLength(100)]
    public string OperationNo { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string WorkCenterNumber { get; set; } = string.Empty;

    [Column(TypeName = "decimal(28, 10)")]
    public decimal OverheadRate { get; set; }

    public int PlanningFlexibility { get; set; }

    [Required]
    [StringLength(64)]
    public string SafetyLeadTime { get; set; } = string.Empty;

    public int RoutingReferenceNumber { get; set; }

    [Required]
    [StringLength(100)]
    public string OriginalCountry { get; set; } = string.Empty;

    public int ApplyToItemEntry { get; set; }

    [Required]
    [Column("VATRegistrationNumber")]
    [StringLength(100)]
    public string VatregistrationNumber { get; set; } = string.Empty;

    [Required]
    [Column("VATDescription")]
    [StringLength(100)]
    public string Vatdescription { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string ExternalDocumentNo { get; set; } = string.Empty;

    [StringLength(100)]
    public string? ReasonCode { get; set; }

    [Required]
    [StringLength(100)]
    public string CertificateNumber { get; set; } = string.Empty;

    [Column(TypeName = "datetime")]
    public DateTime IssueDate { get; set; }

    [Required]
    [StringLength(100)]
    public string PlaceOfIssue { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string BankAddress { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string BankCode { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string BankName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string PersonIncharge { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string SalespersonCode { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string LotNumber { get; set; } = string.Empty;

    public DateOnly? ExpirationDate { get; set; }

    [StringLength(200)]
    public string ExperimentNumber { get; set; } = string.Empty;

    [Column(TypeName = "decimal(25, 10)")]
    public decimal? Usage { get; set; }

    [StringLength(50)]
    public string PostingGroup { get; set; } = string.Empty;

    [StringLength(50)]
    public string ItemClasses { get; set; } = string.Empty;

    public int? PurchaseType { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal? QuantityShipped { get; set; }

    public int RequestQuantity { get; set; }

    public int ConfirmQuantity { get; set; }

    public int? CreditType { get; set; }

    [Column("RowID")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }

    [ForeignKey(nameof(DocumentNumber))]
    [InverseProperty("PurchaseSuggestedPaymentLines")]
    public virtual PurchaseSuggestedPaymentHeader DocumentNumberNavigation { get; set; }

    [ForeignKey(nameof(ReasonCode))]
    [InverseProperty("PurchaseSuggestedPaymentLines")]
    public virtual DemandReason ReasonCodeNavigation { get; set; }

    [ForeignKey(nameof(Number))]
    [InverseProperty("PurchaseSuggestedPaymentLines")]
    public virtual Item ItemNumberNavigation { get; set; }
}
