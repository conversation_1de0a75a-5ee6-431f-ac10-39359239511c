using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Contact;
using PurchaseManager.Shared.Models.Contact;
namespace PurchaseManager.Infrastructure.Server;
public interface IContactManager
{
    IQueryable<GetContactDto> GetAllContacts(ContactFilter filter);
    Task<ApiResponse<GetContactDto>> GetContactByNumberAsync(string number);
    Task<ApiResponse> CreateContactAsync(CreateContactDto dto);
    Task<ApiResponse> UpdateContactAsync(UpdateContactDto dto);
    Task<ApiResponse> BlockOrUnblockContactAsync(string number);
    ApiResponse GetAllContactsByVendorNumber(string vendorNumber);
    Task<ApiResponse> SearchAutocomplete(string number, string name, string queryString);
    
}
