@inherits EditUOMPage
@page "/unit-of-measure/{UnitOfMeasureCode}"
@page "/unit-of-measure/create"
@page "/unit-of-measure"
@attribute [Authorize(Policies.IsAdmin)]

@if (isLoading)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else if (isNotFound)
{
    <PageNotFound /> 
}
else
{
    <div class="mb-2">
        <MudButton Color="Color.Tertiary" StartIcon="@Icons.Material.Filled.ArrowBack"
                   OnClick="@(() => {navigation.NavigateTo("/unit-of-measures");})" Variant="Variant.Text">Back to list</MudButton>
    </div>
    <EditForm EditContext="editContext" OnValidSubmit="OnSubmitForm">
        <DataAnnotationsValidator />
        <MudCard>
            <MudCardHeader Class="">
                <MudText Typo="Typo.h5" Align="Align.Center" Class="mx-auto">
                    @(isCreate ? "Create New" : "") Unit Of
                    Measure
                    @(isCreate ? "" : "Detail")
                </MudText>
                @if (isSaving)
                {
                    <div>
                        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                        <MudText Class="ms-2">@L["Saving"]</MudText>
                    </div>
                }
                else
                {
                    <MudButton StartIcon="@Icons.Material.Filled.Save"
                               Disabled="@((!editContext.IsModified() ))" ButtonType="ButtonType.Submit"
                               Variant="Variant.Filled" Color="Color.Primary" Class="m-auto ">
                        <MudText>@L["Save"]</MudText>
                    </MudButton>
                }
            </MudCardHeader>
            <MudCardContent>
                <MudGrid>
                    <MudItem sm="@(isCreate? 6: 4)">
                        <MudTextField ReadOnly="@(!isCreate)" Label="Code" HelperText="" @bind-Value="item.Code" For="@(() => item.Code)" />
                    </MudItem>
                    @if (!isCreate)
                    {
                        <MudItem sm="4">
                            <MudNumericField T="int?" Min="0" Step="1" Max="2" Label="Status" HideSpinButtons HelperText="" @bind-Value="item.Status" For="@(() => item.Status)" />
                        </MudItem>
                    }
                    <MudItem sm="@(isCreate? 6: 4)">
                        <MudTextField Label="Description" HelperText="" @bind-Value="item.Description" For="@(() => item.Description)" />
                    </MudItem>
                </MudGrid>
            </MudCardContent>
        </MudCard>
    </EditForm>
}