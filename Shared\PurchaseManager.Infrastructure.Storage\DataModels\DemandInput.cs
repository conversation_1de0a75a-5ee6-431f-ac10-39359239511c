﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
namespace PurchaseManager.Infrastructure.Storage.DataModels;
[Table("DemandInput")]
[PrimaryKey("VendorNumber", "ItemN<PERSON>ber", "RowId")]

public class DemandInput
{
    [Key]
    [Column(Order = 0)]
    [StringLength(100)]
    public string VendorNumber { get; set; }
    [Key]
    [Column(Order = 1)]
    [StringLength(100)]
    public string ItemNumber { get; set; }
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }
    [Required]
    [StringLength(100)]
    public string ItemName { get; set; }
    [Required]
    [StringLength(50)]
    public string PurchaseUnitOfMeasure { get; set; }
    public int Quantity { get; set; }
    [Required]
    public DateTime CreateDate { get; set; }
    public int QuantityPerUnitOfMeasure { get; set; }
    public string? BaseUnit { get; set; }
}
