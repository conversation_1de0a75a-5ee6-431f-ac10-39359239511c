﻿@page "/"
@inject IStringLocalizer<Global> L
@attribute [Authorize]
<MudStack Class="my-8" Row Justify="Justify.Center">
    <MudImage Src="images/logo/logo-trungson.png" Width="120" Alt="logo-trungson" Elevation="0"
              Class="rounded-lg ma-4"/>
</MudStack>
<MudText Align="Align.Center" Typo="Typo.h5">ORDER MANAGER SYSTEM</MudText>

<AuthorizeView Policy="@Policies.IsVendor">
    <PurchaseManager.Theme.Material.Demo.Pages.Vendors.VendorDashboard />
</AuthorizeView>
<AuthorizeView Policy="@Policies.IsPurchaseUser">
    <MudGrid>
        <MudItem xs="6">
            <MudCard Elevation="1">
                <MudCardHeader Class="mud-primary-text">
                    <CardHeaderActions>
                        <MudIconButton OnClick="ReloadViewReplenishment" Icon="@Icons.Material.Outlined.Refresh"
                            Size="Size.Medium" />
                    </CardHeaderActions>
                    <CardHeaderContent>
                        @L["Replenishment Orders"]
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Style="height: 200px;" Row="false" Justify="Justify.Center">
                        <MudStack Justify="Justify.Center" Spacing="2" AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h2" Color="Color.Default">
                                <strong>@CountReplenishmentOrders.AutoOrder</strong>
                            </MudText>
                            <MudChip T="String" Color="Color.Primary">Replenishment Orders</MudChip>
                        </MudStack>
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>
        <MudItem xs="6">
            <MudCard Elevation="1">
                <MudCardHeader Class="mud-primary-text">
                    <CardHeaderActions>
                        <MudDatePicker Margin="Margin.Dense" Variant="Variant.Outlined" Date="@Date"
                            DateChanged="ChangeDateViewLeadTime"></MudDatePicker>
                    </CardHeaderActions>
                    <CardHeaderContent>
                        @L["Lead Time"]
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudTable T="PurchaseOrderHeader" Items="@ListDemand" Height="152px" Dense="true" Elevation="0">
                        <RowTemplate Context="Row">
                            <MudTd DataLabel="PO">@Row.Number</MudTd>
                            <MudTd DataLabel="Vendor">@Row.BuyFromVendorName</MudTd>
                        </RowTemplate>
                    </MudTable>
                </MudCardContent>
                <MudCardActions Class="d-flex justify-end">
                    <MudButton Variant="Variant.Text" Color="Color.Primary"
                               OnClick="@(() => NavigationManager.NavigateTo("/schedule"))">more..
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>
        <MudItem xs="6">
            <MudCard Elevation="1">
                <MudCardHeader Class="mud-primary-text">
                    <CardHeaderActions>
                        <MudIconButton Icon="@Icons.Material.Outlined.Refresh" OnClick="ReloadViewFinished" />
                    </CardHeaderActions>
                    <CardHeaderContent>
                        @L["Purchase Orders Finished"]
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Style="height: 200px;" Row="false" Justify="Justify.Center">
                        <MudStack Justify="Justify.Center" Spacing="0" AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h2" Color="Color.Default"><strong>@countOrdersFinish</strong> </MudText>
                            <MudChip T="string" Color="Color.Success">Orders Finished</MudChip>
                        </MudStack>
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>
        <MudItem xs="6">
            <MudCard Elevation="1">
                <MudCardHeader Class="mud-primary-text">
                    <CardHeaderActions>
                        <MudIconButton Icon="@Icons.Material.Outlined.Refresh" OnClick="ReloadViewOrders" />
                    </CardHeaderActions>
                    <CardHeaderContent>
                        @L["Purchase Orders"]
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Style="height: 200px;">
                        <MudList T="string">
                            <MudListItem>
                                <AvatarContent>
                                    <MudAvatar Color="Color.Tertiary"> @(OrderByType.Order.ToString())</MudAvatar>
                                </AvatarContent>
                                <ChildContent>@L["Orders"]</ChildContent>
                            </MudListItem>
                            <MudListItem>
                                <AvatarContent>
                                    <MudAvatar Color="Color.Info"> @(OrderByType.Consigned.ToString())</MudAvatar>
                                </AvatarContent>
                                <ChildContent>@L["Consigned"]</ChildContent>
                            </MudListItem>
                            <MudListItem>
                                <AvatarContent>
                                    <MudAvatar Color="Color.Success"> @(OrderByType.Promotional.ToString())</MudAvatar>
                                </AvatarContent>
                                <ChildContent>@L["Promotional"]</ChildContent>
                            </MudListItem>
                        </MudList>
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</AuthorizeView>
<AuthorizeView Policy="@Policies.IsWarehouseUser">
    <MudGrid Class="mt-3">
        <MudItem xs="6">
            <MudCard Elevation="1">
                <MudCardHeader Class="mud-primary-text">
                    <CardHeaderActions>
                        <MudIconButton OnClick="ReloadViewReplenishment" Icon="@Icons.Material.Outlined.Refresh"
                                       Size="Size.Medium"/>
                    </CardHeaderActions>
                    <CardHeaderContent>
                        @L["Replenishment Orders"]
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Style="height: 200px;" Row="false" Justify="Justify.Center">
                        <MudStack Justify="Justify.Center" Spacing="2" AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h2" Color="Color.Default">
                                <strong>@CountReplenishmentOrders.AutoOrder</strong>
                            </MudText>
                            <MudChip T="String" Color="Color.Primary">Replenishment Orders</MudChip>
                        </MudStack>
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>
        <MudItem xs="6">
            <MudCard Elevation="1">
                <MudCardHeader Class="mud-primary-text">
                    <CardHeaderActions>
                        <MudDatePicker Margin="Margin.Dense" Variant="Variant.Outlined" Date="@Date"
                                       DateChanged="ChangeDateViewLeadTime"></MudDatePicker>
                    </CardHeaderActions>
                    <CardHeaderContent>
                        @L["Lead Time"]
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudTable T="PurchaseOrderHeader" Items="@ListDemand" Height="152px" Dense="true" Elevation="0">
                        <RowTemplate Context="row">
                            <MudTd DataLabel="PO">
                                <MudLink Href="@($"/stock/{@row.Number}/detail")">@row.Number</MudLink>
                            </MudTd>
                            <MudTd DataLabel="Vendor">@row.BuyFromVendorName</MudTd>
                        </RowTemplate>
                    </MudTable>
                </MudCardContent>
                <MudCardActions Class="d-flex justify-end">
                    <MudButton Variant="Variant.Text" Color="Color.Primary"
                               OnClick="@(() => NavigationManager.NavigateTo("/schedule"))">more..
                    </MudButton>
                </MudCardActions>
            </MudCard>
        </MudItem>
        <MudItem xs="6">
            <MudCard Elevation="1">
                <MudCardHeader Class="mud-primary-text">
                    <CardHeaderActions>
                        <MudIconButton Icon="@Icons.Material.Outlined.Refresh" OnClick="ReloadViewFinished"/>
                    </CardHeaderActions>
                    <CardHeaderContent>
                        @L["Purchase Orders Finished"]
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Style="height: 200px;" Row="false" Justify="Justify.Center">
                        <MudStack Justify="Justify.Center" Spacing="0" AlignItems="AlignItems.Center">
                            <MudText Typo="Typo.h2" Color="Color.Default"><strong>@countOrdersFinish</strong></MudText>
                            <MudChip T="String" Color="Color.Success">Orders Finished</MudChip>
                        </MudStack>
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>
        <MudItem xs="6">
            <MudCard Elevation="1">
                <MudCardHeader Class="mud-primary-text">
                    <CardHeaderActions>
                        <MudIconButton Icon="@Icons.Material.Outlined.Refresh" OnClick="ReloadViewOrders"/>
                    </CardHeaderActions>
                    <CardHeaderContent>
                        @L["Purchase Orders"]
                    </CardHeaderContent>
                </MudCardHeader>
                <MudCardContent>
                    <MudStack Style="height: 200px;">
                        <MudList T="String">
                            <MudListItem>
                                <AvatarContent>
                                    <MudAvatar Color="Color.Tertiary"> @(OrderByType.Order.ToString())</MudAvatar>
                                </AvatarContent>
                                <ChildContent>@L["Orders"]</ChildContent>
                            </MudListItem>
                            <MudListItem>
                                <AvatarContent>
                                    <MudAvatar Color="Color.Info"> @(OrderByType.Consigned.ToString())</MudAvatar>
                                </AvatarContent>
                                <ChildContent>@L["Consigned"]</ChildContent>
                            </MudListItem>
                            <MudListItem>
                                <AvatarContent>
                                    <MudAvatar Color="Color.Success"> @(OrderByType.Promotional.ToString())</MudAvatar>
                                </AvatarContent>
                                <ChildContent>@L["Promotional"]</ChildContent>
                            </MudListItem>
                        </MudList>
                    </MudStack>
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</AuthorizeView>
