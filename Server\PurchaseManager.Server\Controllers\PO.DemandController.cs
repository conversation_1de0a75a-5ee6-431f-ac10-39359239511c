﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Dto.QueueGenPO;
namespace PurchaseManager.Server.Controllers;

public partial class PurchaseOrderController
{
    /// <summary>
    ///     Creates a new purchase order based on the provided details.
    ///     This method is used to create a new purchase order.
    /// </summary>
    /// <param name="purchaseOrder">A list of purchase order details represented by <see cref="DetailPoDto" />.</param>
    /// <returns>An asynchronous operation that returns an <see cref="ApiResponse" /> indicating the success or failure of the creation process.</returns>
    [AllowAnonymous]
    [HttpPost("CreatePO")]
    public async Task<ApiResponse> CreatePo(List<DetailPoDto> purchaseOrder)
    {
        return await _queueGenPurchaseOrderManager.CreateQueueGenPurchaseOrder(purchaseOrder);
    }

    [AllowAnonymous]
    [HttpPost("Demand")]
    public async Task<ApiResponse> CreateQueueGenPurchaseOrderDataInput(List<CreateQueueGenPoDemandDto> detailPoDtos)
    {
        return await _queueGenPurchaseOrderManager.CreateQueueGenPurchaseOrderDataInput(detailPoDtos);
    }

    [AllowAnonymous]
    [HttpPost("Demand-v2")]
    public async Task<ApiResponse> CreateQueueGenPurchaseOrderDemandV2(List<ApproveDocumentDto> approveDocumentDtos)
    {
        return await _queueGenPurchaseOrderManager.CreateQueueGenPurchaseOrderDemandV2(approveDocumentDtos);
    }
}
