﻿using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Validators;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace PurchaseManager.Shared.Dto.Email.Validators;

public class EmailDtoValidator : LocalizedAbstractValidator<EmailRequestDto>
{
    public EmailDtoValidator(IStringLocalizer<Global> l) : base(l)
    {
        RuleFor(x => x.ToAddresses)
            .NotEmpty().WithMessage("The list of email addresses cannot be empty.")
            .Must(list => list is { Count: > 0 }).WithMessage("The list of email addresses cannot be empty.");
    }
}
