﻿using SourceGenerators;
namespace PurchaseManager.Shared.Models.PO;

public partial class PurchaseOrderFilter : QueryParameters
{
    [AutoNotify]
    private DateTime? _month;
    [AutoNotify]
    private string? _vendor;
    [AutoNotify]
    private string? _contactNumber;
    [AutoNotify]
    private DateTime? _fromDate;
    [AutoNotify]
    private DateTime? _toDate;
    [AutoNotify]
    private int? _pageSize;
    [AutoNotify]
    private int? _status;
    [AutoNotify]
    public string? _query;
    [AutoNotify]
    private string? _number;
    [AutoNotify]
    private int? _pageIndex;
    [AutoNotify]
    private string? _description;
    [AutoNotify]
    private int? _statusFilter;
    [AutoNotify]
    private string? itemNumber;
    [AutoNotify]
    public bool? isMKT;
}
