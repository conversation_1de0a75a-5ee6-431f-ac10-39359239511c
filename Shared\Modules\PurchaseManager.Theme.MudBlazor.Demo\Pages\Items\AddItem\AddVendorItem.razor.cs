using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Item;

namespace PurchaseManager.Theme.Material.Demo.Pages.Items.AddItem;

public partial class AddVendorItemPageBase : ComponentBase
{
    [Parameter]
    public string VendorNumber { get; set; }

    [Parameter]
    public EventCallback<bool> OnVendorItemAdded { get; set; }

    [Inject]
    protected IApiClient apiClient { get; set; }

    [Inject]
    protected IViewNotifier viewNotifier { get; set; }

    [Inject]
    protected IStringLocalizer<Global> L { get; set; }

    [Inject]
    protected NavigationManager navigation { get; set; }


    protected CreateVendorItemDto createVendorItemDto { get; set; } = new CreateVendorItemDto();
    protected bool isLoading { get; set; }
    protected List<UnitOfMeasureDto> listUnitOfMeasureDto { get; set; } = new List<UnitOfMeasureDto>();

    protected VendorItemFilter vendorItemFilter { get; set; } = new VendorItemFilter();

    protected override async Task OnInitializedAsync()
    {
        isLoading = true;
        createVendorItemDto.VendorNumber = VendorNumber;
        await LoadAllUOM();
        await base.OnInitializedAsync();
        isLoading = false;
    }

    protected async Task OnCreateNewVendorItemSubmit()
    {
        try
        {
            isLoading = true;
            createVendorItemDto.VendorNumber = VendorNumber;
            var resp = await apiClient.CreateVendorItem(createVendorItemDto);
            if (resp.IsSuccessStatusCode)
            {
                viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation successful"]);
                await OnVendorItemAdded.InvokeAsync(true);
            }
            else
            {
                viewNotifier.Show(resp.Message, ViewNotifierType.Error, L["CreateNewVendorItemFailed"]);
                await OnVendorItemAdded.InvokeAsync(false);
            }
        }
        catch (Exception ex)
        {
            await OnVendorItemAdded.InvokeAsync(false);
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["CreateNewVendorItemFailed"]);
        }
        finally
        {
            isLoading = false;
        }
    }
    protected async Task LoadAllUOM()
    {
        try
        {
            var resp = await apiClient.GetAllUnitOfMeasure(null, null, null);
            listUnitOfMeasureDto = resp.ToList();
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["LoadUOMFailed"]);
        }
    }
}
