﻿using PurchaseManager.Shared.Dto.Vendor;

namespace PurchaseManager.Shared.Dto.Item;

public class DetailVendorItemDto
{
    public int RowId { get; set; }
    public string Name { get; set; }
    public string VendorNumber { get; set; }
    public string ItemNumber { get; set; }
    public string UnitOfMeasure { get; set; }
    public decimal? Price { get; set; }
    public int? Vat { get; set; }
    public GetVendorDto GetVendor { get; set; }
    public string CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public string LastUpdatedBy { get; set; }
    public DateTime? LastUpdatedAt { get; set; }
    public int? Blocked { get; set; }
    public string Description { get; set; }
}
