﻿using System.Globalization;
using System.Net.Http.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using MudBlazor.Extensions.Components;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Models.PO;
using PurchaseManager.Shared.Providers;
using PurchaseManager.UI.Base.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Pages.PromotionOrders;

public class PromotionalBasePage : BaseComponent
{
    protected bool IsLoading { get; set; }
    [Inject] protected HttpClient HttpClient { get; set; }
    protected MudDateRangePicker FromToDatePickerRef { get; set; } = new MudDateRangePicker();
    protected DateRange DateRange { get; set; } = new DateRange();
    [Inject]
    protected IPurchaseOrderApiClient PurchaseOrderApiClient { get; set; }
    protected MudAutocomplete<GetVendorDto> AutoComplete { get; set; }
    protected List<POHeaderGetDto> PurchaseHeaders { get; set; } = [];
    protected POHeaderGetDto PurchaseHeaderSelected { get; set; } = new POHeaderGetDto();
    protected MudExList<POHeaderGetDto> MudExListRef { get; set; }
    protected GetVendorDto SelectedVendor { get; set; }
    protected string DocumentNo { get; set; }
    private DocNoOccurrenceEnum DocNoOccurrence { get; set; } = DocNoOccurrenceEnum.Promotional;
    protected UserViewModel UserViewModel { get; set; } = new UserViewModel();
    [Inject]
    private AuthenticationStateProvider AuthStateProvider { get; set; }

    protected override void OnInitialized()
    {
        IsLoading = true;
        var today = DateTime.Today;
        var month = new DateTime(today.Year, today.Month, 1);
        // the last day of the current month
        var last = month.AddMonths(1).AddDays(-1);
        //fromToDatePickerRef.DateRange = new DateRange(first, last);
        DateRange = new DateRange(month, last);

        base.OnInitialized();
        IsLoading = false;
    }
    protected override async Task OnInitializedAsync()
    {
        UserViewModel = await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
        await base.OnInitializedAsync();
    }
    protected async Task<IEnumerable<GetVendorDto>> ItemSearch(string value, CancellationToken token)
    {
        var apiResponse =
            await HttpClient.GetFromJsonAsync<ApiResponseDto<List<GetVendorDto>>>($"api/vendor/Search?number={value}", token);
        if (apiResponse.IsSuccessStatusCode)
        {
            return apiResponse.Result;
        }
        return new List<GetVendorDto>();
    }
    protected async Task LoadData()
    {
        try
        {
            PurchaseHeaders = [];//clear list
            var filters = new PurchaseOrderFilter
            {
                FromDate = DateRange.Start, ToDate = DateRange.End, Vendor = AutoComplete.Value.Number
            };

            var response = await PurchaseOrderApiClient.GetPoHeadersAsync(filters);

            if (!response.IsSuccessStatusCode)
            {
                viewNotifier.Show(L[response.Message], ViewNotifierType.Error, L["Operation Failed"]);
            }
            else if (response.Result is not null)
            {
                PurchaseHeaders = new List<POHeaderGetDto>(response.Result.Data.ToList());
                viewNotifier.Show(response.Result.Data.Count + " item found", ViewNotifierType.Success, L["Operation Successful"]);
            }
            else
            {
                viewNotifier.Show(L["No item found"], ViewNotifierType.Warning, L["Operation Successful"]);
            }

        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    protected async Task ShowPOByVendorNumber(GetVendorDto vendor)
    {
        if (vendor is null || vendor.Number.Length < 5)
        {
            await Task.CompletedTask;
        }
        else
        {
            SelectedVendor = vendor;
            await LoadData();
        }
    }
    protected async Task FilterByDate()
    {
        if (AutoComplete.Value == null)
        {
            viewNotifier.Show(L["Vui lòng chọn NCC"], ViewNotifierType.Warning, L["Operation Successful"]);
            return;
        }
        if (FromToDatePickerRef.DateRange is null)
        {
            return;
        }
        DateRange = FromToDatePickerRef.DateRange;
        await FromToDatePickerRef.CloseAsync();
        await LoadData();
    }

    protected void ShowDetail(POHeaderGetDto detail)
    {

        DocumentNo = detail.Number;
        PurchaseHeaderSelected = PurchaseHeaders.Find(x => x.Number == detail.Number);
    }
    protected async Task CreatePromotionalOrders()
    {
        var apiResponse = await HttpClient.PostJsonAsync<ApiResponseDto<IEnumerable<POHeaderGetDto>>>(
        $"/api/purchaseOrder/createheader?vendornumber={PurchaseHeaderSelected.BuyFromVendorNumber}", null);

        if (!apiResponse.IsSuccessStatusCode)
        {
            viewNotifier.Show(apiResponse.Message, ViewNotifierType.Error);
            return;
        }

        var headers = apiResponse.Result.FirstOrDefault();
        if (DocNoOccurrence == DocNoOccurrenceEnum.Promotional)
        {
            if (headers != null)
            {
                var headerDto = new PurchaseOrderModels.Header.FullUpdateHeader
                {
                    Number = headers.Number,
                    DueDate = headers.DueDate.ToString(CultureInfo.InvariantCulture),
                    OrderDate = headers.OrderDate.ToString(CultureInfo.InvariantCulture),
                    PurchaseUser = UserViewModel.UserName,
                    VendorNo_ = headers.BuyFromVendorNumber,
                    DocNoOccurrence = (int)DocNoOccurrenceEnum.Promotional,
                    YourReference = PurchaseHeaderSelected.Number,
                    PostingDescription = "",
                    PurchaserApprovalBy = "",
                    VendorApprovalBy = "",
                };
                await HttpClient.PutJsonAsync<ApiResponseDto<int>>(
                $"/api/purchaseorder/updateheader?number={headers.Number}", headerDto);
            }

        }
        if (headers != null)
        {
            navigationManager.NavigateTo($"/po/{headers.Number}/detail");
        }
    }
}
