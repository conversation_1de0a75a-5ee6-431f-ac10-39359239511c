@using Microsoft.AspNetCore.Components.Forms
<EditForm OnValidSubmit="OnValidSubmit" Model="currentVendor" @ref="@form">
    <FluentValidationValidator />
    <MudToolBar Class="pa-0">
        <AuthorizeView Policy="@Policies.IsAdmin" Context="AdminContext">
            <Authorized>
                <MudSpacer />
                <MudButton Disabled="@(!form.EditContext.IsModified())" ButtonType="ButtonType.Submit"
                    Color="Color.Primary" Variant="Variant.Filled">@L["Save"]</MudButton>
            </Authorized>
        </AuthorizeView>
    </MudToolBar>
    <MudGrid Spacing="7">
        <MudItem xs="8">
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudStack Row Justify="Justify.FlexStart" AlignItems="AlignItems.Center">
                            <AuthorizeView Policy="@Policies.IsAdmin" Context="AdminContext">
                                <Authorized>
                                    <MudIconButton Color="Color.Default" Icon="@Icons.Material.Filled.ArrowBack"
                                        OnClick="@OnNavigateToListVendor" Variant="Variant.Text" />
                                </Authorized>
                                <NotAuthorized>
                                </NotAuthorized>
                            </AuthorizeView>
                            <MudText Typo="Typo.h5">@L["General"]</MudText>
                        </MudStack>
                    </CardHeaderContent>
                    <CardHeaderActions>
                        <AuthorizeView Policy="@Policies.IsAdmin" Context="AdminContext">
                            <Authorized>

                                @{
                                    var isBlock = currentVendor.Blocked == 1;
                                    <MudTooltip Placement="Placement.Top"
                                        Text="@($"Click to {(isBlock ? "active" : "block")}")">
                                        <MudChip T="string" OnClick="@(async _ => await ToggleVendorStatus())"
                                            Icon="@(isBlock? Icons.Material.Filled.Dangerous : Icons.Material.Filled.VerifiedUser)"
                                            Color="@(isBlock? Color.Error: Color.Success)">
                                            @(isBlock ? "Blocked" : "Active")
                                        </MudChip>
                                    </MudTooltip>
                                }
                            </Authorized>
                            <NotAuthorized>
                                @{
                                    var isBlock = currentVendor.Blocked == 1;
                                    <MudChip T="string"
                                        Icon="@(isBlock? Icons.Material.Filled.Dangerous : Icons.Material.Filled.VerifiedUser)"
                                        Color="@(isBlock? Color.Error: Color.Success)">
                                        @(isBlock ? "Blocked" : "Active")
                                    </MudChip>
                                }
                            </NotAuthorized>
                        </AuthorizeView>
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    <MudTextField Variant="Variant.Text" Class="mb-6" Label="Number" ReadOnly="true" Disabled="true"
                        @bind-Value="currentVendor.Number" />
                    <MudTextField Variant="Variant.Text" Class="mb-6" Label="Name" @bind-Value="currentVendor.Name"
                        For="@(() => currentVendor.Name)" />
                    <MudStack Row="true">
                        <MudTextField Variant="Variant.Text" Class="mb-6" Label="Search Name"
                            For="@(() => currentVendor.SearchName)" @bind-Value="@currentVendor.SearchName" />
                        <MudTextField Variant="Variant.Text" Class="" Label="VAT Registration Number"
                            For="@(() => currentVendor.VatRegistrationNumber)"
                            @bind-Value="@currentVendor.VatRegistrationNumber" />
                        <MudNumericField Variant="Variant.Text" Class="" Label="Payment Days"
                            For="@(() => currentVendor.PaymentDays)" @bind-Value="@currentVendor.PaymentDays" />
                    </MudStack>
                    <MudTextField Variant="Variant.Text" Class="" Label="Address" @bind-Value="@currentVendor.Address"
                        Lines="4" />
                </MudCardContent>
            </MudCard>
        </MudItem>
        <MudItem xs="4">
            <MudCard>
                <MudCardContent>
                    <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                        <MudText Inline Typo="Typo.h5">@L["Contact"]</MudText>
                        <MudButton EndIcon="@Icons.Material.Filled.ArrowOutward" Color="Color.Primary"
                            OnClick="@(e => { navigationManager.NavigateTo($"/vendors/{currentVendor.Number}/contacts"); })">
                            @L["ListOfContacts"]
                        </MudButton>
                    </MudStack>
                </MudCardContent>
                <MudCardContent>
                    <MudTextField Variant="Variant.Text" Class="mb-6" Label="Phone"
                        @bind-Value="@currentVendor.Phone" />
                    <MudTextField Variant="Variant.Text" Class="mb-6" Label="Telex"
                        @bind-Value="@currentVendor.Telex" />
                    <MudTextField Variant="Variant.Text" Class="" Label="Fax" @bind-Value="@currentVendor.Fax" />
                </MudCardContent>
            </MudCard>
        </MudItem>
    </MudGrid>
</EditForm>
@code
{
    [Inject] protected NavigationManager navigationManager { get; set; }
    [Inject] protected IStringLocalizer<Global> L { get; set; }
    [Inject] IViewNotifier viewNotifier { get; set; }
    [Inject] IMasterDataApiClient apiClient { get; set; }
    [Parameter] public EventCallback<bool> OnToggleStatus { get; set; }
    EditForm form { get; set; }
    [Parameter] public GetVendorDto currentVendor { get; set; }
    protected bool isEdit { get; set; } = false;
    protected bool isLoad { get; set; } = false;
    private async Task OnValidSubmit(EditContext context)
    {
        await SaveChanges();
        await InvokeAsync(StateHasChanged);
    }
    protected async Task ToggleVendorStatus()
    {
        isLoad = true;
        try
        {
            var resp = await apiClient.ToggleVendorStatus(currentVendor.Number);
            if (resp.IsSuccessStatusCode)
            {
                // resp = true => blocked | resp = false => active
                currentVendor.Blocked = resp.Result ? 0 : 1;
                viewNotifier.Show(L["Operation Successful"], ViewNotifierType.Success);
                await OnToggleStatus.InvokeAsync(true);
            }
            else
            {
                viewNotifier.Show(resp.Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        finally
        {
            isLoad = false;
        }
    }
    protected void OnNavigateToListVendor()
    {
        navigationManager.NavigateTo($"/vendors");
    }
    protected async Task<bool> SaveChanges()
    {
        var result = true;
        isLoad = true;
        try
        {
            await apiClient.UpdateVendor(currentVendor);
            viewNotifier.Show(L["Operation Successful"], ViewNotifierType.Success);
        }
        catch (Exception ex)
        {
            result = false;
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        isLoad = false;
        return result;
    }
}