﻿using AutoMapper;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Services.PurchaseOrder.Interface;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Dto.PO;
using static Microsoft.AspNetCore.Http.StatusCodes;
namespace PurchaseManager.Server.Services.PurchaseOrder;

public class LineServices : ILineServices
{
    private readonly IPOHeaderManager _poHeaderManager;
    private readonly IMapper _mapper;
    private readonly IPOLineManager _poLineManager;
    private readonly IItemManager _itemManager;
    public LineServices(IPOHeaderManager poHeaderManager, IPOLineManager poLineManager, IItemManager itemManager, IMapper mapper)
    {
        _poHeaderManager = poHeaderManager;
        _poLineManager = poLineManager;
        _itemManager = itemManager;
        _mapper = mapper;
    }
    public async Task<ApiResponse> CreateLineAsync(POLineAddOrUpdate poLineAdd)
    {
        var validationResult = await ValidatePOLineAsync(poLineAdd, true);
        if (!validationResult.IsSuccessStatusCode)
        {
            return validationResult;
        }

        return await _poLineManager.CreateLineAsync(poLineAdd);
    }
    public async Task<ApiResponse> CreateMultipleLinesAsync(List<POLineAddOrUpdate> poLineAdds)
    {
        var errorMessages = new List<string>();

        foreach (var poLine in poLineAdds)
        {
            var validationResult = await ValidatePOLineAsync(poLine, true);
            if (!validationResult.IsSuccessStatusCode)
            {
                errorMessages.Add($"Item {poLine.ItemNumber}: {validationResult.Message}");
            }
        }

        if (errorMessages.Count == 0)
        {
            return await _poLineManager.CreateMultipleLinesAsync(poLineAdds);
        }
        var joinedMessage = string.Join("\n", errorMessages);
        return ApiResponse.S400(joinedMessage);

    }


    public async Task<ApiResponse> UpdateLineAsync(POLineAddOrUpdate poLineAdd)
    {
        var validationResult = await ValidatePOLineAsync(poLineAdd, false);
        if (!validationResult.IsSuccessStatusCode)
        {
            return validationResult;
        }

        return await _poLineManager.UpdateLineAsync(poLineAdd);
    }

    public async Task<ApiResponse> DeleteLineAsync(string documentNumber, string itemNumber, int rowId)
    {
        var statusLine = await _poHeaderManager.IsLineLockedByAnotherUserAsync(documentNumber);
        if (!statusLine.IsSuccessStatusCode)
        {
            return statusLine;
        }

        var deleteLine = await _poLineManager.DeleteLine(documentNumber, itemNumber, rowId);
        return deleteLine;
    }
    public async Task<ApiResponse> SavePdfAsync(string poNumber)
    {
        var poHeader = await _poHeaderManager.GetHeaderAsync(poNumber);
        if (!poHeader.IsSuccessStatusCode)
        {
            return poHeader;
        }

        var bytePdf = await _poHeaderManager.ViewPDF(poNumber);
        var file = new POFileData
        {
            FileName = poNumber, ImageBytes = bytePdf, FileType = "pdf"
        };

        return await _poHeaderManager.UploadFile(file);
    }

    private async Task<ApiResponse> ValidatePOLineAsync(POLineAddOrUpdate poLineAdd, bool isCreate)
    {
        var poHeader = await _poHeaderManager.GetHeaderAsync(poLineAdd.DocumentNumber);
        if (!poHeader.IsSuccessStatusCode)
        {
            return poHeader;
        }

        var statusLine = await _poHeaderManager.IsLineLockedByAnotherUserAsync(poLineAdd.DocumentNumber);
        if (!statusLine.IsSuccessStatusCode)
        {
            return statusLine;
        }

        var item = await _itemManager.GetItem(poLineAdd.ItemNumber);
        if (!item.IsSuccessStatusCode)
        {
            return item;
        }

        var itemDto = _mapper.Map<DetailItemDto>(item.Result);
        if (itemDto.Status < 2)
        {
            return new ApiResponse(Status404NotFound, "ItemNumber is editing");
        }

        if (itemDto.Blocked == 1)
        {
            return new ApiResponse(Status404NotFound, "ItemNumber is blocked");
        }

        if (itemDto.ItemUnitOfMeasures != null)
        {
            var uom = itemDto.ItemUnitOfMeasures
                .FirstOrDefault(x => string.Equals(x.Code, poLineAdd.UnitOfMeasure, StringComparison.OrdinalIgnoreCase));

            if (uom == null)
            {
                return new ApiResponse(Status404NotFound, "UOM is not correct");
            }

            if (uom.Block == 1)
            {
                return new ApiResponse(Status404NotFound, "UOM is blocked");
            }
        }

        var poLine = await _poLineManager.GetLineAsync(poLineAdd.DocumentNumber, poLineAdd.LineNumber);
        switch (isCreate)
        {
            case true when poLine.IsSuccessStatusCode:
                return ApiResponse.S404("LineNumber is conflict");
        }

        var checkDuplicateLine = await _poLineManager.DuplicateValid(poLineAdd);
        if (checkDuplicateLine.IsSuccessStatusCode)
        {
            return ApiResponse.S404("Duplicate line value (Item + UOM + LotNo)");
        }

        var exDateValid = _poLineManager.EXDateValid(poLineAdd.ExpirationDate);
        return exDateValid ? ApiResponse.S200() : ApiResponse.S404("ExpirationDate out of date");

    }

}
