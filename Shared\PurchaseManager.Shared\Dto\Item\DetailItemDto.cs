﻿namespace PurchaseManager.Shared.Dto.Item;

public class DetailItemDto
{
    public string? Number { get; set; }
    public string? Name { get; set; }
    public string? Description { get; set; }
    public string? BaseUnitOfMeasure { get; set; }
    public string? InventoryPostingGroup { get; set; }
    public string? ItemCategoryCode { get; set; }
    public string? VatProductPostingGroup { get; set; }
    public string? ItemDiscountGroup { get; set; }
    public string? ManufacturerCode { get; set; }
    public string? CountryOfOriginCode { get; set; }
    public string? SalaryGroup { get; set; }
    public string? TaxGroupCode { get; set; }
    public string? CommissionGroup { get; set; }
    public int? StatisticsGroup { get; set; }
    public decimal? LotSize { get; set; }
    public int? AllowInvoiceDiscount { get; set; }
    public int? PriceProfitCalculation { get; set; }
    public decimal? ProfitPercent { get; set; }
    public int? CostingMethod { get; set; }
    public decimal? UnitPrice { get; set; }
    public decimal? UnitCost { get; set; }
    public decimal? StandardCost { get; set; }
    public string? LeadTimeCalculation { get; set; }
    public decimal? ReorderPoint { get; set; }
    public decimal? MaximumInventory { get; set; }
    public decimal? ReorderQuantity { get; set; }
    public string? AlternativeItemNo { get; set; }
    public decimal? GrossWeight { get; set; }
    public decimal? NetWeight { get; set; }
    public decimal? UnitsPerParcel { get; set; }
    public decimal? UnitVolume { get; set; }
    public string? VendorNumber { get; set; }
    public string? VendorItemNumber { get; set; }
    public string? FreightType { get; set; }
    public string? TariffNumber { get; set; }
    public decimal? BudgetQuantity { get; set; }
    public decimal? BudgetedAmount { get; set; }
    public decimal? BudgetProfit { get; set; }
    public int? Blocked { get; set; }
    public DateTime? LastDateModified { get; set; }
    public decimal? MinimumOrderQuantity { get; set; }
    public decimal? MaximumOrderQuantity { get; set; }
    public decimal? SafetyStockQuantity { get; set; }
    public decimal? OrderMultiple { get; set; }
    public string? SafetyLeadTime { get; set; }
    public string? SalesUnitOfMeasure { get; set; }
    public string? PurchaseUnitOfMeasure { get; set; }
    public int? ManufacturingPolicy { get; set; }
    public string? ExpirationCalculation { get; set; }
    public int? NonStock { get; set; }
    public string? QuotaNumber { get; set; }
    public decimal? QuotaQuantity { get; set; }
    public string? QuotaAddedNumber { get; set; }
    public decimal? QuotaAddedQuantity { get; set; }
    public string? ImportLicenseNumber { get; set; }
    public decimal? ImportLicenseQuantity { get; set; }
    public int? QuotaRequest { get; set; }
    public string? Name2 { get; set; }
    public string? Name3 { get; set; }
    public DateOnly? VisaIssuedDate { get; set; }
    public DateOnly? ExpirationDate { get; set; }
    public string? Standard { get; set; }
    public string? VendorAuthorizationNumber { get; set; }
    public string? CompanyRegistration { get; set; }
    public string? QualityMeasureCode { get; set; }
    public string? SearchName { get; set; }
    public string? VisaIssuedNumber { get; set; }
    public string? PlaceOfOriginCode { get; set; }
    public string? LoginId { get; set; }
    public string? LastUserModified { get; set; }
    public string? BinCode { get; set; }
    public int? Status { get; set; }
    public int? Type { get; set; }
    public decimal? MinimumInventory { get; set; }
    public string? CategoryNo { get; set; }
    public string? CategoryTypeNo { get; set; }
    public string? CategoryGroupNo { get; set; }
    public string? Importer { get; set; }
    public string? Visa { get; set; }
    public string? Label { get; set; }
    public string? ItemLocation { get; set; }
    public decimal? UnitPriceRegis { get; set; }
    public string? MainIngredient { get; set; }
    public string? RegistrationNo { get; set; }
    public bool? Visible { get; set; }
    public bool? Hidden { get; set; }
    public List<DetailSalesPriceDto>? SalesPrices { get; set; }
    public List<DetailItemUnitOfMeasureDto>? ItemUnitOfMeasures { get; set; }
    public List<ItemColorDto>? ItemColors { get; set; }
}
