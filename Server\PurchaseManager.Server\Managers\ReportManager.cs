using System.Linq.Dynamic.Core;
using System.Reflection;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Report;
using PurchaseManager.Shared.Models.Report.PO;
using PurchaseManager.Storage;
using System.Data.Entity;
using ClosedXML.Excel;
using Microsoft.AspNetCore.Identity;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Extensions;
namespace PurchaseManager.Server.Managers;
public class ReportManager : IReportManager
{
    private readonly ILogger<ReportManager> _logger;
    private readonly IStringLocalizer<Global> L;
    private readonly RoleManager<ApplicationRole> _roleManager;
    private readonly UserManager<ApplicationUser> _userManager;

    private readonly ApplicationDbContext _dbContext;
    public ReportManager(ILogger<ReportManager> logger, IStringLocalizer<Global> l, ApplicationDbContext dbContext, RoleManager<ApplicationRole> roleManager, UserManager<ApplicationUser> userManager)
    {
        _logger = logger;
        L = l;
        _dbContext = dbContext;
        _roleManager = roleManager;
        _userManager = userManager;
    }
    public ApiResponse GetPOReport(PoReportRequest request)
    {
        try
        {
            var filter = request.poReportParams;
            var query = _dbContext.PurchaseOrderHeaders.AsNoTracking().Where(x =>
                            (filter.FromDate == null || filter.ToDate == null || (x.CreatedAtTime >= filter.FromDate && x.CreatedAtTime <= filter.ToDate)) &&
                            (filter.Status == null || x.Status <= filter.Status) &&
                            (filter.VendorNumber == null || x.BuyFromVendorNumber.Equals(filter.VendorNumber)))
                            .AsQueryable();
            var selectedFields = GetPoReportFieldsWithTruePropertyNames(request.poReportFields);
            var data = SelectFields(query, selectedFields);
            if (data.Count() == 0) return new ApiResponse(StatusCodes.Status200OK, L["Operation Successfully"], null);
            byte[] fileData = null;
            using (var workbook = new XLWorkbook())
            {
                var now = DateTime.Now.ToString("dd-MM-yyyy");
                var worksheet = workbook.Worksheets.Add("Purchase Orders " + now);

                var headers = data[0].Keys.ToList();
                for (int i = 0; i < headers.Count; i++)
                {
                    worksheet.Cell(1, i + 1).Value = headers[i];
                }
                for (int row = 0; row < data.Count; row++)
                {
                    var item = data[row];
                    for (int col = 0; col < headers.Count; col++)
                    {
                        var key = headers[col];
                        worksheet.Cell(row + 2, col + 1).Value = (item.TryGetValue(key, out var value) ? value : null).ToString();
                    }
                }
                using (var stream = new MemoryStream())
                {
                    workbook.SaveAs(stream);
                    stream.Position = 0;
                    fileData = stream.ToArray();
                }
            }
            return new ApiResponse(StatusCodes.Status200OK, L["Operation Successfully"], fileData);
        }
        catch (Exception ex)
        {
            _logger.LogError("Export PoData has error(s): ", ex);
            return new ApiResponse(StatusCodes.Status500InternalServerError, L["Operation Fail"], ex.GetBaseException().Message);
        }
    }
    public List<Dictionary<string, object?>> SelectFields(IQueryable<PurchaseOrderHeader> purchaseOrderHeaders, List<string> fieldNames)
    {
        var selectedFields = new List<Dictionary<string, object?>>();
        foreach (var p in purchaseOrderHeaders)
        {
            var result = new Dictionary<string, object?>();
            foreach (var fieldName in fieldNames)
            {
                PropertyInfo? propertyInfo = typeof(PurchaseOrderHeader).GetProperty(fieldName);
                if (propertyInfo != null)
                {
                    var value = propertyInfo.GetValue(p);
                    result.Add(fieldName, value);
                }
            }
            selectedFields.Add(result);
        }
        return selectedFields;
    }
    public Task<ApiResponse> GetVendorReport(PoReportParams reportParams, string module, VendorReportFields fields)
    {
        //Todo: 
        throw new NotImplementedException();
    }
    private static List<string> GetPoReportFieldsWithTruePropertyNames(PoReportFields reportFields)
    {
        Type type = typeof(PoReportFields);
        PropertyInfo[] properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);
        List<string> truePropertyNames = GetListTrueProperty(reportFields, type, properties);
        return truePropertyNames;
    }

    private static List<string> GetListTrueProperty<T>(T reportFields, Type type, PropertyInfo[] properties)
    {
        List<string> truePropertyNames = new List<string>();
        foreach (PropertyInfo property in properties)
        {
            if (property.PropertyType == typeof(bool) || property.PropertyType == typeof(bool?))
            {
                bool? value = (bool?)property.GetValue(reportFields);
                if (value == true)
                {
                    truePropertyNames.Add(property.Name);
                }
            }
        }
        FieldInfo[] fields = type.GetFields(BindingFlags.Public | BindingFlags.Instance);
        foreach (FieldInfo field in fields)
        {
            if (field.FieldType == typeof(bool))
            {
                bool value = (bool)field.GetValue(reportFields);
                if (value == true)
                {
                    truePropertyNames.Add(field.Name);
                }
            }
        }

        return truePropertyNames;
    }

    public async Task<ApiResponse> GetReportVendorAccount()
    {
        try
        {
            var role = await _roleManager.FindByNameAsync("VENDOR");
            if (role == null)
            {
                throw new DomainException("Role VENDOR not found");
            }

            var query = _userManager.Users
                .Include(u => u.UserRoles)
                .Where(u =>
                    u.UserRoles.Any(ur => ur.Role.Name == role.Name) &&
                    u.ContactCode != null
                );
            var foundContact = _dbContext.Contacts
            .Include(c => c.VendorNumberNavigation!).ToList();
            var accounts = (from user in query
                            join contact in _dbContext.Contacts.Include(c => c.VendorNumberNavigation!)
                                on user.ContactCode equals contact.Number into contactGroup
                            from contact in contactGroup.DefaultIfEmpty()
                            select new UserVendorViewModel
                            {
                                UserName = user.UserName,
                                EmployeeCode = user.EmployeeCode,
                                ContactCode = user.ContactCode,
                                TaxCode = contact == null ? string.Empty : contact.VendorNumberNavigation!.VatregistrationNumber,
                                VendorName = contact == null ? string.Empty : contact.VendorNumberNavigation!.Name,
                                IsActive = user.EmailConfirmed,
                                IsBlock = user.Block,
                            })
                .ToList();

            var properties = typeof(UserVendorViewModel).GetProperties();
            var headers = properties.Select(x => x.Name).ToList();

            var excelOpts = new ExcelExportOptions() { SheetName = "Vendor Accounts " + DateTime.UtcNow.ToString("dd-MM-yyyy"), AutoFilter = false };
            string fileData = ExcelExportExtension.ExportToExcel(headers, accounts, excelOpts);// base 64 string
            return new ApiResponse(StatusCodes.Status200OK, L["Operation Successfully"], fileData);

        }
        catch (Exception ex)
        {
            _logger.LogError("Export PoData has error(s): ", ex);
            return new ApiResponse(StatusCodes.Status500InternalServerError, L["Operation Fail"], ex.GetBaseException().Message);
        }
    }

}
