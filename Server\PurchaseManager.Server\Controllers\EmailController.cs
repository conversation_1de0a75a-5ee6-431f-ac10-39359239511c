﻿using Breeze.AspNetCore;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Aop;
using PurchaseManager.Shared.Dto.Email;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Email;

using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Controllers;

// [OpenApiIgnore] // Add
[ApiResponseException]
[Route("api/data/[action]")]
[Authorize]
[ApiController]
public class EmailController : ControllerBase
{
    private readonly IEmailManager _emailManager;
    private readonly IStringLocalizer<Global> L;

    public EmailController(IEmailManager emailManager, IStringLocalizer<Global> l)
    {
        _emailManager = emailManager;
        L = l;
    }

    [HttpPost]
    [ProducesResponseType(Status200OK)]
    [ProducesResponseType(Status400BadRequest)]
    public async Task<ApiResponse> SendMail([FromBody] EmailRequestDto parameters)
    {

        // //test params
        // EmailRequestDto parameters = new EmailRequestDto
        // {
        //     ToAddresses = new List<EmailAddressDto> { new EmailAddressDto("Hoài Nam", "<EMAIL>"), new EmailAddressDto("Hoài Nam Ở Trung Sơn", "<EMAIL>") },
        //     Subject = "Test Email",
        //     Body = "Have a nice dayyyy BB 🥰🥰",
        //     TemplateId = 1,
        //     Queued = true,
        //     IsHtml = false,
        // };
        return ModelState.IsValid ?
            await _emailManager.SendTestEmail(parameters) :
            new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }
    [HttpGet]
    [BreezeQueryFilter]

    public IQueryable<DetailEmailDto> GetEmails([FromQuery] EmailFilter filter)
    {
        return _emailManager.GetEmails(filter);
    }

    // EmailTemplates management
    [HttpGet]
    [BreezeQueryFilter]
    public IQueryable<DetailEmailTemplate> GetEmailTemplates([FromQuery] EmailTemplateFilter filter)
    {
        return _emailManager.GetEmailTemplates(filter);
    }
    [HttpGet]
    public ApiResponse<DetailEmailTemplate> GetEmailTemplateById(int emailTemplateId)
    {
        var data = _emailManager.GetEmailTemplateById(emailTemplateId);
        if (data == null) { return new ApiResponse(Status404NotFound, L["NotFound"]); }
        return new ApiResponse<DetailEmailTemplate>(Status200OK, L["Operation Success"], data);
    }

    [HttpPost]
    public async Task<ApiResponse> CreateEmailTemplate([FromBody] CreateEmailTemplateDto createEmailTemplateDto)
    {
        return ModelState.IsValid ?
            await _emailManager.CreateEmailTemplate(createEmailTemplateDto) :
            new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }

    [HttpPut]
    public async Task<ApiResponse> UpdateEmailTemplate([FromBody] UpdateEmailTemplateDto updateEmailTemplateDto, int emailTemplateId)
    {
        return ModelState.IsValid ?
            await _emailManager.UpdateEmailTemplate(emailTemplateId, updateEmailTemplateDto) :
            new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }
    [HttpPost]
    public async Task<ApiResponse> SendEmailDetailPOToVendor([FromBody] EmailInfoInPODetailDto emailInfoInPODetailDto)
    {
        return ModelState.IsValid ?
            await _emailManager.SendEmailDetailPOToVendor(emailInfoInPODetailDto) :
            new ApiResponse(Status400BadRequest, L["InvalidData"]);
    }
    [HttpGet]
    [BreezeQueryFilter]
    public IQueryable<SentEmailDto> GetAllSentEmail([FromQuery] SentEmailFilter filter)
    {
        var x = _emailManager.GetAllSentEmail(filter);
        var sss = x.ToList();
        return x;
    }
}
