﻿
@page "/admin/reasons"
@inherits ReasonDemandBase

@layout AdminLayout

<div class="py-6 ">

    <MudText Align="Align.Center" Typo="Typo.h5">@L["Demand Reasons"]</MudText>
</div>

@if (listDemandReason == null)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    <MudTable Items="listDemandReason" Striped="true" Bordered="true" Dense="true" Hover="true" Elevation="2" T="PurchaseManager.Shared.Dto.PurchaseSuggestedPayment.DetailDemandReasonDto">
        <ToolBarContent>
            <MudButton StartIcon="@Icons.Material.Filled.Add" OnClick="@(e => { createDialogOpen = true; })" Variant="Variant.Filled" Color="Color.Success">@L["New Reason"]</MudButton>
        </ToolBarContent>
        <HeaderContent>
            <MudTh>@L["Reason Name"]</MudTh>
            <MudTh>@L["Create By"]</MudTh>
            <MudTh></MudTh>
            <MudTh></MudTh>
        </HeaderContent>
        <RowTemplate Context="Reason">
            <MudTd>@Reason.ReasonName</MudTd>
            <MudTd><div style="width:130px;">@Reason.CreateBy</div></MudTd>
            <MudTd>
                <MudIconButton OnClick="@(async _ => await OnDeleteReason(Reason.ReasonCode))" Icon="@Icons.Material.Filled.Delete" Color="Color.Error" />
                <MudIconButton OnClick="@(_ => { editDialogOpen = true;  currentReason = Reason; })" Icon="@Icons.Material.Filled.Edit" Color="Color.Primary" />
            </MudTd>
        </RowTemplate>
        <PagerContent>
            <MudTablePager RowsPerPageString=@L["Rows per page"] />
        </PagerContent>
    </MudTable>
}
<MudDialog @bind-Visible="@createDialogOpen">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Add" Class="mr-3 mb-n1" />
            @L["New Reason"]
        </MudText>
    </TitleContent>
    <DialogContent>
        <EditForm id="createReasonForm" Model="@newReasonModel" OnValidSubmit="@CreateReasonAsync">
            <FluentValidationValidator />
            <MudValidationSummary />
            <MudTextField @bind-Value="@newReasonModel.ReasonName" Label=@L["Reason Name"]
                AdornmentIcon="@Icons.Material.Filled.Person" Adornment="Adornment.End" FullWidth="true" Required="true"
                RequiredError=@L["Required"]></MudTextField>
        </EditForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(e => { createDialogOpen = false; })">@L["Cancel"]</MudButton>
        <MudButton ButtonType="ButtonType.Submit" form="createReasonForm" Variant="Variant.Filled"
            Color="Color.Primary">@L["Create"]</MudButton>
    </DialogActions>
</MudDialog>

<MudDialog @bind-Visible="@editDialogOpen">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-3 mb-n1" />
            @L["Edit {0}", currentReason.ReasonName]
        </MudText>
    </TitleContent>
    <DialogContent>
        <EditForm id="updateReasonForm" Model="@currentReason" OnValidSubmit="@UpdateReasonAsync">
            <FluentValidationValidator />
            <MudValidationSummary />
            <MudTextField @bind-Value="@currentReason.ReasonName" Label=@L["Reason Name"]
                AdornmentIcon="@Icons.Material.Filled.Person" Adornment="Adornment.End" FullWidth="true" Required="true"
                RequiredError=@L["Required"]></MudTextField>
        </EditForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(_ => editDialogOpen = false)">@L["Cancel"]</MudButton>
        <MudButton ButtonType="ButtonType.Submit" form="updateReasonForm" Variant="Variant.Filled"
            Color="Color.Primary">@L["Update"]</MudButton>
    </DialogActions>
</MudDialog>

