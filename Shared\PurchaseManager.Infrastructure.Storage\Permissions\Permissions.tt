﻿<#@ template debug="false" hostspecific="false" language="C#" #>
<#@ assembly name="System.Core" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Text" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ output extension=".cs" #>
<#
var permissionActions = new string[] { "Create", "Update", "Read", "Delete" };
var entities = new string[] { "User", "Role", "Tenant" };
#>
//Autogenerated from Permissions.tt
using System.ComponentModel.DataAnnotations;

namespace PurchaseManager.Infrastructure.Storage.Permissions
{
    public static class Permissions
    {
        #region Admin
<#
foreach(var entity in entities)
{#>
        public static class <#=entity#>
        {
<#
foreach(var permissionAction in permissionActions)
{#>
            [Display(Name = "<#=permissionAction#><#=entity#>Permission")]
            public const string <#=permissionAction#> = "<#=entity#>.<#=permissionAction#>";
<#}#>
        }
<#}#>
        #endregion
    }
}