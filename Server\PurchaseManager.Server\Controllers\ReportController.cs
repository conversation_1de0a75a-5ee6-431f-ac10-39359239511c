using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Aop;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Report;
using PurchaseManager.Shared.Models.Report.PO;
namespace PurchaseManager.Server.Controllers;
[Route("api/[controller]")]
[ApiController]
[ApiResponseException]
public class ReportController : ControllerBase
{
    private readonly IReportManager _reportManager;
    private readonly IStringLocalizer<Global> L;
    public ReportController(IReportManager reportManager, IStringLocalizer<Global> l)
    {
        _reportManager = reportManager;
        L = l;
    }
    [HttpPost("get-all-po-report-by-params")]
    public ApiResponse GetPoReport(PoReportRequest request)
    {
        return _reportManager.GetPOReport(request);
    }
    [HttpPost("get-report-vendor-account")]
    public async Task<ApiResponse> GetReportVendorAccount()
        => await _reportManager.GetReportVendorAccount();
}
