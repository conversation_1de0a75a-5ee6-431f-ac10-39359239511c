﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.Permissions;
using PurchaseManager.Server.Services.PurchaseOrder.Interface;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.PO;
using static Microsoft.AspNetCore.Http.StatusCodes;
namespace PurchaseManager.Server.Controllers;

//[OpenApiIgnore]
[SecurityHeaders]
[Route("api/[controller]")]
[ApiController]
public partial class PurchaseOrderController : ControllerBase
{
    private readonly IHeaderServices _headerServices;
    private readonly ILineServices _lineServices;
    private readonly IPOLineManager _lineManager;
    private readonly IPOHeaderManager _poHeaderManager;
    private readonly ApiResponse _invalidData;
    private readonly IStringLocalizer<Global> _i18N;
    private readonly IQueueGenPurchaseOrderManager _queueGenPurchaseOrderManager;

    public PurchaseOrderController(IStringLocalizer<Global> l, IQueueGenPurchaseOrderManager queueGenPurchaseOrderManager,
        IHeaderServices headerServices, IPOHeaderManager poHeaderManager,
        IPOLineManager lineManager, ILineServices lineServices)
    {
        _i18N = l;
        _invalidData = new ApiResponse(Status400BadRequest, _i18N["InvalidData"]);
        _queueGenPurchaseOrderManager = queueGenPurchaseOrderManager;
        _headerServices = headerServices;
        _poHeaderManager = poHeaderManager;
        _lineManager = lineManager;
        _lineServices = lineServices;
    }

    [HttpPost("header-vendor")]
    [Authorize(Permissions.PO.Create)]
    public async Task<ApiResponse> CreateHeader(string vendorNumber)
    {
        if (!ModelState.IsValid)
        {
            return _invalidData;
        }
        var responseApi = await _headerServices.CreateHeader(vendorNumber, string.Empty);
        return responseApi;
    }

    [HttpPost("header-contact")]
    [Authorize(Permissions.PO.Create)]
    public async Task<ApiResponse> CreateHeaderByContactNumberAsync(string contactNumber)
    {
        return ModelState.IsValid ? await _headerServices.CreateHeader(string.Empty, contactNumber) : _invalidData;
    }

    /// <summary>
    ///     Determines if there are recent orders from the given list of vendor numbers.
    /// </summary>
    /// <param name="listVendorNumber">A list of vendor numbers representing the Vendor to check.</param>
    /// <returns>An <see cref="ApiResponse" /> containing the result of the operation.</returns>
    [AllowAnonymous]
    [HttpPost("vendors/verify-recent-order")]
    public async Task<ApiResponse> IsRecentOrderFromVendor([FromBody] List<string> listVendorNumber)
    {
        return await _poHeaderManager.IsRecentOrderFromVendorAsync(listVendorNumber);
    }

    /// <summary>
    ///     Determines if there are recent orders from the given list of item numbers.
    /// </summary>
    /// <param name="listItemNumber">A list of item numbers to check for recent orders.</param>
    /// <returns>An <see cref="ApiResponse" /> containing the result of the operation.</returns>
    [AllowAnonymous]
    [HttpPost("items/verify-recent-order")]
    public async Task<ApiResponse> IsRecentOrderFromItem([FromBody] List<string> listItemNumber)
    {
        return await _poHeaderManager.IsRecentOrderFromItemAsync(listItemNumber);
    }

    /// <summary>
    ///     Determines if there are recent orders from the given list of suggested document numbers.
    /// </summary>
    /// <param name="listSuggestDocumentNumber">A list of suggested document numbers to verify for recent orders.</param>
    /// <returns>An <see cref="ApiResponse" /> containing the result of the verification.</returns>
    [AllowAnonymous]
    [HttpPost("suggests/verify-recent-order")]
    public async Task<ApiResponse> IsRecentOrderFromSuggestDocument([FromBody] List<string> listSuggestDocumentNumber)
    {
        return await _poHeaderManager.IsRecentOrderFromSuggestDocumentAsync(listSuggestDocumentNumber);
    }

    /// <summary>
    ///     Retrieves the header details of a purchase order based on the provided number.
    /// </summary>
    /// <param name="number">The identifier of the purchase order to retrieve the header details for.</param>
    /// <returns>An <see cref="ApiResponse" /> containing the result of the operation.</returns>
    [HttpGet("header/{number}")]
    [Authorize(Permissions.PO.Read)]
    [ProducesResponseType(Status204NoContent)]
    public async Task<ApiResponse> GetHeader(string number)
    {
        return ModelState.IsValid ? await _poHeaderManager.GetHeaderAsync(number) : _invalidData;
    }

    /// <summary>
    ///     Get PO Header by filter
    /// </summary>
    /// <param name="filter"></param>
    /// <returns></returns>
    [HttpGet("headers/filter")]
    public async Task<ApiResponse> GetPOHeadersByFilter([FromQuery] PurchaseOrderFilter filter)
    {
        return await _poHeaderManager.GetPOHeadersByFilterAsync(filter);
    }

    /// <summary>
    ///     Retrieves the last unit cost for a specified item and unit from a specific vendor.
    /// </summary>
    /// <param name="itemCode">The code of the item to search for.</param>
    /// <param name="unit">The unit of measure associated with the item.</param>
    /// <param name="vendorCode">The code of the vendor providing the item.</param>
    /// <returns>An <see cref="ApiResponse{Decimal}" /> containing the last unit cost for the specified item, unit, and vendor.</returns>
    [HttpGet("last-unit-Cost/{itemCode}/{unit}/{vendorCode}")]
    // [Authorize(Permissions.PO.Read)]
    public async Task<ApiResponse<decimal>> GetLastUnitCostByItemCodeByVendorCode(string itemCode, string unit, string vendorCode)
    {
        return await _poHeaderManager.GetLastPurchasePriceAsync(itemCode, unit, vendorCode);
    }

    [HttpGet("latest-price/{itemCode}/{unit}/{vendorCode}")]
    public async Task<ApiResponse<GetLatestPriceDto>> GetLatestPrice(string itemCode, string unit, string vendorCode)
    {
        return await _poHeaderManager.GetLatestPriceBySku(itemCode, unit, vendorCode);
    }
    /// <summary>
    ///     Get User Created PO By PONumber
    /// </summary>
    /// <param name="poNumber"></param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpGet("user-created/{poNumber}")]
    public async Task<ApiResponse> GetUserCreatedPOByPONumberAsync(string poNumber)
    {
        return await _poHeaderManager.GetUserCreatedPOByPONumberAsync(poNumber);
    }

    [HttpPut("header")]
    [Authorize(Permissions.PO.Update)]
    public async Task<ApiResponse> UpdateHeader([FromBody] UpdatePOHeaderDto headerDto)
    {
        return ModelState.IsValid ? await _headerServices.UpdateHeader(headerDto) : _invalidData;
    }

    [AllowAnonymous]
    [HttpPut("open-document/{number}")]
    [Authorize(Permissions.PO.Update)]
    public async Task<ApiResponse> OpenDocument(string number)
    {
        return ModelState.IsValid ? await _headerServices.OpenDocument(number, false) : _invalidData;
    }

    [AllowAnonymous]
    [HttpPut("stock-order/open-document/{number}")]
    [Authorize(Permissions.PO.Update)]
    public async Task<ApiResponse> StockOrderOpenDocument(string number)
    {
        return ModelState.IsValid ? await _headerServices.OpenDocument(number, true) : _invalidData;
    }

    [AllowAnonymous]
    [HttpPut("confirm-header/{number}")]
    [Authorize(Permissions.PO.Update)]
    public async Task<ApiResponse> ConfirmHeader(string number)
    {
        return ModelState.IsValid ? await _headerServices.ChangeDocumentStatus(number, PurchaseOrderEnum.Confirm) : _invalidData;
    }

    [HttpPut("CancelConfirmHeader")]
    [Authorize(Permissions.PO.Update)]
    public async Task<ApiResponse> CancelConfirmHeader(string number)
    {
        return ModelState.IsValid ? await _headerServices.ChangeDocumentStatus(number, PurchaseOrderEnum.CancelConfirm) : _invalidData;
    }

    [HttpPut("approve-header/{number}")]
    [Authorize(Permissions.PO.Update)]
    public async Task<ApiResponse> ApproveHeader(string number)
    {
        return ModelState.IsValid ? await _headerServices.ChangeDocumentStatus(number, PurchaseOrderEnum.Approve) : _invalidData;
    }

    [HttpPut("un-approve-header/{number}")]
    [Authorize(Permissions.PO.Update)]
    public async Task<ApiResponse> UnApproveHeader(string number)
    {
        return ModelState.IsValid ? await _headerServices.ChangeDocumentStatus(number, PurchaseOrderEnum.Confirm) : _invalidData;
    }

    [HttpPut("complete-header/{number}/{condition}")]
    [Authorize(Permissions.PO.Update)]
    public async Task<ApiResponse> CompletedHeader(string number, bool condition)
    {
        var status = PurchaseOrderEnum.Completed;
        if (!condition)
        {
            status = PurchaseOrderEnum.PartiallyReceived;
        }
        return ModelState.IsValid ? await _headerServices.ChangeDocumentStatus(number, status) : _invalidData;
    }

    [HttpPut("change-document-status/{number}/{status}")]
    [Authorize(Permissions.PO.Update)]
    public async Task<ApiResponse> ChangeDocumentStatusEndpoint(string number, int status)
    {
        return ModelState.IsValid ? await _headerServices.ChangeDocumentStatus(number, (PurchaseOrderEnum)status) : _invalidData;
    }

    [AllowAnonymous]
    [HttpPut("close-document/{number}")]
    [Authorize(Permissions.PO.Update)]
    public async Task<ApiResponse> CloseDocument(string number)
    {
        return ModelState.IsValid ? await _poHeaderManager.CloseDocumentAsync(number) : _invalidData;
    }

    /// <summary>
    ///     Delete multiple PO by Purchasing Code
    /// </summary>
    /// <param name="purchasingCode"></param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPost("header/delete")]
    public async Task<ApiResponse> DeleteMultiple(List<string> purchasingCode)
    {
        return await _poHeaderManager.DeleteMultipleHeaderAsync(purchasingCode);
    }
}
