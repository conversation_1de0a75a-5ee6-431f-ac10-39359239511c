﻿using AutoMapper;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
namespace PurchaseManager.Storage.Mapping;

public class PurchaseSuggestedPaymentMappingProfile : Profile
{
    public PurchaseSuggestedPaymentMappingProfile()
    {
        CreateMap<DemandReason, CreateDemandReasonDto>().ReverseMap();
        CreateMap<DemandReason, UpdateDemandReasonDto>().ReverseMap();
        CreateMap<DemandReason, DetailDemandReasonDto>().ReverseMap();
        CreateMap<PurchaseSuggestedPaymentHeader, DetailPurchaseSuggestedPaymentHeaderDto>();
        CreateMap<PurchaseSuggestedPaymentHeader, ApproveDocumentDto>();
        CreateMap<PurchaseSuggestedPaymentLine, DetailPurchaseSuggestedPaymentLineDto>()
            .ForMember(dest => dest.ItemName,
            opt
                => opt.MapFrom(src => src.ItemNumberNavigation.Name))
            .ForMember(dest => dest.ReasonName,
            opt
                => opt.MapFrom(src => src.ReasonCodeNavigation.ReasonName))
            ;
        CreateMap<UpdatePurchaseSuggestedPaymentHeaderDto, DetailPurchaseSuggestedPaymentHeaderDto>();
        // CreateMap<PurchaseSuggestedPaymentLine, UpdatePurchaseSuggestedPaymentLineDto>().ReverseMap();

        CreateMap<PurchaseSuggestedPaymentLine, PurchaseSuggestedReport>()
            .ForMember(destinationMember: x => x.ItemNumber, memberOptions: opt =>
                opt.MapFrom(src => src.Number))
            .ForMember(destinationMember: x => x.ItemName, memberOptions: opt =>
                opt.MapFrom(src => src.ItemNumberNavigation.Name))
            .ForMember(destinationMember: x => x.ReasonName, memberOptions: opt =>
                opt.MapFrom(src => src.ReasonCodeNavigation.ReasonName))
            ;
    }
}
