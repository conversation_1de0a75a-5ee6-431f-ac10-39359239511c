using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Models.Item;
using PurchaseManager.Shared.Providers;
using PurchaseManager.Theme.Material.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Pages.Items.ListItem;

public partial class ListVendorItemPageBase : ItemsTableBase<DetailVendorItemDto>
{
    [Inject]
    protected NavigationManager navigation { get; set; }

    [Inject]
    protected AuthenticationStateProvider authStateProvider { get; set; }
    protected AccountVendorFilter accountVendorFilter { get; set; } = new AccountVendorFilter();
    protected VendorItemFilter vendorItemFilter { get; set; } = new VendorItemFilter();


    protected UserVendorViewModel currentVendor { get; set; } = new UserVendorViewModel();

    protected MudTextField<string> SearchNumberRef { get; set; }
    protected MudTextField<string> SearchNameRef { get; set; }
    protected bool isLoading { get; set; }

    protected UserViewModel userViewModel { get; set; } = new UserViewModel();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
    }

    protected async Task LoadVendorInfo()
    {
        try
        {
            var user = (await authenticationStateTask).User;
            userViewModel = null;
            if (user.Identity.IsAuthenticated)
            {
                userViewModel = await ((IdentityAuthenticationStateProvider)authStateProvider).GetUserViewModel();

                vendorItemFilter.VendorNumber = userViewModel.VendorCode;
                queryParameters = vendorItemFilter;
                // // await table.ReloadServerData();
                // await Reload();
            }
            accountVendorFilter.UserName = userViewModel.UserName;
            var resp = await apiClient.GetsAccountVendor(accountVendorFilter);
            currentVendor = resp.Result.Data.FirstOrDefault();
            from = "GetAllVendorItems";
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["UserCreationFailed"]);
        }

    }
    protected override async Task LoadItems()
    {
        await LoadVendorInfo();
        await base.LoadItems();
    }
    protected async Task OnSearchItemNumber(string number)
    {

        apiClient.ClearEntitiesCache();
        vendorItemFilter.ItemNumber = number;
        if (!string.IsNullOrEmpty(number)) vendorItemFilter.ItemName = null;
        if (!string.IsNullOrEmpty(SearchNameRef.Value))
        {
            vendorItemFilter.ItemName = SearchNameRef.Value;
        }
        await OnSearch(number);
    }
    protected async Task OnSearchItemName(string name)
    {
        apiClient.ClearEntitiesCache();
        if (!string.IsNullOrEmpty(name)) vendorItemFilter.ItemNumber = null;

        if (!string.IsNullOrEmpty(SearchNumberRef.Value))
        {
            vendorItemFilter.ItemNumber = SearchNumberRef.Value;
        }
        vendorItemFilter.ItemName = name;
        await OnSearch(name);
    }
}
