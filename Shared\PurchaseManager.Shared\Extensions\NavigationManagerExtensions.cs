﻿using System.Net;
using System.Runtime.InteropServices;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.WebUtilities;
namespace PurchaseManager.Shared.Extensions;

//https://chrissainty.com/working-with-query-strings-in-blazor/
public static class NavigationManagerExtensions
{
    public static void GoTo(this NavigationManager navManager, string uri)
    {
        navManager.NavigateTo(uri, new NavigationOptions
        {
            ReplaceHistoryEntry = true
        });
    }

    public static bool TryGetQueryString<T>(this NavigationManager navManager, string key, out T value)
    {
        var uri = navManager.ToAbsoluteUri(navManager.Uri);

        if (QueryHelpers.ParseQuery(uri.Query).TryGetValue(key, out var valueFromQueryString))
        {
            if (typeof(T) == typeof(int) && int.TryParse(valueFromQueryString, out var valueAsInt))
            {
                value = (T)(object)valueAsInt;
                return true;
            }

            if (typeof(T) == typeof(string))
            {
                value = (T)(object)valueFromQueryString.ToString();
                return true;
            }

            if (typeof(T) == typeof(decimal) && decimal.TryParse(valueFromQueryString, out var valueAsDecimal))
            {
                value = (T)(object)valueAsDecimal;
                return true;
            }
        }

        value = default;
        return false;
    }

    public static bool IsWebAssembly()
    {
        return RuntimeInformation.IsOSPlatform(OSPlatform.Create("BROWSER"));
    }

    public static Dictionary<string, string> SetParam(this Dictionary<string, string> parameters, string name, string? value)
    {
        var existingValue = parameters.ContainsKey(name);
        if (string.IsNullOrEmpty(value))
        {
            if (existingValue)
            {
                parameters.Remove(name);// remove the existing value
            }
            return parameters;// ignore null value
        }

        if (parameters.Count == 0 || !existingValue)
        {
            parameters.Add(name, value);// add new value
        }
        else
        {
            parameters[name] = value;// update existing value
        }
        return parameters;
    }
    public static string ToQueryString(Dictionary<string, string> parameters)
    {
        if (parameters == null || parameters.Count == 0)
        {
            return string.Empty;
        }

        var queryParameters = parameters
            .Where(p => p.Value != null)
            .Select(p => $"{WebUtility.UrlEncode(p.Key)}={WebUtility.UrlEncode(p.Value)}")
            .ToList();

        if (queryParameters.Count == 0)
        {
            return string.Empty;
        }

        return "?" + string.Join("&", queryParameters);
    }
}
