﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.PO;
using PurchaseManager.Shared.Models.PurchasePrice;

namespace PurchaseManager.Theme.Material.Shared.Components;

public partial class AlertPurchasePriceUploadFile : ComponentBase
{
    [Parameter] public List<DuplicatePurchasePriceItemDto> Parameters { get; set; }
    [Inject] public IStringLocalizer<Global> L { get; set; }
}
