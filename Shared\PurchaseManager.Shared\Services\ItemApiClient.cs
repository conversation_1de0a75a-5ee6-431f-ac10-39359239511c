﻿using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Interfaces;
namespace PurchaseManager.Shared.Services;

public class ItemApiClient : BaseApiClient, IItemApiClient
{
    public ItemApiClient(HttpClient httpClient, ILogger<BaseApiClient> logger, string rootApiPath = "api/data/") : base(httpClient,
    logger, rootApiPath) {}
    public async Task<ApiResponseDto<List<DetailItemUnitOfMeasureDto>>> GetItemUnitOfMeasure(string itemNumber)
    {
        var apiResponse = await httpClient.GetFromJsonAsync<ApiResponseDto<List<DetailItemUnitOfMeasureDto>>>(
        $"{rootApiPath}ItemUnitOfMeasure/{itemNumber}");
        return apiResponse;
    }
}
