﻿using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.QueueGenPO;
using PurchaseManager.Shared.Extensions;
namespace PurchaseManager.Shared.Services;

public partial class PurchaseOrderApiClient
{
    /// <summary>
    ///     Create Queue Gen PO Demand
    /// </summary>
    /// <param name="detailPoDtos"></param>
    /// <returns></returns>
    public async Task<ApiResponseDto> CreateDemand(List<CreateQueueGenPoDemandDto> detailPoDtos)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"{BaseUrl}/Demand", detailPoDtos);
    }
    /// <summary>
    /// Update quantity
    /// </summary>
    /// <param name="updatePoDto"></param>
    /// <returns></returns>
    public async Task<ApiResponseDto> UpdatePurchaseOrder(UpdatePoDto updatePoDto)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>($"{BaseUrl}/UpdatePurchaseOrder", updatePoDto);
    }
}
