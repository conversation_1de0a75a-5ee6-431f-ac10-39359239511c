﻿@inherits UploadFilesBasePage
<MudStack Style="height: 300px; position: relative;" Class="pa-5">
    <MudFileUpload T="IReadOnlyList<IBrowserFile>"
                   @ref="@_fileUploadRef"
                   AppendMultipleFiles
                   OnFilesChanged="OnInputFileChanged"
                   Disabled="@Disabled"
                   Hidden="@false"
                   Accept=".jpg, .png, .pdf"
                   InputClass="absolute mud-width-full mud-height-full overflow-hidden z-20"
                   InputStyle="opacity:0"
                   @ondragenter="@SetDragClass"
                   @ondragleave="@ClearDragClass"
                   @ondragend="@ClearDragClass">
        <ActivatorContent>
            <MudPaper Height="200px"
                      Outlined="true"
                      Class="@_dragClass">
                <MudText Typo="Typo.h6">
                    @Caption
                </MudText>
                @foreach (var file in fileNames)
                {
                    <MudChip T="string" Color="Color.Dark" Text="@file" />
                }
            </MudPaper>
        </ActivatorContent>
    </MudFileUpload>
    @if (isFileUploading)
    {
        <MudAlert Severity="Severity.Info">File(s) is uploading... Please wait!!</MudAlert>
    }
    else
    {
        <MudToolBar Gutters="true"
                    Class="relative d-flex justify-end gap-4 z-30">
            <MudButton OnClick="@Submit"
                       Color="Color.Primary"
                       Disabled="@(!fileNames.Any())"
                       Variant="Variant.Filled">
                @L["Upload"]
            </MudButton>
            <MudButton OnClick="@ClearReceiveFiles"
                       Color="Color.Error"
                       Disabled="@(!fileNames.Any())"
                       Variant="Variant.Filled">
                @L["Clear"]
            </MudButton>
        </MudToolBar>
    }
</MudStack>