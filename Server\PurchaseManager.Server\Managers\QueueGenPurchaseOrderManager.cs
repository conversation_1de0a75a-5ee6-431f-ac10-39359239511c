using System.Data.Common;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Newtonsoft.Json;
using PurchaseManager.Constants;
using PurchaseManager.Constants.PurchaseSuggestedPayment;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Services.PurchaseOrder.Interface;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Dto.QueueGenPO;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Storage;
using static Microsoft.AspNetCore.Http.StatusCodes;
namespace PurchaseManager.Server.Managers;

public class QueueGenPurchaseOrderManager : IQueueGenPurchaseOrderManager
{
    private readonly ApplicationPersistenceManager _persistenceManager;

    private readonly IStringLocalizer<Global> _l;
    private readonly IHeaderServices _headerServices;
    private readonly IAdminManager _adminManager;
    private readonly IPOLineManager _poLineManager;
    private readonly IPOHeaderManager _poHeaderManager;
    private readonly ApplicationDbContext _context;
    private readonly ILogger<QueueGenPurchaseOrderManager> _logger;
    private readonly IMapper _mapper;
    public QueueGenPurchaseOrderManager(ApplicationPersistenceManager persistenceManager, ILogger<QueueGenPurchaseOrderManager> logger, IStringLocalizer<Global> l,
        ApplicationDbContext context, IPOLineManager purchaseOrderManager, IMapper mapper, IAdminManager adminManager,
        IHeaderServices headerServices, IPOHeaderManager poHeaderManager)
    {
        _persistenceManager = persistenceManager;
        _logger = logger;
        _l = l;
        _context = context;
        _poLineManager = purchaseOrderManager;
        _mapper = mapper;
        _adminManager = adminManager;
        _headerServices = headerServices;
        _poHeaderManager = poHeaderManager;
    }
    public async Task<ApiResponse> CreateQueueGenPurchaseOrder(List<DetailPoDto> detailPos)
    {
        try
        {
            // Check if any item in detailPos has quantity = 0 => return
            if (detailPos.Any(x => x.Quantity == 0))
                return new ApiResponse(Status406NotAcceptable, _l["Quantity invalid"]);

            IQueryable<QueueGenPurchaseOrder> queryableItems = _persistenceManager.GetEntities<QueueGenPurchaseOrder>().AsNoTracking();
            bool anyVendorNumberAndItemNumberExisted = detailPos.Any(detailPO => queryableItems
                .Any(po => po.VendorNumber == detailPO.VendorNumber && po.ItemNumber == detailPO.ItemNumber));

            // Check if any QueueGenPurchaseOrder with vendor name and item number already exists
            if (anyVendorNumberAndItemNumberExisted)
                return new ApiResponse(Status400BadRequest, _l["PO has existed"]);

            // Get item by number
            List<QueueGenPurchaseOrder> lsToAdd = new List<QueueGenPurchaseOrder>();
            foreach (var dp in detailPos)
            {
                var items = _persistenceManager.GetEntities<Item>()
                    .AsNoTracking()
                    .Include(h => h.ItemUnitOfMeasures)
                    .Where(h => h.Number.Equals(dp.ItemNumber));

                var itemMatched = await items.FirstOrDefaultAsync();
                if (itemMatched == null)
                    continue;

                // Đơn vị mua của sản phẩm: PurchaseUnitOfMeasure
                var purchaseUnitOfMeasure = itemMatched.ItemUnitOfMeasures.FirstOrDefault(i => i.Code == itemMatched.PurchaseUnitOfMeasure);
                if (purchaseUnitOfMeasure == null)
                {
                    _logger.LogWarning($"Base unit of measure not found for item {dp.ItemNumber}");
                    continue;
                }


                var quantity = dp.Quantity / purchaseUnitOfMeasure.QuantityPerUnitOfMeasure;
                QueueGenPurchaseOrder newItemInQueue = new QueueGenPurchaseOrder
                {
                    CreateDate = DateTime.Now,
                    ItemName = dp.ItemName,
                    IsGenerated = true,
                    ItemNumber = dp.ItemNumber,
                    VendorNumber = dp.VendorNumber,
                    // Số lượng mua đã được quy đổi về đơn vị mua mặt định.
                    Quantity = (int)Math.Ceiling(quantity),
                    // Hệ số quy đổi ở bảng Item Unit of Measure.
                    QuantityPerUnitOfMeasure = (int)purchaseUnitOfMeasure.QuantityPerUnitOfMeasure!,
                    // Đơn vị tính mua hàng theo item.
                    PurchaseUnitOfMeasure = itemMatched.PurchaseUnitOfMeasure
                };
                lsToAdd.Add(newItemInQueue);

                // Update totalOrder in detailPO
                var totalOrderUpdate = new UpdatePoDto
                {
                    VendorNumber = dp.VendorNumber, ItemNumber = dp.ItemNumber, Quantity = dp.Quantity, CreateDate = dp.CreateDate
                };

                await _poLineManager.UpdatePurchaseOrderAfterCreatePO(totalOrderUpdate);
                await Task.Delay(2000);
            }

            await _context.QueueGenPurchaseOrders.AddRangeAsync(lsToAdd);
            await _context.SaveChangesAsync();

            var rs = await _headerServices.POAutoCreate();
            var isCreatedSuccess = rs.IsSuccessStatusCode;
            return isCreatedSuccess
                ? new ApiResponse(Status201Created, _l["Operation Successful"])
                : new ApiResponse(Status500InternalServerError, _l["Operation Fail"]);
        }
        catch (DbException)
        {
            return new ApiResponse(Status500InternalServerError, "ServerError");
        }
        catch (Exception ex)
        {
            _logger.LogError($"Create Queue Purchase Order Not Success: {ex.GetBaseException().Message}");
            return new ApiResponse(Status400BadRequest, ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> CreateQueueGenPurchaseOrderDataInput(List<CreateQueueGenPoDemandDto> createQueueGenPoDemand)
    {
        try
        {
            // Validate input
            var validationResponse = await ValidateQueueGenPoDemand(createQueueGenPoDemand);
            if (validationResponse.StatusCode != Status200OK)
            {
                return validationResponse;// Return validation error response
            }
            // Process validated data and add to queue
            var itemsToAdd = new List<QueueGenPurchaseOrder>();
            foreach (var demandDto in createQueueGenPoDemand)
            {
                // Check and create brand if it doesn't exist
                var contactNumber = await ContactCode(demandDto.BrandCode, demandDto.VendorNumber);
                var item = await _context.Items
                    .Include(i => i.ItemUnitOfMeasures)
                    .FirstOrDefaultAsync(i => i.Number == demandDto.ItemNumber);

                if (item == null)
                {
                    _logger.LogWarning("Item {DemandDtoItemNumber} not found in database", demandDto.ItemNumber);
                    continue;
                }

                var unitOfMeasure =
                    item.ItemUnitOfMeasures.FirstOrDefault(x
                        => x.Code.Equals(demandDto.PurchaseUnitOfMeasure, StringComparison.CurrentCultureIgnoreCase));

                if (unitOfMeasure == null)
                {
                    _logger.LogWarning("Base unit of measure not found for item {ItemNumber}", item.Number);
                    continue;
                }
                // Calculate quantity based on the base unit of measure
                // var quantity = demandDto.Quantity / baseUnitOfMeasure.QuantityPerUnitOfMeasure;
                var docType = demandDto.DocumentType;
                var newItemInQueue = new QueueGenPurchaseOrder
                {
                    CreateDate = DateTime.Now,
                    BrandCode = contactNumber,
                    ItemName = item.Name,
                    IsGenerated = false,
                    ItemNumber = item.Number,
                    Description = demandDto.Description,
                    VAT = demandDto.VAT,
                    DocumentType = docType,
                    PriceB4VAT = demandDto.PriceB4VAT,
                    PurchaseUnitOfMeasure = demandDto.PurchaseUnitOfMeasure.ToUpper(),
                    Quantity = demandDto.Quantity,
                    QuantityPerUnitOfMeasure = (int)unitOfMeasure.QuantityPerUnitOfMeasure,
                    VendorNumber = demandDto.VendorNumber == string.Empty ? item.VendorNumber : demandDto.VendorNumber,
                    BaseUnit = item.BaseUnitOfMeasure
                };
                if (demandDto.PriceB4VAT == 0)
                {
                    var latestPrice = await _poHeaderManager
                        .GetLatestPriceBySku(newItemInQueue.ItemNumber, newItemInQueue.PurchaseUnitOfMeasure,
                        newItemInQueue.VendorNumber);
                    if (latestPrice.IsSuccessStatusCode)
                    {
                        newItemInQueue.PriceB4VAT = latestPrice.Result.UnitCost;
                    }
                    else
                    {
                        _logger.LogWarning("Failed to get latest price for item {ItemNumber} with vendor {VendorNumber}",
                        newItemInQueue.ItemNumber, newItemInQueue.VendorNumber);
                        newItemInQueue.PriceB4VAT = 0;// Set to 0 if price retrieval fails
                        continue;// Skip adding this item if price retrieval fails
                    }
                }

                itemsToAdd.Add(newItemInQueue);
            }

            // Add items to the queue in a database
            await _context.QueueGenPurchaseOrders.AddRangeAsync(itemsToAdd);
            await _context.SaveChangesAsync();

            // Trigger automatic PO creation
            var poCreateResponse = await _headerServices.POAutoCreate();
            // save demand to a new table
            var demandInput = _mapper.Map<List<DemandInput>>(itemsToAdd);
            demandInput.ForEach(d => d.RowId = 0);
            await _context.DemandInputs.AddRangeAsync(demandInput);
            await _context.SaveChangesAsync();
            // Check if PO creation was successful
            return poCreateResponse.IsSuccessStatusCode
                ? new ApiResponse(Status201Created, _l["Operation Successful"])
                : new ApiResponse(Status500InternalServerError, _l["Operation Fail"]);
        }
        catch (Exception ex)
        {
            _logger.LogError("Create Queue Purchase Order Not Success: {Message}", ex.GetBaseException().Message);
            return new ApiResponse(Status400BadRequest, ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> CreateQueueGenPurchaseOrderDemandV2(List<ApproveDocumentDto> documentApproved)
    {
        try
        {
            // Validate input
            var itemsToAdd = new List<QueueGenPurchaseOrder>();

            foreach (var documentApprove in documentApproved)
            {
                var header = await _context.PurchaseSuggestedPaymentHeaders
                    .FirstOrDefaultAsync(x => x.Number == documentApprove.Number);
                if (header == null)
                    continue;
                if (header.Status != (int)PurchaseSuggestedPaymentEnum.Approve)
                    return new ApiResponse(404, $"Document is: {documentApprove.Number} not approve");

                var listLine = await _context.PurchaseSuggestedPaymentLines
                    .Where(x => x.DocumentNumber == documentApprove.Number)
                    .ToListAsync();
                if (listLine.Count == 0)
                    continue;
                foreach (var line in listLine)
                {
                    var itemData = await _context.Items
                        .Include(i => i.ItemUnitOfMeasures)
                        .FirstOrDefaultAsync(i => i.Number == line.Number);
                    var purchaseUnitOfMeasure = itemData.ItemUnitOfMeasures.FirstOrDefault(i => i.Code == itemData.PurchaseUnitOfMeasure);
                    if (purchaseUnitOfMeasure == null)
                    {
                        _logger.LogWarning($"Base unit of measure not found for item {line.Number}");
                        continue;
                    }

                    // số lượng đặt hàng đã được quy đổi sang base unit
                    var quantity = line.ConfirmQuantity / purchaseUnitOfMeasure.QuantityPerUnitOfMeasure;// chia theo hệ số PUnitofMeasure//

                    var newItemInQueue = new QueueGenPurchaseOrder
                    {
                        ItemNumber = itemData.Number,
                        // Số lượng mua đã được quy đổi về đơn vị mua mặt định.
                        Quantity = (int)Math.Ceiling(quantity),
                        // Hệ số quy đổi ở bảng Item Unit of Measure.
                        QuantityPerUnitOfMeasure = (int)purchaseUnitOfMeasure.QuantityPerUnitOfMeasure!,
                        // Đơn vị tính mua hàng theo item.
                        PurchaseUnitOfMeasure = itemData.PurchaseUnitOfMeasure,
                        IsGenerated = false,
                        VendorNumber = line.BuyFromVendorNumber == string.Empty ? itemData.VendorNumber : line.BuyFromVendorNumber,
                        CreateDate = DateTime.Now,
                        ItemName = itemData.Name,
                    };
                    itemsToAdd.Add(newItemInQueue);
                }
            }
            // Add items to the queue in a database
            await _context.QueueGenPurchaseOrders.AddRangeAsync(itemsToAdd);
            await _context.SaveChangesAsync();

            // Trigger automatic PO creation
            var poCreateResponse = await _headerServices.POAutoCreate();
            // save demand to a new table
            var demandInput = _mapper.Map<List<DemandInput>>(itemsToAdd);
            demandInput.ForEach(d => d.RowId = 0);
            await _context.DemandInputs.AddRangeAsync(demandInput);
            await _context.SaveChangesAsync();
            // Check if PO creation was successful
            return poCreateResponse.IsSuccessStatusCode
                ? new ApiResponse(Status201Created, _l["Operation Successful"])
                : new ApiResponse(Status500InternalServerError, _l["Operation Fail"]);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Create Queue Purchase Order Not Success: {ex.GetBaseException().Message}");
            return new ApiResponse(Status400BadRequest, ex.GetBaseException().Message);
        }
    }

    //TODO: check quantityUnitOfMeasure nhé 
    private async Task<ApiResponse> ValidateQueueGenPoDemand(List<CreateQueueGenPoDemandDto> createQueueGenPoDemand)
    {
        try
        {
            // Check if any quantity is zero
            if (createQueueGenPoDemand.Any(x => x.Quantity == 0))
            {
                return new ApiResponse(Status406NotAcceptable, _l["Quantity invalid"]);
            }
            var queryableItems = _persistenceManager.GetEntities<QueueGenPurchaseOrder>().AsNoTracking().ToList();
            var itemVendorMap = await _context.Items
                .Where(item => createQueueGenPoDemand.Select(d => d.ItemNumber).Contains(item.Number))
                .ToDictionaryAsync(keySelector: item => item.Number, elementSelector: item => item.VendorNumber);

            createQueueGenPoDemand.ForEach(demand =>
            {
                if (string.IsNullOrEmpty(demand.VendorNumber) && itemVendorMap.TryGetValue(demand.ItemNumber, out var value))
                {
                    demand.VendorNumber = value;
                }
            });

            var duplicateItems = createQueueGenPoDemand
                .Where(demand => queryableItems
                    .Any(po => po.VendorNumber == demand.VendorNumber && po.ItemNumber == demand.ItemNumber))
                .ToList();

            if (duplicateItems.Count != 0)
            {
                var errorMessages = duplicateItems
                    .Select(item => $"ItemNumber: {item.ItemNumber}, VendorNumber: {item.VendorNumber} already exists in queue")
                    .ToList();
                var errorMessageInput = JsonConvert.SerializeObject(errorMessages);
                var resultCheck = JsonConvert.DeserializeObject<object>(errorMessageInput);
                return new ApiResponse(Status400BadRequest, "The following item-vendor pairs already exist", resultCheck);
            }

            var vendorListDemand = createQueueGenPoDemand
                .Where(x => !string.IsNullOrEmpty(x.VendorNumber))
                .Select(x => x.VendorNumber).ToList();

            var existingVendors = await _context.Vendors
                .Where(vendor => vendorListDemand.Contains(vendor.Number))
                .Select(vendor => vendor.Number)
                .ToListAsync();

            var nonExistingVendors = vendorListDemand.Except(existingVendors).ToList();

            if (nonExistingVendors.Count != 0)
            {
                return new ApiResponse(Status400BadRequest, _l["Vendor(s) {0} does not exist", string.Join(",", nonExistingVendors)]);
            }
            var purchaseUnitOfMeasureCodes = createQueueGenPoDemand.Select(x => x.PurchaseUnitOfMeasure).ToList();
            var itemNumbers = createQueueGenPoDemand.Select(x => x.ItemNumber).ToList();
            var itemList = await _context.Items
                .Where(item => itemNumbers.Any(num => num == item.Number))
                .ToListAsync();

            // var vendorNumbers = itemList.Select(x => x.VendorNumber).ToList();
            var vendorNumbers = existingVendors;

            var vendorList = await _context.Vendors
                .Where(vendor => vendorNumbers.Any(num => num == vendor.Number))
                .ToListAsync();
            // Check for blocked items
            var blockedItems = itemList.Where(x => x.Blocked == 1).ToList();
            if (blockedItems.Count > 0)
            {
                var blockedItemNumbers = blockedItems.Select(item => item.Number).ToList();
                return new ApiResponse(Status400BadRequest, _l["Item(s) {0} is blocked", string.Join(",", blockedItemNumbers)]);
            }

            // Check for items that are not editable
            var nonEditableItems = itemList.Where(x => x.Status != 2).ToList();
            if (nonEditableItems.Count > 0)
            {
                var nonEditableItemNumbers = nonEditableItems.Select(item => item.Number).ToList();
                return new ApiResponse(Status400BadRequest, _l["Item(s) {0} is in waiting status", string.Join(",", nonEditableItemNumbers)]);
            }

            // Check for blocked vendors and non-editable vendors
            var blockedVendorNumbers = vendorList.Where(x => x.Blocked == VendorBlockedType.Blocked)
                .Select(vendor => vendor.Number)
                .ToList();
            var blockVendorDemand = _context.Vendors
                .Where(item =>
                    vendorListDemand.Any(num => num == item.Number)
                    && item.Blocked == VendorBlockedType.Blocked)
                .Select(x => x.Number)
                .ToList();
            if (blockedVendorNumbers.Count != 0 || blockVendorDemand.Count != 0)
            {
                var blockVendorList = blockedVendorNumbers.Count > 0 ? blockedVendorNumbers : blockVendorDemand;
                return new ApiResponse(Status400BadRequest, _l["Vendor(s) {0} is blocked", string.Join(",", blockVendorList)]);
            }

            var nonEditableVendorNumbers = vendorList.Where(x => x.Status != VendorStatusType.Saved)
                .Select(item => item.Number)
                .ToList();
            if (nonEditableVendorNumbers.Count != 0)
            {
                return new ApiResponse(Status400BadRequest, _l["Vendor(s) {0} is in waiting status", string.Join(",", nonEditableVendorNumbers)]);
            }

            // Check exists IUOM
            var existingUnitOfMeasures = await _context.ItemUnitOfMeasures
                .Where(iuom => itemNumbers.Contains(iuom.ItemNumber))
                .Select(iuom => new
                {
                    iuom.ItemNumber, iuom.Code
                })
                .ToListAsync();

            // check qtyPerUnitOfMeasure
            foreach (var item in createQueueGenPoDemand)
            {
                var itemUnitOfMeasure = await _context.ItemUnitOfMeasures
                    .FirstOrDefaultAsync(x => x.ItemNumber == item.ItemNumber &&
                                              x.Code == item.PurchaseUnitOfMeasure);
                if (itemUnitOfMeasure == null)
                {
                    return new ApiResponse(Status400BadRequest,
                    _l["Purchase Unit Of Measure {0} not found for ItemNumber {1}", item.PurchaseUnitOfMeasure, item.ItemNumber]);
                }
            }
            
            var groupedUnitOfMeasures = existingUnitOfMeasures
                .GroupBy(iuom => iuom.ItemNumber)
                .Select(g => new
                {
                    ItemNumber = g.Key, Codes = g.Select(iuom => iuom.Code.ToUpper()).ToList()
                })
                .ToList();

            var errors =
                (from Code in purchaseUnitOfMeasureCodes
                 let found = groupedUnitOfMeasures.Any(g => g.Codes.Contains(Code.ToUpper()))
                 where !found
                 let affectedItem = createQueueGenPoDemand.FirstOrDefault(g => g.PurchaseUnitOfMeasure.Contains(Code, StringComparison.CurrentCultureIgnoreCase))
                 where affectedItem != null
                 select (affectedItem.ItemNumber, Code)).ToList();

            if (errors.Count == 0)
            {
                return new ApiResponse(Status200OK, "Validation successful");
            }

            var errorDetails = errors.Select(e
                => $"Purchase Unit Of Measure: {e.Code.ToUpper()} does not exist in ItemNumber: {e.ItemNumber}").ToList();

            var errorMessage = JsonConvert.SerializeObject(errorDetails);
            var result = JsonConvert.DeserializeObject<object>(errorMessage);
            return new ApiResponse(Status400BadRequest, "The following item-vendor pairs already exist", result);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Validation failed: {ex.GetBaseException().Message}");
            return new ApiResponse(Status500InternalServerError, _l["Validation failed"]);
        }
    }

    private async Task<string> ContactCode(string code, string vendorNumber)
    {
        if (string.IsNullOrEmpty(code))
        {
            return vendorNumber;
        }
        var contact = await _context.Contacts.FirstOrDefaultAsync(x => x.Number.ToLower() == code.ToLower());
        // if contact is null, create new contact
        if (contact != null)
        {
            return code;
        }
        var contactModel = new Contact
        {
            Number = code,
            Address = string.Empty,
            Block = false,
            CreatedAt = DateTime.Now,
            CreatedBy = _adminManager.GetUserLogin(),
            Name = code,
            VendorNumber = vendorNumber,
            Phone = string.Empty,
            Tags = string.Empty,
            Email = code + "@TrungSonPharma.com",
            Tax = string.Empty,
            HasAccount = false,
            // Default value personal
            Type = 1
        };

        await _context.Contacts.AddAsync(contactModel);
        await _context.SaveChangesAsync();
        return contactModel.Number;
    }
}
