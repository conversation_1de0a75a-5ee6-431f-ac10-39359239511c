﻿using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Db;
using Breeze.Sharp;

namespace PurchaseManager.Shared.Interfaces
{
    public interface ILocalizationApiClient : IBaseApiClient
    {
        void AddEntity(LocalizationRecord entity);
        void RemoveEntity(LocalizationRecord entity);
        Task<QueryResult<PluralFormRule>> GetPluralFormRules();
        Task<QueryResult<LocalizationRecord>> GetLocalizationRecords(LocalizationRecordKey key = null);
        Task<QueryResult<LocalizationRecordKey>> GetLocalizationRecordKeys(int? take, int? skip, string filter = null);
        Task<ApiResponseDto> DeleteLocalizationRecordKey(LocalizationRecordKey key);
        Task<ApiResponseDto> EditLocalizationRecordKey(LocalizationRecordKey oldkey, LocalizationRecordKey newKey);
        Task<ApiResponseDto> ReloadTranslations();
        Task<ApiResponseDto> Upload(MultipartFormDataContent content);
    }
}
