using AutoMapper;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.PO;
using PurchaseManager.Theme.Material.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder;
public class ReplenishmentBasePage : ItemsTableBase<DetailPoDto>
{
    [Inject] protected HttpClient HttpClient { get; set; }
    [Inject] protected IMapper Mapper { get; set; }
    [Inject]
    protected IPurchaseOrderApiClient PurchaseOrderApiClient { get; set; }
    protected HashSet<DetailPoDto> SelectedItems { get; set; } = [];
    protected PurchaseOrderFilter PurchaseOrderFilter { get; set; } = new PurchaseOrderFilter();
    protected DetailPoDto SelectedDetailPO { get; set; } = new DetailPoDto();
    protected IEnumerable<DetailPoDto> ListDetailPO { get; set; } = new List<DetailPoDto>();
    protected MudDatePicker DateFilterRef { get; set; }
    protected List<CheckPaymentDayResponse> ListCheckPaymentDayResponse { get; set; } = [];
    protected bool POCreating { get; set; }
    protected bool IsAlertDialogOpen { get; set; }
    protected bool IsDisableEditTable { get; set; }
    protected MudTextField<string> SearchByVendorRef { get; set; }
    protected MudTextField<string> SearchByItemRef { get; set; }
    protected override async Task OnInitializedAsync()
    {
        from = "GetPurchases";
        queryParameters = PurchaseOrderFilter;
        ListDetailPO = items;
        _ = (await authenticationStateTask).User;
        await base.OnInitializedAsync();
    }
    protected async Task OnSearchByVendor(string text)
    {
        PurchaseOrderFilter.Vendor = text;
        apiClient.ClearEntitiesCache();
        await Reload();
    }
    protected async Task OnSearchByDate(DateTime? date)
    {
        date ??= DateTime.Now;
        PurchaseOrderFilter.Month = date;
        apiClient.ClearEntitiesCache();
        await Reload();
        IsDisableEditTable = date.Value.Month != DateTime.Now.Month;
    }
    protected async Task OnSearchByItemNameOrItemNumber(string text)
    {
        PurchaseOrderFilter.Query = text;
        //purchaseOrderFilter.Vendor = text;
        apiClient.ClearEntitiesCache();
        await Reload();
    }
    protected TableGroupDefinition<DetailPoDto> GroupDefinition { get; set; } = new TableGroupDefinition<DetailPoDto>
    {
        GroupName = "",
        Indentation = false,
        Expandable = true,
        IsInitiallyExpanded = false,
        Selector = (e) => e.VendorName +" : "+ e.VendorNumber
    };
    protected async Task DetailPOHasBeenCommitted(DetailPoDto element)
    {
        //save detail po
        try
        {
            var updateModel = Mapper.Map<UpdatePoDto>(element);
            updateModel.ItemNumber = element.ItemNumber;
            updateModel.VendorNumber = element.VendorNumber;
            var resp = await PurchaseOrderApiClient.UpdatePurchaseOrder(updateModel);
            if (resp.IsSuccessStatusCode)
            {
                viewNotifier.Show($"{element.ItemName} updated", ViewNotifierType.Success);
                await Reload();
                SelectedItems.Clear();
                StateHasChanged();
            }
            else
            {
                viewNotifier.Show(resp.Message, ViewNotifierType.Error);
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        } 
    }

    public async Task CheckPaymentDayByVendorNumber()
    {
        try
        {
            var req = SelectedItems.Select(x => x.VendorNumber).ToList();
            var resp = await apiClient.CheckPaymentDayByVendorNumber(req);
            if (resp.Result.Count > 0)
            {
                ListCheckPaymentDayResponse = resp.Result;
                IsAlertDialogOpen = true;
            }
            else await CreatePO();
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    protected async Task CreatePO()
    {
        try
        {
            IsAlertDialogOpen = false;
            POCreating = true;
            var resp = await apiClient.CreatePO(SelectedItems.ToList());
            if (resp.IsSuccessStatusCode)
            {
                viewNotifier.Show("PO Created", ViewNotifierType.Success);
                await Task.Delay(1000);
                navigationManager.NavigateTo("/PO");
            }
            else viewNotifier.Show(resp.Message, ViewNotifierType.Error);
            POCreating = false;
        }
        catch (Exception ex)
        {
            POCreating = false;
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
}
