﻿using AutoMapper;

using Breeze.Sharp.Core;

using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Localization;

using MudBlazor;
using MudBlazor.Extensions.Components;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
namespace PurchaseManager.Theme.Material.Demo.Pages.Items.EditItem;
public partial class EditItemPage : ComponentBase
{
    [Inject] protected IStringLocalizer<Global> L { get; set; }
    [Inject] protected IDialogService Dialog { get; set; }
    [Inject] protected NavigationManager navigation { get; set; }
    [Inject] protected IApiClient apiClient { get; set; }
    [Inject] protected IMapper _mapper { get; set; }
    [Inject] IViewNotifier notifier { get; set; }
    [Parameter] public string DetailItemDtoId { get; set; }
    protected bool isEditing { get; set; }
    protected bool isShowAddUOMForm { get; set; }
    protected bool isShowAddSalePriceForm { get; set; }
    protected bool isLoading { get; set; } = true;
    protected bool isCreate { get; set; }
    protected bool isItemSaving { get; set; }
    protected bool isUOMSaving { get; set; }
    protected MudExList<ColorDto> componentC;

    protected bool isSalePriceSaving { get; set; }
    protected bool isNotFound { get; set; }
    protected string selectedUnitOfMeasure { get; set; }
    protected decimal QuantityPerUnitOfMeasureToAdd { get; set; } = 10;
    protected DateTime? itemExpirationDate = DateTime.Today;
    protected DateTime? date = DateTime.Today;
    protected DateTime? itemVisaIssuedDate = DateTime.Today;
    public DateRange dateRangeSalePrice { get; set; } = new DateRange();
    protected ColorDto[] selectedItem { get; set; }
    protected DialogOptions salePriceDialogOptions { get; set; }
    protected CreateItemUnitOfMeasureDto newUOMDto { get; set; }
    protected int selectedRowNumber = -1;
    [SupplyParameterFromForm]
    protected DetailItemDto item { get; set; }
    protected DetailItemDto initialItem { get; set; }
    protected DetailSalesPriceDto salePriceBeforeEdit { get; set; }
    protected DetailSalesPriceDto salePriceSelected { get; set; }
    protected DetailSalesPriceDto salePriceToAdd { get; set; }
    public UnitOfMeasureDto unitOfMeasureToAddSalePrice { get; set; } = new UnitOfMeasureDto();
    protected DetailItemUnitOfMeasureDto detailItemUnitOfMeasureDtoSelected { get; set; }
    protected DetailItemUnitOfMeasureDto iuomBeforeEdit { get; set; }
    protected EditContext editContext;
    protected TableEditTrigger editTrigger = TableEditTrigger.RowClick;
    protected List<UnitOfMeasureDto> listDetailItemDtoUnitOfMeasure { get; set; } = new List<UnitOfMeasureDto>();
    protected List<UnitOfMeasureDto> listUnitOfMeasureWithoutDefault { get; set; } = new List<UnitOfMeasureDto>();
    protected List<UnitOfMeasureDto> listUnitOfMeasureWithDefault { get; set; } = new List<UnitOfMeasureDto>();
    protected List<UnitOfMeasureDto> listUOMInSalePrice { get; set; } = new List<UnitOfMeasureDto>();
    protected List<UnitOfMeasureDto> listUnitOfMeasureSelected { get; set; } = new List<UnitOfMeasureDto>();
    protected IEnumerable<DetailSalesPriceDto> listSalesPrices { get; set; }
    protected IEnumerable<DetailItemUnitOfMeasureDto> listDetailItemUnitOfMeasureDto { get; set; } = new List<DetailItemUnitOfMeasureDto>();
    protected List<string> clickedEvents = new();
    protected UnitOfMeasureDto unitOfMeasureSelected { get; set; }
    protected Func<UnitOfMeasureDto, string> converter = p => p?.Code;
    protected Func<ColorDto, string> converterColorDto = p =>
    {
        return p?.ColorName;
    };
    protected ColorDto colorDto { get; set; } = new ColorDto();
    protected IEnumerable<ColorDto> selectedColor { get; set; } = new List<ColorDto>();

    protected ColorDto colorDtoToSelect { get; set; } = new();

    protected ColorDto[] listColorDto;

    protected override async Task OnInitializedAsync()
    {
        item ??= new();
        initialItem ??= new();
        editContext = new(item);
        await LoadDetailItemDtoById();
        await GetColors();

        if (isNotFound) return;
        await LoadUnitOfMeasure();
        CheckViewAction();
        InitSalePriceDialogOps();
        await base.OnInitializedAsync();
        isLoading = false;
    }
    protected async Task LoadDetailItemDtoById()
    {
        if (string.IsNullOrWhiteSpace(DetailItemDtoId))
        {

            isNotFound = true;
            await base.OnInitializedAsync();
            return;
        }

        var resp = await apiClient.GetItemByNumber(DetailItemDtoId);
        if (resp.IsSuccessStatusCode)
        {
            item = resp.Result;
            initialItem = item;
            if (item.ExpirationDate != null || item.VisaIssuedDate != null)
            {
                itemExpirationDate = DateTime.Parse(item.ExpirationDate.ToString());
                itemVisaIssuedDate = DateTime.Parse(item.VisaIssuedDate.ToString());
            }
            var lsSalesPrice = item.SalesPrices!.ToList();
            listSalesPrices = lsSalesPrice;
            var listUOMDto = item.ItemUnitOfMeasures!.ToList();
            listDetailItemUnitOfMeasureDto = listUOMDto;
            isNotFound = false;
        }
        else
        {
            isNotFound = true;
            await base.OnInitializedAsync();
            isLoading = false;
            return;
        }

    }
    #region item actions 
    protected async Task OnSaveItemClick()
    {
        try
        {
            // check item colors 
            // block all colors
            var isChangeColor = item.ItemColors != selectedColor;
            if (isChangeColor)
            {
                item.ItemColors.ForEach(x =>
                {
                    x.Block = true;
                });
                var length = item.ItemColors.Count() > selectedColor.Count() ? item.ItemColors.Count() : selectedColor.Count();
                var isRemove = item.ItemColors.Count() > selectedColor.Count();
                var isAdd = item.ItemColors.Count() < selectedColor.Count();
                var isChange = item.ItemColors.Count() == selectedColor.Count();
                if (isRemove) // removed some colors 
                {
                    var listColorToAdd = new List<ItemColorDto>();
                    selectedColor.ToList().ForEach(x =>
                    {
                        var existedColor = item.ItemColors.Find(y => y.ColorCode == x.ColorCode);
                        //update status for this color
                        if (existedColor is not null)
                        {
                            var updateIndex = item.ItemColors.IndexOf(existedColor);
                            item.ItemColors[updateIndex].Block = false;
                        }
                        else
                        {
                            listColorToAdd.Add(new ItemColorDto
                            {
                                ColorCode = x.ColorCode,

                                Block = false,
                                ColorCodeNavigation = new ColorDto
                                {
                                    ColorCode = x.ColorCode,
                                    ColorName = x.ColorName
                                },
                                ItemNumber = item.Number
                            });
                        }
                    });
                    if (listColorToAdd.Any())
                    {
                        item.ItemColors.AddRange(listColorToAdd);
                    }
                }
                else if (isAdd || isChange) // added some colors
                {
                    selectedColor.ForEach(x =>
                    {
                        var addedColor = item.ItemColors.Find(y => y.ColorCode == x.ColorCode);
                        if (addedColor is null)
                        {
                            item.ItemColors.Add(new ItemColorDto
                            {
                                ColorCode = x.ColorCode,

                                Block = false,
                                ColorCodeNavigation = new ColorDto
                                {
                                    ColorCode = x.ColorCode,
                                    ColorName = x.ColorName
                                },
                                ItemNumber = item.Number
                            });
                        }
                    });
                    selectedColor.ForEach(x =>
                    {
                        var addedColor = item.ItemColors.Find(y => y.ColorCode == x.ColorCode);
                        if (addedColor is not null)
                        {
                            //update status for this color
                            var updateIndex = item.ItemColors.IndexOf(addedColor);
                            item.ItemColors[updateIndex].Block = false;
                        }

                    });
                }
                var req = _mapper.Map<List<InsertOrUpdateItemColorsDto>>(item.ItemColors);
                var updateColorResp = await apiClient.InsertOrUpdateColors(req);
                if (updateColorResp.IsSuccessStatusCode)
                {
                    notifier.Show(updateColorResp.Message, ViewNotifierType.Success, L["SaveSuccessfully"]);
                    isEditing = false;
                }
                else notifier.Show(updateColorResp.Message, ViewNotifierType.Error, L["SaveFailure"]);
            }

            isItemSaving = true;
            item.Status = 2;
            var updateItemDto = _mapper.Map<UpdateItemDto>(item);
            updateItemDto.VisaIssuedDate = DateOnly.FromDateTime((DateTime)itemVisaIssuedDate!);
            updateItemDto.ExpirationDate = DateOnly.FromDateTime((DateTime)itemExpirationDate!);
            var resp = await apiClient.UpdateItem(updateItemDto, item.Number);
            isItemSaving = false;
            if (resp.IsSuccessStatusCode)
            {
                notifier.Show(resp.Message, ViewNotifierType.Success, L["SaveSuccessfully"]);
                isEditing = false;
            }
            else notifier.Show(resp.Message, ViewNotifierType.Error, L["SaveFailure"]);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            notifier.Show(ex.Message, ViewNotifierType.Error, L["SaveFailure"]);
        }
    }
    protected async Task OnEditClick()
    {
        if (item.Status != 1)
        {
            item.Status = 1;
            var updateItemDto = _mapper.Map<UpdateItemDto>(item);
            var resp = await apiClient.UpdateItem(updateItemDto, item.Number);
            if (!resp.IsSuccessStatusCode) notifier.Show(resp.Message, ViewNotifierType.Error, L["UnavailableEditItem"]);
        }
        isEditing = true;

    }
    protected async Task DiscardChange()
    {
        //TODO update item => set status != 1: editing cannot order
        if (item.Status == 1)
        {
            item.Status = 2;
            var updateItemDto = _mapper.Map<UpdateItemDto>(item);
            var resp = await apiClient.UpdateItem(updateItemDto, item.Number);
            if (!resp.IsSuccessStatusCode) notifier.Show(resp.Message, ViewNotifierType.Error, L["Operation Failure"]);
        }
        await ResetForm();
    }
    #endregion
    #region  uom actions 
    protected async Task OnSaveNewUOM()
    {
        //if (unitOfMeasureSelected is not null)
        //{
        //    listUnitOfMeasureSelected.AddRange(unitOfMeasureSelected.ToList().Except(listUnitOfMeasureSelected));
        //}
        var listUOMToCreate = _mapper.Map<List<CreateItemUnitOfMeasureDto>>(new List<UnitOfMeasureDto> { unitOfMeasureSelected });
        if (!listUOMToCreate.Any()) return;
        listUOMToCreate.ForEach(x =>
        {
            x.ItemNumber = item.Number;
            x.QuantityPerUnitOfMeasure = QuantityPerUnitOfMeasureToAdd;
        });
        isUOMSaving = true;
        var resp = await apiClient.CreateIUOM(listUOMToCreate);
        isUOMSaving = false;
        if (resp.IsSuccessStatusCode)
        {
            notifier.Show(resp.Message, ViewNotifierType.Success, L["SaveSuccessfully"]);
            isShowAddUOMForm = false;
            isLoading = true;
            await LoadDetailItemDtoById();
            RemoveDefaultUOM();
            unitOfMeasureSelected = new();
            isLoading = false;
        }
        else notifier.Show(resp.Message, ViewNotifierType.Error, L["SaveFailure"]);
    }
    protected async Task LoadUnitOfMeasure()
    {
        var resp = await apiClient.GetAllUnitOfMeasure(null, null);
        listUnitOfMeasureWithoutDefault = resp.Results.ToList();//view
        listUnitOfMeasureWithDefault = resp.Results.ToList(); // all of UOM
        unitOfMeasureSelected = new();
        RemoveDefaultUOM();
    }
    protected void RemoveDefaultUOM()
    {
        List<UnitOfMeasureDto> result = listUnitOfMeasureWithoutDefault
            .Where(uom => !item.ItemUnitOfMeasures.Any(dto => dto.Code == uom.Code))
            .ToList();
        listUnitOfMeasureWithoutDefault = result;
    }
    #endregion
    #region table Item Unit of measure config
    //IUOM : item unit of measure 
    protected async void IUOMHasBeenCommitted(object element)
    {
        DetailItemUnitOfMeasureDto unit = (DetailItemUnitOfMeasureDto)element;
        try
        {
            var reqParam = _mapper.Map<UpdateItemUnitOfMeasureDto>(unit);

            var resp = await apiClient.UpdateIUOM(item.Number, unit.Code, reqParam);
            if (resp.IsSuccessStatusCode)
            {
                notifier.Show(resp.Message, ViewNotifierType.Success, L["SaveSuccessfully"]);
                isLoading = true;
                await LoadDetailItemDtoById();
                RemoveDefaultUOM();
                unitOfMeasureSelected = new();
                isLoading = false;
            }
            else notifier.Show(resp.Message, ViewNotifierType.Error, L["SaveFailure"]);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }
    }
    protected void BackupIUOM(object element)
    {
        iuomBeforeEdit = (DetailItemUnitOfMeasureDto)element;
    }
    protected void ResetIUOMToOriginalValues(object element)
    {
        element = iuomBeforeEdit;
    }
    #endregion
    protected void CheckViewAction()
    {
        var uri = new Uri(navigation.Uri);
        var path = uri.LocalPath;
        isCreate = path.Contains("create");
    }

    protected void DeleteSalesPrice(string value)
    {
        var DetailItemDtoToRemove = listSalesPrices.Single(r => r.ItemNumber == value);
        listSalesPrices.ToList().Remove(DetailItemDtoToRemove);
    }

    protected void ShowSalePriceForm()
    {
        isShowAddSalePriceForm = true;
        List<UnitOfMeasureDto> result = listUnitOfMeasureWithDefault
        .Where(uom => item.ItemUnitOfMeasures.Any(dto => dto.Code == uom.Code))
        .ToList();
        listUnitOfMeasureSelected = result;
        salePriceToAdd = new DetailSalesPriceDto
        {
            StartingDate = DateTime.Now,
            EndingDate = DateTime.Now.AddDays(30),
            UnitPrice = 10000,
            Quantity = 1,
            QuantityPerUnitOfMeasure = 10,
            ItemNumber = item.Number
        };

    }

    protected void ShowUOMFrom()
    {
        isShowAddUOMForm = true;
        unitOfMeasureSelected = new();

    }
    protected async Task ResetForm()
    {
        //TODO  Confirm before reset form 
        await LoadDetailItemDtoById();
        editContext.MarkAsUnmodified();
        isEditing = false;
    }
    #region table Sale price config
    protected async void SalePriceHasBeenCommitted(object element)
    {
        DetailSalesPriceDto sp = (DetailSalesPriceDto)element;
        try
        {
            var reqParam = _mapper.Map<UpdateSalesPriceDto>(sp);

            var resp = await apiClient.UpdateSalePrice(item.Number, (DateTime)sp.StartingDate, sp.UnitOfMeasureCode, reqParam);
            if (resp.IsSuccessStatusCode)
            {
                notifier.Show(resp.Message, ViewNotifierType.Success, L["SaveSuccessfully"]);
                isLoading = true;
                await LoadDetailItemDtoById();
                RemoveDefaultUOM();
                unitOfMeasureSelected = new();
                isLoading = false;
            }
            else notifier.Show(resp.Message, ViewNotifierType.Error, L["SaveFailure"]);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }
    }
    protected void BackupSalePrice(object element)
    {
        salePriceBeforeEdit = (DetailSalesPriceDto)element;
    }

    protected async Task OnSaveNewSalePrice()
    {
        List<DetailSalesPriceDto> lsSalePriceToAdd = new List<DetailSalesPriceDto> { salePriceToAdd };
        List<CreateSalesPriceDto> items = _mapper.Map<List<CreateSalesPriceDto>>(lsSalePriceToAdd);
        isSalePriceSaving = true;
        isShowAddSalePriceForm = false;
        var resp = await apiClient.CreateSalePrice(items);
        isSalePriceSaving = false;
        if (resp.IsSuccessStatusCode)
        {
            notifier.Show(resp.Message, ViewNotifierType.Success, L["SaveSuccessfully"]);
            isLoading = true;
            await LoadDetailItemDtoById();
            isLoading = false;
        }
        else notifier.Show(resp.Message, ViewNotifierType.Error, L["SaveFailure"]);
    }
    protected void ResetSalePriceToOriginalValues(object element)
    {
        element = salePriceBeforeEdit;
    }

    #endregion
    protected void InitSalePriceDialogOps()
    {
        salePriceDialogOptions = new DialogOptions()
        {
            MaxWidth = MaxWidth.Medium,
            FullWidth = true
        };
    }
    protected async Task GetColors()
    {
        try
        {
            var reps = await apiClient.GetColors();
            var listColorSelected = item.ItemColors.Where(x => x.Block == false).ToList();
            listColorDto = reps.ToArray();
            var selectedItem = new List<ColorDto>();
            listColorDto.ForEach(x =>
            {
                if (listColorSelected.Any(y => y.ColorCodeNavigation.ColorCode == x.ColorCode))
                {
                    selectedItem.Add(x);
                }
            });
            selectedColor = selectedItem; // show in UI
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
        }


    }
    protected void OnSelectColorChanged(ColorDto color)
    {
        if (color == null) return;

        var foundItemColor = item.ItemColors.Find(x => x.ColorCodeNavigation.ColorCode == color.ColorCode);
        if (foundItemColor is null)// not exist in list
        {

            item.ItemColors.Add(new ItemColorDto
            {
                ColorCode = color.ColorCode,

                Block = false,
                ColorCodeNavigation = new ColorDto
                {
                    ColorCode = color.ColorCode,
                    ColorName = color.ColorName
                },
                ItemNumber = item.Number
            });
        }
        else if (foundItemColor is not null && foundItemColor.Block)//  exist in list and it is not block
        {
            var updateIndex = item.ItemColors.IndexOf(foundItemColor);
            item.ItemColors[updateIndex].Block = false;
        }

    }
}