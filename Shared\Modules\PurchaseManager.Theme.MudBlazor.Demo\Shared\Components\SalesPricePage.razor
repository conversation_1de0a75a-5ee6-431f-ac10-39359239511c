﻿<div class="ps-2 py-4 my-4">
    <EditForm Model="@SalesPrice">
        <DataAnnotationsValidator />
        <MudTextField Label="UnitPrice" HelperText="" @bind-Value="SalesPrice.UnitPrice"
                      For="@(() => SalesPrice.UnitPrice)" />
        <MudTextField Label="Description" HelperText="" @bind-Value="SalesPrice.Description"
                      For="@(() => SalesPrice.Description)" />
        <MudNumericField HideSpinButtons Label="Quantity" HelperText="" @bind-Value="SalesPrice.Quantity"
                         For="@(() => SalesPrice.Quantity)" />
        <MudNumericField HideSpinButtons Label="QuantityPerUnitOfMeasure" HelperText="" @bind-Value="SalesPrice.QuantityPerUnitOfMeasure"
                         For="@(() => SalesPrice.QuantityPerUnitOfMeasure)" />
        <MudSelect T="UnitOfMeasureDto" @bind-Value="@unitOfMeasure" ToStringFunc="@converter"
                   Label="Other Unit Of Measure" AnchorOrigin="Origin.BottomCenter">
            @foreach (var o in ListUnitOfMeasureSelected)
            {
                <MudSelectItem Value="@o" />
            }
        </MudSelect>
        <MudDateRangePicker PickerVariant="PickerVariant.Dialog" PickerClosed="OnPickerClose" @bind-DateRange="@dateRange" Margin="Margin.Dense"
                            PlaceholderStart="Start Date" PlaceholderEnd="End Date" MinDate="@DateTime.Now.Date" />
        <MudSpacer />
    </EditForm>

</div>
