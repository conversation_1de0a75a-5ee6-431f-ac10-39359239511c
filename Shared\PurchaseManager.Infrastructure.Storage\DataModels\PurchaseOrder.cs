﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
namespace PurchaseManager.Infrastructure.Storage.DataModels;
[Table("PurchaseOrders")]
[PrimaryKey("VendorNumber", "ItemNumber")]
public partial class PurchaseOrder
{
    [Key]
    [StringLength(100)]
    public string VendorNumber { get; set; } = null!;

    [Key]
    [StringLength(100)]
    public string ItemNumber { get; set; } = null!;

    [StringLength(100)]
    public string? ItemName { get; set; }
    [StringLength(100)]
    public string? VendorName { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }

    public int Min { get; set; }

    public int Max { get; set; }

    public int? Stock { get; set; }

    public int Suggest { get; set; }

    public int? Quantity { get; set; }
    public int? TotalOrder { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreateDate { get; set; }

    [StringLength(50)]
    public string? LastUserModified { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? LastDateModified { get; set; }

    public int Status { get; set; }
}
