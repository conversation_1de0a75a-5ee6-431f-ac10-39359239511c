@inherits VendorsPage
@page "/admin/vendors"
@attribute [Authorize(Policies.IsPurchaseUser)]

<PageTitle>@L["Vendor Accounts"]</PageTitle>

@if (listVendorUser == null)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    @* <MudOverlay Visible="@isLoading" DarkBackground="true" Absolute="true"> *@

        <div class="my-6"></div>
        <MudTable  FixedFooter FixedHeader Height="700px" Class="mt-4" T="UserVendorViewModel"
            ServerData="@(new Func<TableState, CancellationToken, Task<TableData<UserVendorViewModel>>>(ServerReload))"
            Striped="true" Bordered="true" Dense="true" Hover="true" Elevation="1" @ref="table">
            <ToolBarContent>
                <MudText Typo="Typo.h6">@L["Vendor Accounts"]</MudText>
                <MudSpacer />                 
                <MudSwitch T="bool?" Value="@isAllDeactivate" ValueChanged="@(async value => await OnShowAllDeactivateAccountClick((bool)value))" Label="@L["Deactivate"]"   Color="Color.Primary" UncheckedColor="Color.Default" />
                <MudSwitch T="bool?" Value="@isAllBlocked" ValueChanged="@(async value => await OnShowAllBlockedAccountClick((bool)value))" Label="@L["Blocked"]"   Color="Color.Error" UncheckedColor="Color.Default" />
                <MudTextField T="string" ValueChanged="@(s=>OnSearch(s))" Placeholder="Search" Adornment="Adornment.Start"
                    AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium" Class="mt-0"></MudTextField>
                <MudButton  Disabled="@isLoading" Variant="Variant.Filled"  OnClick="@(async _ => await GetReportVendorAccount())" Class="mx-3" Color="Color.Primary">
                    
                @if (isDownloading)
                    {
                        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                        <MudText Class="ms-2">Downloading</MudText>
                    }
                    else
                    {
                        <MudText>Download</MudText>
                    }
                </MudButton>
            </ToolBarContent>
            <HeaderContent>
                <MudTh>@L["UserName"]</MudTh>
                <MudTh>@L["TaxCode"]</MudTh>
                <MudTh>@L["VendorName"]</MudTh>
                <MudTh>@L["IsActive"]</MudTh>
                <MudTh>@L["IsBlock"]</MudTh>
            </HeaderContent>
            <RowTemplate Context="UserRow">
                <MudTd>
                    <div style="width:130px;">@UserRow.UserName</div>
                </MudTd>
                <MudTd>
                    <div style="width:130px;">@UserRow.TaxCode</div>
                </MudTd>
                <MudTd>
                    <div style="width:130px;">@UserRow.VendorName</div>
                </MudTd>
                <MudTd>
                    <div style="width:130px;">
                        @if (!UserRow.IsActive)
                        {
                            <MudTooltip Placement="Placement.Top" Text="@($"Verify user {UserRow.UserName}")">
                                <MudIconButton Color="Color.Primary"
                                    OnClick="@( _ => { showConfirmActivate = true ; selectedVendorUser = UserRow;})"
                                    Icon="@Icons.Material.Filled.CheckCircle">
                                </MudIconButton>
                            </MudTooltip>
                        }
                        else
                        {
                            <MudIconButton Color="Color.Success" Icon="@Icons.Material.Filled.CheckCircle">
                            </MudIconButton>
                        }
                    </div>
                </MudTd>
                <MudTd>
                    <div style="width:130px;">
                        <MudTooltip Placement="Placement.Top"
                            Text="@($"{(UserRow.IsBlock ? "Unblock" : "Block")} user {UserRow.UserName}")">
                            <MudIconButton OnClick="@( _ => { showToggleBlock = true; selectedVendorUser = UserRow;})"
                                Color="@(UserRow.IsBlock ? Color.Error : Color.Default)"
                                Icon="@Icons.Material.Filled.CheckCircle">
                            </MudIconButton>
                        </MudTooltip>
                    </div>
                </MudTd>
                <MudTd>
                    <div style="width:130px;">
                        <MudTooltip Placement="Placement.Top" Text="@($"Reset {UserRow.UserName}'s password")">
                            <MudIconButton
                                OnClick="@( _ => {isShowConfirmResetPass = true;  selectedVendorUser = UserRow;})"
                                Color="Color.Default" Icon="@Icons.Material.Filled.Refresh">
                            </MudIconButton>
                        </MudTooltip>
                    </div>
                </MudTd>
            </RowTemplate>
            <PagerContent>
                <MudTablePager RowsPerPageString=@L["Rows per page"] />
            </PagerContent>
        </MudTable>
    @* </MudOverlay> *@

    <MudDialog @bind-Visible="@showConfirmActivate">
        <DialogContent>
            <MudText Typo="Typo.h6">@L["Are you sure to activate user `{0}`?", selectedVendorUser.UserName]</MudText>
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="@( _ => showConfirmActivate = false)" Variant="Variant.Outlined" Color="Color.Primary">
                No
            </MudButton>
            <MudButton OnClick="@(async _ => await VerifyUser())" Class="ml-auto" Color="Color.Success">
                Yes, activate
            </MudButton>
        </DialogActions>
    </MudDialog>
    <MudDialog @bind-Visible="@showToggleBlock">
        <DialogContent>
            <MudText Typo="Typo.h6">@L["Are you sure to {0} user `{1}`?", selectedVendorUser.IsBlock ? "unblock" : "block",
            selectedVendorUser.UserName]</MudText>
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="@( _ => showToggleBlock = false)" Variant="Variant.Outlined" Color="Color.Primary">
                No
            </MudButton>
            <MudButton OnClick="@(async _ => await ToggleUser())" Class="ml-auto" Color="Color.Error">
                Yes, @(selectedVendorUser.IsBlock ? "Unblock" : "block")
            </MudButton>
        </DialogActions>
    </MudDialog>
    <MudDialog @bind-Visible="@isShowConfirmResetPass">
        <DialogContent>
            <MudText Typo="Typo.h6">@L["Are you sure to reset user's password ?"]</MudText>
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="@( _ => isShowConfirmResetPass = false)" Variant="Variant.Text" Color="Color.Primary">
                No
            </MudButton>
            <MudButton OnClick="@( _ =>  ResetVendorAccountPassword())" Class="ml-auto" Color="Color.Error">
                Yes
            </MudButton>
        </DialogActions>
    </MudDialog>
}

@code {
    public MudTable<UserVendorViewModel> table;

    private async Task<TableData<UserVendorViewModel>> ServerReload(TableState state, CancellationToken token)
    {
        await OnPage(state.Page, state.PageSize);

        return new TableData<UserVendorViewModel>() { TotalItems = totalItemsCount, Items = listVendorUser };
    }

    protected override async Task Reload()
    {
        await table.ReloadServerData();
    }
    protected void OnSearch(String text)
    {
        filter.UserName = text;
        table.ReloadServerData();
    }
}