﻿using System.Reflection;
using Finbuckle.MultiTenant;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.Storage;
namespace PurchaseManager.Storage;

public static class ServiceCollectionExtensions
{

    private const string ProjectName = nameof(PurchaseManager);

    public static IServiceCollection RegisterStorage(this IServiceCollection services, IConfiguration configuration)
    {
        #region Multitenancy
        services.AddDbContext<TenantStoreDbContext>(builder => GetDbContextOptions<TenantStoreDbContext>(builder, configuration));

        services.AddMultiTenant<TenantInfo>()
            .WithHostStrategy("__tenant__")
            .WithEFCoreStore<TenantStoreDbContext, TenantInfo>()
            .WithStaticStrategy(Settings.DefaultTenantId);
        #endregion Multitenancy

        services.AddDbContext<ApplicationDbContext>(builder => GetDbContextOptions<ApplicationDbContext>(builder, configuration));
        services.AddScoped<ApplicationPersistenceManager>();

        services.AddDbContext<LocalizationDbContext>(builder => GetDbContextOptions<LocalizationDbContext>(builder, configuration));
        services.AddScoped<LocalizationPersistenceManager>();

        services.AddTransient<IDatabaseInitializer, DatabaseInitializer>();
        services.AddSingleton<DapperContext>();

        return services;
    }

    private static void GetDbContextOptions<T>(DbContextOptionsBuilder builder, IConfiguration configuration) where T : DbContext
    {
        var migrationsAssembly = typeof(T).GetTypeInfo().Assembly.GetName().Name;
        var useSqlServer = !Convert.ToBoolean(configuration[$"{ProjectName}:UsePostgresServer"] ?? "false");

        if (useSqlServer)
        {
            var connectionString = configuration.GetConnectionString("DefaultConnection");

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new ArgumentNullException("The DefaultConnection was not found.");
            }

            if (!connectionString.ToLower().Contains("multipleactiveresultsets=true"))
            {
                throw new ArgumentException("When Sql Server is in use the DefaultConnection must contain: MultipleActiveResultSets=true");
            }

            builder.UseSqlServer(connectionString, sqlServerOptionsAction: options =>
            {
                options.UseNetTopologySuite();
                options.CommandTimeout(600);
                options.MigrationsAssembly(migrationsAssembly);
                options.UseCompatibilityLevel(120);
            });
        }
        else
        {
            builder.UseNpgsql(configuration.GetConnectionString("PostgresConnection"), npgsqlOptionsAction: options => options.MigrationsAssembly(migrationsAssembly));
        }
    }
}
