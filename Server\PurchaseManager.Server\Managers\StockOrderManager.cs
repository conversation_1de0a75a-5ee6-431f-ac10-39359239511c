﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Models.StockOrder;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Managers;

public class StockOrderManager : IStockOrderManager
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;
    private const string Business = "STOCKORDER";
    private const string BusinessHeader = "STOCKORDERHEADER";
    private const string BusinessLine = "STOCKORDERLINE";
    private const string Branch = "AL";
    private readonly IAdminManager _adminManager;
    public StockOrderManager(ApplicationDbContext context, IMapper mapper, IAdminManager adminManager)
    {
        _context = context;
        _mapper = mapper;
        _adminManager = adminManager;
    }
    public async Task<ApiResponse> GetReceiveLotsByPoHeaderAsync(StockOrderFilter filter)
    {
        try
        {
            var take = filter.PageSize ?? 10;
            var skip = (filter.PageIndex ?? 0) * take;
            var query = _context.StockOrders
                .Where(h =>
                    filter.HeaderNumber == null || h.HeaderNumber.Contains(filter.HeaderNumber));

            var totalRecords = await query.CountAsync();

            var result = await query
                .OrderByDescending(h => h.RowId)
                .Skip(skip)
                .Take(take)
                .ToListAsync();

            var pagedResult = new PagedResultDto<GetStockOrderDto>
            {
                RowCount = totalRecords,
                CurrentPage = filter.PageIndex ?? 0,
                PageSize = filter.PageSize ?? 10,
                Data = _mapper.Map<List<GetStockOrderDto>>(result)
            };

            return ApiResponse.S200(result: pagedResult);
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.Message);
        }
    }
    public async Task<ApiResponse> CreateReceiveLotsAsync(CreateStockOrderDto stockOrders)
    {
        try
        {
            var stockOrderNumber = await _adminManager.CreateNumberSeries(Business, Branch);
            var stockOrder = _mapper.Map<StockOrder>(stockOrders);

            stockOrder.Number = stockOrderNumber;
            stockOrder.CreateBy = _adminManager.GetUserLogin();
            stockOrder.Status = 1;// Always set to Draft when creating (will be updated to Saved when user clicks Save)

            var updateLineDto = new SOUpdatePoLineDto
            {
                PONumber = stockOrder.HeaderNumber,
                LineNumber = stockOrder.LineNumber,
                ItemNumber = stockOrder.ItemNumber,
                DeltaQuantity = stockOrder.QuantityReceived
            };

            var (isNotFound, value) = await UpdatePOLineQuantityAsync(updateLineDto);
            if (!isNotFound)
            {
                return value;
            }

            await _context.StockOrders.AddAsync(stockOrder);
            await _context.SaveChangesAsync();
            return ApiResponse.S200();
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.Message);
        }
    }

    private async Task<(bool flowControl, ApiResponse value)> UpdatePOLineQuantityAsync(SOUpdatePoLineDto updatePoLineDto)
    {
        var poLineToUpdate = await _context.PurchaseOrderLines.FirstOrDefaultAsync(x =>
            x.DocumentNumber == updatePoLineDto.PONumber &&
            x.LineNumber == updatePoLineDto.LineNumber &&
            x.ItemNumber == updatePoLineDto.ItemNumber);

        if (poLineToUpdate == null)
        {
            return (flowControl: false, value: ApiResponse.S400($"PO Line not found: {updatePoLineDto.PONumber}-{updatePoLineDto
                .LineNumber}-{updatePoLineDto.ItemNumber}"));
        }

        // Update QuantityReceived by adding the delta
        var newQuantityReceived = poLineToUpdate.QuantityReceived + updatePoLineDto.DeltaQuantity;

        // Validate: QuantityReceived cannot be negative
        if (newQuantityReceived < 0)
        {
            return (flowControl: false, value: ApiResponse.S400(
            $"Cannot reduce quantity below 0. Current: {poLineToUpdate.QuantityReceived}, Trying to change by: {updatePoLineDto
                .DeltaQuantity}"));
        }

        // Validate: QuantityReceived cannot exceed Quantity ordered (optional business rule)
        if (newQuantityReceived > poLineToUpdate.Quantity)
        {
            // Allow over-receiving but log warning
            // Could add a setting to strictly enforce this if needed
        }

        // Apply the changes
        poLineToUpdate.QuantityReceived = newQuantityReceived;

        // Calculate QuantityToReceive = Quantity ordered - Quantity received
        // If over-received, QuantityToReceive should be 0
        poLineToUpdate.QuantityToReceive = Math.Max(0, poLineToUpdate.Quantity - poLineToUpdate.QuantityReceived);

        return (flowControl: true, value: null);
    }


    public async Task<ApiResponse> UpdateReceiveLotsAsync(string number, UpdateStockOrderDto stockOrder)
    {
        var foundStockOrder = await _context.StockOrders.FirstOrDefaultAsync(x => x.Number == number);
        if (foundStockOrder is null)
        {
            return ApiResponse.S404("Stock Order not found");
        }

        var oldQuantity = foundStockOrder.QuantityReceived;
        var newQuantity = stockOrder.QuantityReceived;
        var deltaQuantity = newQuantity - oldQuantity;// Calculate the difference

        // Update the StockOrder entity
        _mapper.Map(stockOrder, foundStockOrder);
        // Status will be updated by SaveDraftStockOrdersAsync when user clicks Save button

        var updateLineDto = new SOUpdatePoLineDto
        {
            PONumber = stockOrder.HeaderNumber,
            LineNumber = stockOrder.LineNumber,
            ItemNumber = stockOrder.ItemNumber,
            DeltaQuantity = deltaQuantity
        };

        // Update POLine with the delta quantity
        var (isNotFound, value) = await UpdatePOLineQuantityAsync(updateLineDto);
        if (!isNotFound)
        {
            return value;
        }
        await _context.SaveChangesAsync();
        return ApiResponse.S200();
    }

    public async Task<ApiResponse> SaveDraftStockOrdersAsync(string headerNumber)
    {
        try
        {
            // Find all draft StockOrders for this PO Header
            var draftStockOrders = await _context.StockOrders
                .Where(x => x.HeaderNumber == headerNumber && x.Status == 1)
                .ToListAsync();

            if (draftStockOrders.Count == 0)
            {
                return ApiResponse.S200("No draft stock orders found to update");
            }
            // Update all draft status to saved
            foreach (var stockOrder in draftStockOrders)
            {
                stockOrder.Status = 2;// Set to Saved
            }

            await _context.SaveChangesAsync();

            return ApiResponse.S200($"Updated {draftStockOrders.Count} draft stock orders to saved status");
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.Message);
        }
    }

    public async Task<ApiResponse> GetLinesStockOrderAsync(string code)
    {
        try
        {
            var poLines = await _context.PurchaseOrderLines
                .Where(x => x.DocumentNumber == code)
                .ToListAsync();
            if (poLines.Count == 0)
            {
                return ApiResponse.S404("Document no detail");
            }

            var soLineMapper = poLines.Select(x => new SOLineGetDto
            {
                DocumentNumber = x.DocumentNumber,
                LineNumber = x.LineNumber,
                DocumentType = x.DocumentType,
                ItemNumber = x.ItemNumber,
                ItemName = x.ItemName,
                UnitOfMeasure = x.UnitOfMeasure,
                Quantity = x.Quantity,
                QuantityReceived = x.QuantityReceived,
                QuantityToReceive = x.QuantityToReceive,
                QuantityInvoiced = x.QuantityInvoiced,
                LotNo = x.LotNo,
                ExpirationDate = x.ExpirationDate?.ToDateTime(TimeOnly.MinValue),
                ConversionRateDisplay = GetConversionRateDisplay(x.ItemNumber, x.UnitOfMeasure)
            }).ToList();

            return ApiResponse.S200(result: soLineMapper);
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.GetBaseException().Message);
        }
    }

    private string GetConversionRateDisplay(string itemNumber, string unitOfMeasure)
    {
        try
        {
            // Query ItemUnitOfMeasure table to get conversion rate
            var itemUnits = _context.ItemUnitOfMeasures
                .Where(x => x.ItemNumber == itemNumber)
                .ToList();

            if (!itemUnits.Any())
            {
                return string.Empty;
            }

            // Tìm đơn vị cơ sở (QtyPerUnitOfMeasure = 1)
            var baseUnit = itemUnits.FirstOrDefault(x => x.QuantityPerUnitOfMeasure == 1);

            // Tìm đơn vị hiện tại
            var currentUnit = itemUnits.FirstOrDefault(x =>
                x.Code.Equals(unitOfMeasure, StringComparison.CurrentCultureIgnoreCase));

            if (baseUnit != null && currentUnit != null)
            {
                return $"{currentUnit.QuantityPerUnitOfMeasure:0.##}/{baseUnit.Code}";
            }

            return string.Empty;
        }
        catch
        {
            return string.Empty;
        }
    }

    #region New Header/Line Methods
    public async Task<ApiResponse> GetStockOrderHeadersAsync(StockOrderFilter filter)
    {
        try
        {
            var take = filter.PageSize ?? 10;
            var skip = (filter.PageIndex ?? 0) * take;
            var query = _context.StockOrderHeaders
                .Include(h => h.StockOrderLines)
                .Where(h =>
                    filter.HeaderNumber == null || h.POHeaderNumber.Contains(filter.HeaderNumber));

            var totalRecords = await query.CountAsync();

            var result = await query
                .OrderByDescending(h => h.Number)
                .Skip(skip)
                .Take(take)
                .ToListAsync();

            var pagedResult = new PagedResultDto<GetStockOrderHeaderDto>
            {
                RowCount = totalRecords,
                CurrentPage = filter.PageIndex ?? 0,
                PageSize = filter.PageSize ?? 10,
                Data = _mapper.Map<List<GetStockOrderHeaderDto>>(result)
            };

            return ApiResponse.S200(result: pagedResult);
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.Message);
        }
    }

    public async Task<ApiResponse> GetStockOrderHeaderByNumberAsync(string number)
    {
        try
        {
            var stockOrderHeader = await _context.StockOrderHeaders
                .Include(h => h.StockOrderLines)
                .FirstOrDefaultAsync(h => h.Number == number);

            if (stockOrderHeader == null)
            {
                return ApiResponse.S404("Stock Order Header not found");
            }

            var result = _mapper.Map<GetStockOrderHeaderDto>(stockOrderHeader);
            return ApiResponse.S200(result: result);
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.Message);
        }
    }

    public async Task<ApiResponse> CreateDraftStockOrderAsync(CreateStockOrderHeaderDto createDto)
    {
        try
        {
            // Create a draft without finalizing - just store the data
            var stockOrderNumber = await _adminManager.CreateNumberSeries(Business, Branch);
            var currentUser = _adminManager.GetUserLogin();
            var currentTime = DateTime.Now;

            var stockOrderHeader = new StockOrderHeader
            {
                Number = stockOrderNumber,
                POHeaderNumber = createDto.POHeaderNumber,
                DocumentType = createDto.DocumentType,
                CreatedAt = currentTime,
                CreatedBy = currentUser,
                Status = 1,// Draft
                IsERPSynced = false,
                Note = createDto.Note
            };

            var stockOrderLines = new List<StockOrderLine>();
            for (var i = 0; i < createDto.Lines.Count; i++)
            {
                var lineDto = createDto.Lines[i];
                var line = new StockOrderLine
                {
                    StockOrderNumber = stockOrderNumber,
                    LineNumber = i + 1,
                    POHeaderNumber = lineDto.POHeaderNumber,
                    POLineNumber = lineDto.POLineNumber,
                    DocumentType = lineDto.DocumentType,
                    ItemNumber = lineDto.ItemNumber,
                    LotNo = lineDto.LotNo,
                    ExpirationDate = lineDto.ExpirationDate,
                    TotalQuantity = lineDto.TotalQuantity,
                    QuantityReceived = lineDto.QuantityReceived,
                    ItemName = lineDto.ItemName,
                    Note = lineDto.Note,
                    CreatedAt = currentTime,
                    CreatedBy = currentUser
                };
                stockOrderLines.Add(line);
            }

            await _context.StockOrderHeaders.AddAsync(stockOrderHeader);
            await _context.StockOrderLines.AddRangeAsync(stockOrderLines);
            await _context.SaveChangesAsync();

            return ApiResponse.S200(result: new
            {
                StockOrderNumber = stockOrderNumber
            });
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.Message);
        }
    }

    public async Task<ApiResponse> UpdateDraftStockOrderAsync(string stockOrderNumber, SaveStockOrderDto updateDto)
    {
        try
        {
            var stockOrderHeader = await _context.StockOrderHeaders
                .Include(h => h.StockOrderLines)
                .FirstOrDefaultAsync(h => h.Number == stockOrderNumber);

            if (stockOrderHeader == null)
            {
                return ApiResponse.S404("Stock Order Header not found");
            }

            if (stockOrderHeader.Status != 1)// Only allow updates on Draft status
            {
                return ApiResponse.S400("Can only update draft stock orders");
            }

            // Update header
            stockOrderHeader.Note = updateDto.Note;

            // Remove existing lines
            _context.StockOrderLines.RemoveRange(stockOrderHeader.StockOrderLines);

            // Add new lines
            var newLines = updateDto.Lines.Select((lineDto, i) => new StockOrderLine
                {
                    StockOrderNumber = stockOrderNumber,
                    LineNumber = i + 1,
                    POHeaderNumber = lineDto.POHeaderNumber,
                    POLineNumber = lineDto.POLineNumber,
                    DocumentType = lineDto.DocumentType,
                    ItemNumber = lineDto.ItemNumber,
                    LotNo = lineDto.LotNo,
                    ExpirationDate = lineDto.ExpirationDate,
                    TotalQuantity = lineDto.TotalQuantity,
                    QuantityReceived = lineDto.QuantityReceived,
                    ItemName = lineDto.ItemName,
                    Note = lineDto.Note,
                    CreatedAt = stockOrderHeader.CreatedAt,
                    CreatedBy = stockOrderHeader.CreatedBy
                })
                .ToList();

            await _context.StockOrderLines.AddRangeAsync(newLines);
            await _context.SaveChangesAsync();

            return ApiResponse.S200();
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.Message);
        }
    }

    public async Task<ApiResponse> FinalizeStockOrderAsync(string stockOrderNumber, SaveStockOrderDto finalizeDto)
    {
        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var stockOrderHeader = await _context.StockOrderHeaders
                .Include(h => h.StockOrderLines)
                .FirstOrDefaultAsync(h => h.Number == stockOrderNumber);

            if (stockOrderHeader == null)
            {
                return ApiResponse.S404("Stock Order Header not found");
            }

            if (stockOrderHeader.Status != 1)// Only allow finalization of Draft status
            {
                return ApiResponse.S400("Can only finalize draft stock orders");
            }

            // Update header
            stockOrderHeader.Note = finalizeDto.Note;
            stockOrderHeader.Status = 2;// Set to Saved/Finalized

            // Remove existing lines
            _context.StockOrderLines.RemoveRange(stockOrderHeader.StockOrderLines);

            // Add final lines and update PO lines
            var newLines = new List<StockOrderLine>();
            for (var i = 0; i < finalizeDto.Lines.Count; i++)
            {
                var lineDto = finalizeDto.Lines[i];
                var line = new StockOrderLine
                {
                    StockOrderNumber = stockOrderNumber,
                    LineNumber = i + 1,
                    POHeaderNumber = lineDto.POHeaderNumber,
                    POLineNumber = lineDto.POLineNumber,
                    DocumentType = lineDto.DocumentType,
                    ItemNumber = lineDto.ItemNumber,
                    LotNo = lineDto.LotNo,
                    ExpirationDate = lineDto.ExpirationDate,
                    TotalQuantity = lineDto.TotalQuantity,
                    QuantityReceived = lineDto.QuantityReceived,
                    ItemName = lineDto.ItemName,
                    Note = lineDto.Note,
                    CreatedAt = stockOrderHeader.CreatedAt,
                    CreatedBy = stockOrderHeader.CreatedBy
                };
                newLines.Add(line);

                // Update PO Line quantities
                var updateLineDto = new SOUpdatePoLineDto
                {
                    PONumber = lineDto.POHeaderNumber,
                    LineNumber = lineDto.POLineNumber,// Use POLineNumber from StockOrderLine
                    ItemNumber = lineDto.ItemNumber,
                    DeltaQuantity = lineDto.QuantityReceived
                };

                var (isNotFound, value) = await UpdatePOLineQuantityAsync(updateLineDto);
                if (!isNotFound)
                {
                    await transaction.RollbackAsync();
                    return value;
                }
            }

            await _context.StockOrderLines.AddRangeAsync(newLines);
            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            return ApiResponse.S200(result: new
            {
                Message = "Stock Order finalized successfully",
                StockOrderNumber = stockOrderNumber
            });
        }
        catch (Exception e)
        {
            await transaction.RollbackAsync();
            return ApiResponse.S500(e.Message);
        }
    }

    /// <summary>
    /// Tạo StockOrderHeader và Lines luôn với trạng thái Final (không qua draft)
    /// </summary>
    public async Task<ApiResponse> CreateFinalStockOrderAsync(CreateStockOrderHeaderDto createDto)
    {
        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // Tạo StockOrderNumber mới
            var stockOrderNumber = await _adminManager.CreateNumberSeries(BusinessHeader, Branch);
            var currentUser = _adminManager.GetUserLogin();
            var currentTime = DateTime.Now;

            // Tạo Header với status = 2 (Final)
            var header = new StockOrderHeader
            {
                Number = stockOrderNumber,
                POHeaderNumber = createDto.POHeaderNumber,
                DocumentType = createDto.DocumentType,
                Note = createDto.Note,
                Status = 2,// 2 = Final (không qua draft)
                CreatedAt = currentTime,
                CreatedBy = currentUser,
                IsERPSynced = false
            };

            await _context.StockOrderHeaders.AddAsync(header);

            // Tạo Lines và update PO Line quantities
            var lines = new List<StockOrderLine>();
            for (var i = 0; i < createDto.Lines.Count; i++)
            {
                var lineDto = createDto.Lines[i];
                var stockOrderLineNumber = await _adminManager.CreateNumberSeries(BusinessLine, Branch);
                var line = new StockOrderLine
                {
                    Number = stockOrderLineNumber,
                    StockOrderNumber = stockOrderNumber,
                    LineNumber = i + 1,
                    POHeaderNumber = lineDto.POHeaderNumber,
                    POLineNumber = lineDto.POLineNumber,
                    DocumentType = lineDto.DocumentType,
                    ItemNumber = lineDto.ItemNumber,
                    LotNo = lineDto.LotNo,
                    ExpirationDate = lineDto.ExpirationDate,
                    TotalQuantity = lineDto.TotalQuantity,
                    QuantityReceived = lineDto.QuantityReceived,
                    ItemName = lineDto.ItemName,
                    Note = lineDto.Note,
                    CreatedAt = currentTime,
                    CreatedBy = currentUser
                };
                lines.Add(line);
            }

            await _context.StockOrderLines.AddRangeAsync(lines);
            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            return ApiResponse.S200("Stock Order created successfully");
        }
        catch (Exception e)
        {
            await transaction.RollbackAsync();
            return ApiResponse.S500(e.Message);
        }
    }

    /// <summary>
    /// Cập nhật status của nhiều StockOrder cùng lúc
    /// </summary>
    public async Task<ApiResponse> BatchUpdateStockOrderStatusAsync(BatchUpdateStockOrderStatusDto dto)
    {
        try
        {
            var stockOrders = await _context.StockOrders
                .Where(x => dto.StockOrderNumbers.Contains(x.Number))
                .ToListAsync();

            if (stockOrders.Count == 0)
            {
                return ApiResponse.S404("No StockOrders found with provided numbers");
            }

            foreach (var stockOrder in stockOrders)
            {
                stockOrder.Status = dto.Status;
                stockOrder.IsCreateSO = true;
            }

            await _context.SaveChangesAsync();

            return ApiResponse.S200(result: new
            {
                Message = $"Updated {stockOrders.Count} StockOrders to status {dto.Status}",
                UpdatedCount = stockOrders.Count
            });
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.Message);
        }
    }
    #endregion
}
