﻿namespace PurchaseManager.Shared.Dto.PO;

public class POHeaderGetDto
{
    public string Number { get; set; } = null!;
    public string BuyFromVendorNumber { get; set; } = null!;
    public string BuyFromVendorName { get; set; } = null!;
    public string PayToVendorNumber { get; set; } = null!;
    public string PayToName { get; set; } = null!;
    public string AppliesToDocumentNumber { get; set; } = null!;
    public string PayToAddress { get; set; } = null!;
    public string ShipToName { get; set; } = null!;
    public string ShipToAddress { get; set; } = null!;
    public string PostingDescription { get; set; } = null!;
    public string ExternalDocumentNumber { get; set; } = null!;
    public decimal PaymentDiscount { get; set; }
    public string SourceCode { get; set; } = null!;
    public string LocationCode { get; set; } = null!;
    public string VendorPostingGroup { get; set; } = null!;
    public string CurrencyCode { get; set; } = null!;
    public string PurchaserCode { get; set; } = null!;
    public string VATRegistrationNumber { get; set; } = null!;
    public string BuyFromAddress { get; set; } = null!;
    public string VATBusinessPostingGroup { get; set; } = null!;
    public int Status { get; set; }
    public int Ship { get; set; }
    public string LoginId { get; set; } = string.Empty;
    public decimal OverheadRate { get; set; }
    public int Checked { get; set; }
    public string YourReference { get; set; } = null!;
    public int Invoice { get; set; }
    public int Receive { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal TotalQuantityReceived { get; set; }
    public decimal TotalQuantity { get; set; }
    public string UsingID { get; set; } = string.Empty;
    public int UsingMinutes { get; set; }
    public string ModifiedID { get; set; } = string.Empty;
    public decimal TotalRecord { get; set; }
    public string VendorApprovalBy { get; set; } = string.Empty;
    public string PurchaserApprovalBy { get; set; } = string.Empty;
    public int DocNoOccurrence { get; set; }
    public string BuyFromContact { get; set; } = string.Empty;
    public DateTime DocumentDate { get; set; }
    public DateTime PostingDate { get; set; }
    public DateTime OrderDate { get; set; }
    public DateTime DueDate { get; set; }
    public DateTime CreatedAtTime { get; set; }
    public DateTime? DocumentTime { get; set; }
    public DateTime? LastModifiedTime { get; set; }
    public DateTime? BeginUsingTime { get; set; }
    public DateTime? DateReceived { get; set; }
    public DateTime? TimeReceived { get; set; }
    public DateTime? DateSent { get; set; }
    public DateTime? RequestedReceiptDate { get; set; }
    public DateTime? PromisedReceiptDate { get; set; }
    public DateTime? TimeSent { get; set; }
}


