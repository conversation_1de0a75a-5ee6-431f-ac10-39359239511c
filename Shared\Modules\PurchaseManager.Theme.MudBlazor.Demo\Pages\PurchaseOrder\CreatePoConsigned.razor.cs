using System.Globalization;
using System.Net.Http.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using MudBlazor;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Providers;

namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder;

public partial class CreatePoConsigned : ComponentBase
{
    [Inject]
    private HttpClient HttpClient { get; set; }
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    [Inject]
    private DialogService DialogService { get; set; }
    [Inject]
    protected IStringLocalizer<Global> L { get; set; }
    [Inject]
    protected NavigationManager NavigationManager { get; set; }
    [Inject]
    private IPurchaseOrderApiClient PurchaseOrderApiClient { get; set; }
    protected MudAutocomplete<GetVendorDto> AutoComplete { get; set; }
    [Inject] AuthenticationStateProvider AuthStateProvider { get; set; }

    protected UserViewModel UserViewModel { get; set; } = new UserViewModel();
    protected DocNoOccurrenceEnum DocNoOccurrence { get; set; } = DocNoOccurrenceEnum.Consigned;

    protected override async Task OnInitializedAsync()
    {
        UserViewModel = await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
        await base.OnInitializedAsync();
    }

    protected async Task Create()
    {
        // Valid
        if (AutoComplete.Value?.Number == null)
        {
            await DialogService.ShowMessageBox("Warning", "Bạn chưa chọn chứng từ");
            return;
        }

        /* * */
        var vendorNumber = AutoComplete.Value.Number;
        var apiResponse = await PurchaseOrderApiClient.CreateHeaderAsync(vendorNumber, null);
        
        if (!apiResponse.IsSuccessStatusCode)
        {
            ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Error);
            return;
        }

        var headers = apiResponse.Result;
        if (DocNoOccurrence == DocNoOccurrenceEnum.Consigned)
        {
            var headerDto = new UpdatePOHeaderDto
            {
                Number = headers.Number,
                DueDate = headers.DueDate,
                OrderDate = headers.OrderDate,
                PurchaseUser = UserViewModel.UserName,
                VendorNo = headers.BuyFromVendorNumber,
                DocNoOccurrence = 1,
                PostingDescription = "",
                PurchaserApprovalBy = "",
                VendorApprovalBy = "",

            };
            await PurchaseOrderApiClient.UpdateHeader(headerDto);
        }
        NavigationManager.NavigateTo($"/po/{headers.Number}/detail");
    }

    protected async Task<IEnumerable<GetVendorDto>> ItemSearch(string value, CancellationToken token)
    {
        var apiResponse = await
            HttpClient.GetFromJsonAsync<ApiResponseDto<List<GetVendorDto>>>($"api/vendor/search?number={value}",
                cancellationToken: token);
        return apiResponse.IsSuccessStatusCode ? apiResponse.Result : new List<GetVendorDto>();
    }
}
