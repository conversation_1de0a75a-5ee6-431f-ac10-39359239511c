﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Table("PurchaseSuggestedPaymentHeader")]
public class PurchaseSuggestedPaymentHeader
{
    public int DocumentType { get; set; }

    [Key]
    [StringLength(100)]
    public string Number { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string BuyFromVendorNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string PayToVendorNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(160)]
    public string PayToName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string PayToAddress { get; set; } = string.Empty;

    [Required]
    [StringLength(160)]
    public string YourReference { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string ShipToCode { get; set; } = string.Empty;

    [Required]
    [StringLength(160)]
    public string ShipToName { get; set; } = string.Empty;

    [Column(TypeName = "datetime")]
    public DateTime OrderDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime PostingDate { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime ExpectedReceiptDate { get; set; }

    [Required]
    [StringLength(100)]
    public string PostingDescription { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string PaymentTermsCode { get; set; } = string.Empty;

    [Column(TypeName = "datetime")]
    public DateTime DueDate { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal PaymentDiscount { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime PmtDiscountDate { get; set; }

    [Required]
    [StringLength(100)]
    public string ShipmentMethodCode { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string LocationCode { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string CurrencyCode { get; set; } = string.Empty;

    [Column(TypeName = "decimal(28, 10)")]
    public decimal CurrencyFactor { get; set; }

    [Required]
    [StringLength(100)]
    public string InvoiceDiscCode { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string PurchaserCode { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string OrderClass { get; set; } = string.Empty;

    public int NumberPrinted { get; set; }

    [Required]
    [StringLength(6)]
    public string OnHold { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string AppliesToDocNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string AccountNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string JobNumber { get; set; } = string.Empty;

    public int Invoice { get; set; }

    [Required]
    [StringLength(100)]
    public string ExternalDocumentNumber { get; set; } = string.Empty;

    [Required]
    [Column("VATRegistrationNumber")]
    [StringLength(100)]
    public string VatregistrationNumber { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string ReasonCode { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string TransactionType { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string TransportMethod { get; set; } = string.Empty;

    public int AccountType { get; set; }

    [Required]
    [StringLength(100)]
    public string EntryPoint { get; set; } = string.Empty;

    public int Correction { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime DocumentDate { get; set; }

    [Required]
    [StringLength(100)]
    public string Area { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string TransactionSpecification { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string PaymentMethodCode { get; set; } = string.Empty;

    [Required]
    [Column("VATBusPostingGroup")]
    [StringLength(100)]
    public string VatbusPostingGroup { get; set; } = string.Empty;

    public int Status { get; set; }

    [Required]
    [StringLength(100)]
    public string CampaignNumber { get; set; } = string.Empty;

    [Column(TypeName = "datetime")]
    public DateTime RequestedReceiptDate { get; set; }

    [Column("PromisedReceipt Date", TypeName = "datetime")]
    public DateTime PromisedReceiptDate { get; set; }

    [Required]
    [StringLength(64)]
    public string LeadTimeCalculation { get; set; } = string.Empty;

    [Required]
    [StringLength(64)]
    public string SourceCode { get; set; } = string.Empty;

    [Required]
    [Column("VATDescription")]
    [StringLength(100)]
    public string Vatdescription { get; set; } = string.Empty;

    [Column(TypeName = "datetime")]
    public DateTime DateReceived { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime TimeReceived { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime DateSent { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime TimeSent { get; set; }

    [Required]
    [Column("LoginID")]
    [StringLength(100)]
    public string LoginId { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string OriginalCountry { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string BankName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string BankCode { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string BankAddress { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string PersonIncharge { get; set; } = string.Empty;

    [Column(TypeName = "datetime")]
    public DateTime IssueDate { get; set; }

    [Required]
    [StringLength(100)]
    public string PlaceOfIssue { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string CertificateNumber { get; set; } = string.Empty;

    [Column("RowID")]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }

    [Column("LastUsingId")]
    [StringLength(20)]
    [Unicode(false)]
    public string? LastUsingId { get; set; } = string.Empty;

    [Column(TypeName = "datetime")]
    public DateTime? LastOpenTime { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? LastCloseTime { get; set; }

    [Column("LastCloseId")]
    [StringLength(20)]
    [Unicode(false)]
    public string? LastCloseId { get; set; } = string.Empty;

    [InverseProperty("DocumentNumberNavigation")]
    public virtual ICollection<PurchaseSuggestedPaymentLine> PurchaseSuggestedPaymentLines { get; set; } = new List<PurchaseSuggestedPaymentLine>();
}
