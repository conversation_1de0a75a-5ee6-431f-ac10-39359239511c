﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

[PrimaryKey("ItemNumber", "Code")]
[Table("ItemUnitOfMeasure")]
public partial class ItemUnitOfMeasure
{
    [Key]
    [StringLength(100)]
    public string ItemNumber { get; set; } = null!;

    [Key]
    [StringLength(100)]
    public string Code { get; set; } = null!;

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }

    public decimal QuantityPerUnitOfMeasure { get; set; }

    [StringLength(160)]
    public string? Description { get; set; }

    public int? Type { get; set; }

    public int? Status { get; set; }

    [Column("Last Date Modified", TypeName = "datetime")]
    public DateTime? LastDateModified { get; set; }

    [StringLength(50)]
    public string? LoginId { get; set; }

    public int? Block { get; set; }

    [ForeignKey("Code")]
    [InverseProperty("ItemUnitOfMeasures")]
    public virtual UnitOfMeasure CodeNavigation { get; set; } = null!;

    [ForeignKey("ItemNumber")]
    [InverseProperty("ItemUnitOfMeasures")]
    public virtual Item ItemNumberNavigation { get; set; } = null!;
}
