﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Table("InventoryControl")]
public class InventoryControl
{
    [Key]
    [Column("InventoryID")]
    [StringLength(100)]
    public string InventoryId { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }

    public int StockQuantity { get; set; }

    public int? QuantityIn { get; set; }

    public int? QuantityOut { get; set; }

    public DateOnly Date { get; set; }

    public int MaxInventory { get; set; }

    public int QuantityAvailable { get; set; }

    [Required]
    [StringLength(50)]
    public string CreateBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreateAt { get; set; }

    [StringLength(50)]
    public string UpdateBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? UpdateAt { get; set; }
}
