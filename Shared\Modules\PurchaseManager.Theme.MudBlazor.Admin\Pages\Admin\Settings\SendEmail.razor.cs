﻿using Microsoft.AspNetCore.Components.Web;

using MudBlazor;

using PurchaseManager.Shared.Dto.Email;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.UI.Base.Pages.Admin.Settings;

using System.ComponentModel.DataAnnotations;
namespace PurchaseManager.Theme.Material.Admin.Pages.Admin.Settings
{
    public partial class SendEmailBase : SettingsBase
    {
        protected bool isLoading { get; set; }
        protected DetailEmailTemplate detailEmailTemplate { get; set; } = new DetailEmailTemplate();
        protected Func<DetailEmailTemplate, string> converter = p => p?.TemplateName;
        protected List<DetailEmailTemplate> listDetailEmailTemplate { get; set; } = new();
        protected EmailRequestDto requestDto { get; set; } = new EmailRequestDto();
        protected MudForm form;
        [DataType(DataType.EmailAddress)]
        protected string toAddressEditing { get; set; }
        protected string bodyPreview { get; set; } = "";
        protected MudTextField<string> toAddressRef { get; set; } = new MudTextField<string>();
        protected MudTextField<string> ccRef { get; set; } = new MudTextField<string>();
        protected MudTextField<string> bccRef { get; set; } = new MudTextField<string>();
        protected string ccEditing { get; set; }
        protected string bccEditing { get; set; }
        protected bool success;
        protected bool isCC { get; set; }
        protected bool isBCC { get; set; }
        protected MudBlazor.Extensions.Components.MudExHtmlEdit mudExHtmlEditRef { get; set; } = new MudBlazor.Extensions.Components.MudExHtmlEdit();
        protected bool isShowPreview { get; set; } = false;
        protected string[] errors = { };
        protected IReadOnlyCollection<string> _selectedMode = new List<string>(); // cc or bcc
        protected override async Task OnInitializedAsync()
        {
            isLoading = true;
            await LoadDetailEmailTemplate();
            isLoading = false;
            await base.OnInitializedAsync();
        }
        protected async Task LoadDetailEmailTemplate()
        {
            var resp = await apiClient.GetItems<DetailEmailTemplate>("GetEmailTemplates");
            if (!resp.Results.Any()) viewNotifier.Show("No Data", ViewNotifierType.Error, L["Operation Failed!"]);
            else
            {
                listDetailEmailTemplate = resp.Results.ToList();
            }
        }
        //To
        protected async Task OnToAddressChanged(KeyboardEventArgs e)
        {
            if (toAddressRef is not null && requestDto.ToAddresses is not null && requestDto.ToAddresses.Find(x => x.Address == toAddressRef.Value) != null) viewNotifier.Show(L["Already Exist Email"], ViewNotifierType.Error, L["Operation Failed"]);
            else
            if ((e.Code == "Enter" || e.Code == "NumpadEnter") && !toAddressRef.ValidationErrors.Any())
            {
                if (requestDto.ToAddresses is null)
                {
                    requestDto.ToAddresses = [new EmailAddressDto(name: toAddressRef.Value, address: toAddressRef.Value)];
                }
                else
                {
                    requestDto.ToAddresses.Add(new EmailAddressDto(name: toAddressRef.Value, address: toAddressRef.Value));
                }
                await toAddressRef.Clear();
            }
        }
        protected async Task OnClickToEditToAddress(string name)
        {
            var itemToRemove = new EmailAddressDto("", "");
            requestDto.ToAddresses.ForEach(x =>
            {
                if (x.Address == name)
                {
                    toAddressEditing = x.Address;
                    itemToRemove = x;
                }
            });
            if (itemToRemove.Address != "")
            {
                requestDto.ToAddresses.Remove(itemToRemove);
            }
            await toAddressRef.FocusAsync();
        }
        // CC
        protected async Task OnCCChanged(KeyboardEventArgs e)
        {
            if (ccRef is not null && requestDto.CcAddresses is not null && requestDto.CcAddresses.Find(x => x.Address == ccRef.Value) != null) viewNotifier.Show(L["Already Exist Email"], ViewNotifierType.Error, L["Operation Failed"]);
            else if ((e.Code == "Enter" || e.Code == "NumpadEnter") && !ccRef.ValidationErrors.Any())
            {
                if (requestDto.CcAddresses is null)
                {
                    requestDto.CcAddresses = [new EmailAddressDto(name: ccRef.Value, address: ccRef.Value)];
                }
                else
                {
                    requestDto.CcAddresses.Add(new EmailAddressDto(name: ccRef.Value, address: ccRef.Value));
                }
                await ccRef.Clear();
            }
        }
        protected async Task OnClickToEditCC(string name)
        {
            var itemToRemove = new EmailAddressDto("", "");
            requestDto.CcAddresses.ForEach(x =>
            {
                if (x.Address == name)
                {
                    ccEditing = x.Address;
                    itemToRemove = x;
                }
            });
            if (itemToRemove.Address != "")
            {
                requestDto.CcAddresses.Remove(itemToRemove);
            }
            await ccRef.FocusAsync();
        }
        // BCC
        protected async Task OnBCCChanged(KeyboardEventArgs e)
        {
            if (bccRef is not null && requestDto.BccAddresses is not null && requestDto.BccAddresses.Find(x => x.Address == bccRef.Value) != null) viewNotifier.Show(L["Already Exist Email"], ViewNotifierType.Warning, L["Operation Warning"]);
            else if ((e.Code == "Enter" || e.Code == "NumpadEnter") && !bccRef.ValidationErrors.Any())
            {
                if (requestDto.BccAddresses is null)
                {
                    requestDto.BccAddresses = [new EmailAddressDto(name: bccRef.Value, address: bccRef.Value)];
                }
                else
                {
                    requestDto.BccAddresses.Add(new EmailAddressDto(name: bccRef.Value, address: bccRef.Value));
                }
                await bccRef.Clear();
            }
        }
        protected async Task OnClickToEditBCC(string name)
        {
            var itemToRemove = new EmailAddressDto("", "");
            requestDto.BccAddresses.ForEach(x =>
            {
                if (x.Address == name)
                {
                    bccEditing = x.Address;
                    itemToRemove = x;
                }
            });
            if (itemToRemove.Address != "")
            {
                requestDto.BccAddresses.Remove(itemToRemove);
            }
            await bccRef.FocusAsync();
        }
        protected async Task<IEnumerable<DetailEmailTemplate>> SearchTemplate(string value, CancellationToken token)
        {
            isShowPreview = false;
            if (string.IsNullOrWhiteSpace(value)) return listDetailEmailTemplate;
            await Task.Delay(5);
            return listDetailEmailTemplate.Where(x => x.TemplateName.ToLower().Contains(value.ToLower()));
        }
        protected async Task SendEmail()
        {
            try
            {
                requestDto.Body = detailEmailTemplate.Body;
                requestDto.Subject = detailEmailTemplate.Subject;
                requestDto.IsHtml = detailEmailTemplate.Body.Contains("DOCTYPE html");
                requestDto.TemplateId = detailEmailTemplate.EmailTemplatesId;
                if (!_selectedMode.Contains("CC"))
                {
                    requestDto.CcAddresses = null;
                }
                if (!_selectedMode.Contains("BCC"))
                {
                    requestDto.BccAddresses = null;
                }
                var resp = await apiClient.SendEmail(requestDto);
                if (!resp.IsSuccessStatusCode) viewNotifier.Show(L[resp.Message], ViewNotifierType.Error, L["Operation Failed"]);
                else
                {
                    viewNotifier.Show(L["Send Email Success"], ViewNotifierType.Success, L["Operation Successful"]);
                }
            }
            catch (Exception ex)
            {
                viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
        }
    }
}
