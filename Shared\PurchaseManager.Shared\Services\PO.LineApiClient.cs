﻿using System.Net.Http.Json;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Extensions;
namespace PurchaseManager.Shared.Services;

public partial class PurchaseOrderApiClient
{
    public async Task<ApiResponseDto<POLineGetDto>> AddLine(POLineAddOrUpdate detailPos)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<POLineGetDto>>($"{BaseUrl}/line", detailPos);
    }
    public async Task<ApiResponseDto<List<POLineGetDto>>> AddMultipleLines(List<POLineAddOrUpdate> detailPos)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<List<POLineGetDto>>>($"{BaseUrl}/multiple-lines", detailPos);

    }
    public async Task<ApiResponseDto> DeleteLine(string documentNumber, string itemNumber, int rowId)
    {
        return await httpClient.DeleteAsync<ApiResponseDto>(
        $"{BaseUrl}/{documentNumber}/{itemNumber}/{rowId}");
    }

    public async Task<ApiResponseDto<List<POLineGetDto>>> GetLines(string purchaseOrderNumber)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<List<POLineGetDto>>>(
        $"{BaseUrl}/line/{purchaseOrderNumber}");
    }

    public async Task<ApiResponseDto<int>> UpdatePurchaseOrderLine(POLineAddOrUpdate updateLine)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>($"{BaseUrl}/line", updateLine);
    }
}
