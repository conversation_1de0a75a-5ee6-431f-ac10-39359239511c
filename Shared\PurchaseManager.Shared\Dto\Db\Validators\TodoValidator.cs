﻿using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Validators;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace PurchaseManager.Shared.Dto.Db.Validators
{
    public class TodoValidator : LocalizedAbstractValidator<Todo>
    {
        public TodoValidator(IStringLocalizer<Global> l) : base(l)
        {
            RuleFor(p => p.Title)
                .NotEmpty()
                .MaximumLength(128).WithName(L["Title"]);
        }
    }
}
