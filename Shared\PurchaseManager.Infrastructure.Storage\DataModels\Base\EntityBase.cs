using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace PurchaseManager.Infrastructure.Storage.DataModels.Base;

/// <summary>
///     Base entity class with common properties for all entities
/// </summary>
public abstract class EntityBase
{
    /// <summary>
    ///     Primary key - Business code/identifier
    /// </summary>
    [Key]
    public string Number { get; set; } = null!;

    /// <summary>
    ///     Auto-generated identity column for internal use
    /// </summary>
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }
}
