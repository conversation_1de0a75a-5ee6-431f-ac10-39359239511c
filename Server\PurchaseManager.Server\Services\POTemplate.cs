﻿using PurchaseManager.Shared.Dto.PO;
using QuestPDF;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
namespace PurchaseManager.Server.Services;

public class POTemplate
{
    public static byte[]? GenerationPDF(IEnumerable<POReportPDF> rp)
    {
        try
        {
            Settings.License = LicenseType.Community;
            var stream = new MemoryStream();
            var pdf =
                Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4.Landscape());
                        page.Margin(1, Unit.Centimetre);
                        page.PageColor(Colors.White);
                        page.DefaultTextStyle(x => x.FontSize(9));
                        page.DefaultTextStyle(x => x.FontFamily("Tahoma"));
                        page.Header()
                        .Height(7, Unit.Centimetre)
                        .Column(x =>
                        {
                            x.Item().Row(row =>
                            {
                                row.ConstantItem(220).Image(@"wwwroot/images/logo/logots2.png");

                                row.RelativeItem().AlignCenter().Column(column =>
                                {
                                    column.Item().Text(text =>
                                    {
                                        text.Span("CÔNG TY TRÁCH NHIỆM HỮU HẠN TRUNG SƠN ALPHA").FontSize(13).SemiBold().FontColor(Colors.Black);
                                        //text.Span($"{rp.FirstOrDefault().BuyFromVendorName}");
                                    });
                                    column.Spacing(1);
                                    column.Item().Text(text =>
                                    {
                                        text.Span(
                                            "Địa chỉ: Tầng 4 Tòa nhà Trung Sơn, 12 Mai Chí Thọ, KDC 586, Phường Hưng Phú, TP.Cần Thơ")
                                            .FontSize(11).FontColor(Colors.Black);
                                        //text.Span($"{rp.FirstOrDefault().BuyFromVendorName}");
                                    });
                                    column.Spacing(1);
                                    column.Item().Text(text =>
                                    {
                                        text.Span("Mã số thuế: 1801397528").FontSize(13).SemiBold().FontColor(Colors.Black);
                                        //text.Span($"{rp.FirstOrDefault().BuyFromVendorName}");
                                    });
                                    column.Spacing(1);
                                    column.Item().Text(text =>
                                    {
                                        text.Span(
                                            "Thửa đất số 927-1625-1626, tờ bản đồ số 2, Khu vực 1, Phường Cái Răng, Thành phố Cần Thơ")
                                            .FontSize(11).FontColor(Colors.Black);
                                        //text.Span("Địa chỉ nhận hàng: 52A quốc lộ 1A, Phường Ba Láng, Quận Cái Răng, TP.Cần Thơ").FontSize(11).FontColor(Colors.Black);
                                        //text.Span($"{rp.FirstOrDefault().BuyFromVendorName}");
                                    });
                                });

                                //row.ConstantItem(100).Image(@"wwwroot/images/logo/logo-dongwha.png");
                            });

                            x.Spacing(1);

                            x.Item().AlignCenter().Text("THÔNG TIN MUA HÀNG").FontSize(20).Bold();// tiêu đề trang
                            x.Item().AlignCenter().Text($"(#" + rp.First().Number + ")").FontSize(15).SemiBold().FontColor(Colors.Red.Medium);// số chứng từ

                            x.Spacing(1);

                            x.Item().Row(row =>
                            {
                                row.RelativeItem().AlignMiddle().Column(column =>
                                {
                                    column.Item().Text(text =>
                                    {
                                        text.Span("NCC: ").SemiBold().FontSize(13);
                                        text.Span($"{rp.FirstOrDefault().BuyFromVendorName}").FontSize(13);
                                    });

                                    column.Item().Text(text =>
                                    {
                                        text.Span("Chú thích: ").SemiBold().FontSize(13);
                                        text.Span($"{rp.FirstOrDefault().PostingDescription}").FontSize(13);
                                    });

                                    column.Item().Text(text =>
                                    {
                                        text.Span("Ngày tạo: ").SemiBold().FontSize(13);
                                        text.Span($"{rp.FirstOrDefault().PostingDate:d}").FontSize(13);
                                    });
                                });

                                row.RelativeItem().AlignLeft().Column(column =>
                                {
                                    column.Item().Text(text =>
                                    {
                                        text.Span("Người tạo: ").SemiBold();
                                        text.Span($"{rp.FirstOrDefault().BuyerName}").FontSize(13);
                                    });

                                    column.Item().Text(text =>
                                    {
                                        text.Span("Tel: ").SemiBold();
                                        text.Span($"{rp.FirstOrDefault().BuyerPhone}").FontSize(13);
                                    });

                                    column.Item().Text(text =>
                                    {
                                        text.Span("Ngày giao hàng: ").SemiBold().FontSize(13);
                                        text.Span($"{rp.FirstOrDefault().DueDate:d}").FontSize(13);
                                    });
                                });
                            });
                        });

                        const int inchesToPoints = 72;
                        page.Content()
                        .Background(Colors.White)
                        //.Height(20, Unit.Centimetre)
                        .Padding(3)
                        //.MinimalBox()
                        .DefaultTextStyle(x => x.FontSize(8))
                        .DefaultTextStyle(x => x.FontFamily("Tahoma"))
                        .Border(0)
                         .Column(x =>
                         {
                             x.Item().Table(table =>
                              {
                                  table.ColumnsDefinition(columns =>
                                  {
                                      columns.ConstantColumn(30);
                                      //columns.RelativeColumn();
                                      columns.RelativeColumn();
                                      columns.RelativeColumn();
                                      columns.RelativeColumn();
                                      columns.RelativeColumn();
                                      columns.RelativeColumn();
                                      columns.RelativeColumn();
                                      columns.RelativeColumn();
                                      columns.RelativeColumn();
                                      columns.RelativeColumn();
                                      columns.RelativeColumn();
                                      columns.RelativeColumn();
                                  });
                                  table.Header(header =>
                                  {
                                      header.Cell().AlignCenter().Text("#").Bold();
                                      header.Cell().AlignLeft().Text("TS/SKU").Bold();
                                      header.Cell().AlignLeft().Text("Name").Bold();
                                      header.Cell().AlignRight().Text("Số lượng").Bold();
                                      header.Cell().AlignRight().Text("Đơn vị").Bold();
                                      header.Cell().AlignRight().Text("Giá").Bold();
                                      header.Cell().AlignRight().Text("VAT(%)").Bold();
                                      header.Cell().AlignRight().Text("Giảm giá(-VAT)").Bold();
                                      header.Cell().AlignRight().Text("Thành tiền(-VAT)").Bold();
                                      header.Cell().AlignRight().Text("(+VAT)").Bold();
                                      header.Cell().AlignRight().Text("Tổng tiền(+VAT)").Bold();
                                      header.Cell().AlignCenter().Text("Chú thích").Bold();
                                      header.Cell().ColumnSpan(12).PaddingTop(4).BorderBottom(1).BorderColor(Colors.Black);
                                  });
                                  foreach (var report in rp.ToList())
                                  {
                                      decimal amountWithVat = report.Vat / 100 * report.Amount;

                                      table.Cell().Element(CellStyle).AlignCenter().AlignMiddle()
                                          .Text($"{rp.ToList().IndexOf(report) + 1}");
                                      table.Cell().Element(CellStyle).AlignLeft().AlignMiddle().Text(report.ItemNumber);
                                      table.Cell().Element(CellStyle).AlignLeft().AlignMiddle().Text(report.ItemName);
                                      table.Cell().Element(CellStyle).AlignRight().AlignMiddle().Text($"{report.Quantity}");
                                      table.Cell().Element(CellStyle).AlignRight().AlignMiddle().Text(report.UOM);
                                      table.Cell().Element(CellStyle).AlignRight().AlignMiddle().Text($"{report.UnitCost:n0}");
                                      table.Cell().Element(CellStyle).AlignRight().AlignMiddle().Text($"{report.Vat:n0}");
                                      table.Cell().Element(CellStyle).AlignRight().AlignMiddle()
                                          .Text($"{report.LineDiscountAmount:n0}");
                                      table.Cell().Element(CellStyle).AlignRight().AlignMiddle().Text($"{report.Amount:n0}");
                                      table.Cell().Element(CellStyle).AlignRight().AlignMiddle().Text($"{amountWithVat:n0}");
                                      table.Cell().Element(CellStyle).AlignRight().AlignMiddle()
                                          .Text($"{report.AmountIncludingVat:n0}");
                                      table.Cell().Element(CellStyle).AlignCenter().AlignMiddle().Text(report.LineDescription);
                                      static IContainer CellStyle(IContainer container) => container.BorderBottom(1).BorderColor(Colors.Grey.Lighten2).PaddingVertical(5);
                                  }
                                  table.Footer(col =>
                                  {
                                      //var countItem = rp.ToList().Count();
                                      //col.Cell().ColumnSpan(2).AlignRight().Text($"{countItem}").FontSize(7).Bold();
                                      var totalDiscountVAT = rp.Sum(x => x.LineDiscountAmount);
                                      var totalAmountNoVAT = rp.Sum(x => x.Amount);
                                      var totalAmountVAT = rp.Sum(x => x.Vat / 100 * x.Amount);
                                      var totalAmount = rp.Sum(x => x.AmountIncludingVat);
                                      col.Cell().ColumnSpan(7)
                                          .PaddingTop(10)
                                          .AlignRight()
                                          .Text("Tổng cộng:")
                                          .FontSize(7)
                                          .Bold();
                                      col.Cell().PaddingTop(10).AlignRight().Text($"{totalDiscountVAT:C}").FontSize(7).Bold();
                                      col.Cell().PaddingTop(10).AlignRight().Text($"{totalAmountNoVAT:C}").FontSize(7).Bold();
                                      col.Cell().PaddingTop(10).AlignRight().Text($"{totalAmountVAT:C}").FontSize(7).Bold();
                                      col.Cell().PaddingTop(10).AlignRight().Text($"{totalAmount:C}").FontSize(7).Bold();
                                  });
                              });
                             //var totalPrice = rq.Sum(x => x.UnitPrice * x.Quantity);
                             //x.Item().PaddingRight(5).AlignRight().Text($"Grand total: {totalPrice:C}").SemiBold();
                             //x.Item().PaddingRight(5).AlignRight().Text($"Buyer: {rp.FirstOrDefault().PurchaserCode}").FontSize(12).SemiBold().FontColor(Colors.Grey.Medium);
                         });
                        page.Footer()
                        .Background(Colors.Grey.Lighten3)
                        .Height(1, Unit.Centimetre)
                        .AlignMiddle()
                        .Column(x =>
                        {
                            x.Item().Row(row =>
                            {
                                row.RelativeItem().AlignRight().Padding(2).ContentFromRightToLeft()
                                .Text(x =>
                                {
                                    x.CurrentPageNumber().FontSize(9);
                                    x.Span("/");
                                    x.TotalPages().FontSize(9);
                                });
                            }
                            );
                        });
                    });
                }).GeneratePdf();

            return pdf;
        }
        catch
        {
            return null;
        }
    }

}
