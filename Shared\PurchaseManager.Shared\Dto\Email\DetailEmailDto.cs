﻿using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto.Db;
namespace PurchaseManager.Shared.Dto.Email;

public class DetailEmailDto
{
    public long Id { get; set; }
    public string ToEmail { get; set; } = null!;
    public string CcEmail { get; set; } = null!;
    public string BccEmail { get; set; } = null!;
    public string Subject { get; set; } = null!;
    public string Body { get; set; } = null!;
    public string Email { get; set; }
    public EmailType EmailType { get; set; }
    public DateTime CreatedOn { get; set; }
    public DateTime? SentOn { get; set; }
    public int EmailTemplatesId { get; set; }
    public virtual DetailEmailTemplateAfterDetailEmailDto EmailsNavigation { get; set; } = null!;
}
public class DetailEmailAfterDetailEmailTemplateDto
{
    public int EmailId { get; set; }
    public string ToEmail { get; set; } = null!;
    public string Subject { get; set; } = null!;
    public string Body { get; set; } = null!;
    public DateTime SentDateTime { get; set; }
}
