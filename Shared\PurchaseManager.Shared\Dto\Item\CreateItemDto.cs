﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PurchaseManager.Shared.Dto.Item;

public class CreateItemDto
{
    [StringLength(160)]
    public string Name { get; set; } = null!;

    [StringLength(2000)]
    public string Description { get; set; } = null!;

    [StringLength(100)]
    public string BaseUnitOfMeasure { get; set; } = null!;

    [StringLength(100)]
    public string? InventoryPostingGroup { get; set; }

    [StringLength(100)]
    public string? ItemCategoryCode { get; set; }

    [Column("VATProductPostingGroup")]
    [StringLength(100)]

    public string? VatProductPostingGroup { get; set; }

    [StringLength(100)]
    public string? ItemDiscountGroup { get; set; }

    [StringLength(100)]
    public string? ManufacturerCode { get; set; }

    [StringLength(100)]
    public string? CountryOfOriginCode { get; set; }

    [StringLength(100)]
    public string? SalaryGroup { get; set; }

    [StringLength(100)]
    public string? TaxGroupCode { get; set; }

    [StringLength(100)]
    public string? CommissionGroup { get; set; }

    public int StatisticsGroup { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal LotSize { get; set; }

    public int AllowInvoiceDiscount { get; set; }

    public int PriceProfitCalculation { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal ProfitPercent { get; set; }

    public int CostingMethod { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal UnitPrice { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal UnitCost { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal StandardCost { get; set; }

    [StringLength(64)]
    public string? LeadTimeCalculation { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal ReorderPoint { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal MaximumInventory { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal ReorderQuantity { get; set; }

    [Column("AlternativeItemNo_")]
    [StringLength(100)]

    public string? AlternativeItemNo { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal GrossWeight { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal NetWeight { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal UnitsPerParcel { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal UnitVolume { get; set; }

    [StringLength(100)]
    public string? VendorNumber { get; set; }

    [StringLength(100)]
    public string? VendorItemNumber { get; set; }

    [StringLength(100)]
    public string? FreightType { get; set; }

    [StringLength(100)]
    public string? TariffNumber { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal BudgetQuantity { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal BudgetedAmount { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal BudgetProfit { get; set; }

    public int Blocked { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal MinimumOrderQuantity { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal MaximumOrderQuantity { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal SafetyStockQuantity { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal OrderMultiple { get; set; }

    [StringLength(64)]
    public string? SafetyLeadTime { get; set; }

    [StringLength(100)]
    public string? SalesUnitOfMeasure { get; set; }

    [StringLength(100)]
    public string? PurchaseUnitOfMeasure { get; set; }

    public int ManufacturingPolicy { get; set; }

    [StringLength(64)]
    public string? ExpirationCalculation { get; set; }

    public int NonStock { get; set; }

    [StringLength(100)]
    public string? QuotaNumber { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal QuotaQuantity { get; set; }

    [StringLength(100)]
    public string? QuotaAddedNumber { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal QuotaAddedQuantity { get; set; }

    [StringLength(100)]
    public string? ImportLicenseNumber { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal ImportLicenseQuantity { get; set; }

    public int QuotaRequest { get; set; }

    [StringLength(100)]
    public string? Name2 { get; set; }

    [StringLength(100)]
    public string? Name3 { get; set; }

    public DateOnly? VisaIssuedDate { get; set; }

    public DateOnly? ExpirationDate { get; set; }

    [StringLength(100)]
    public string? Standard { get; set; }

    [StringLength(50)]
    public string? VendorAuthorizationNumber { get; set; }

    [StringLength(50)]
    public string? CompanyRegistration { get; set; }

    [StringLength(50)]
    public string? QualityMeasureCode { get; set; }

    [StringLength(50)]
    public string? SearchName { get; set; }

    [StringLength(50)]
    public string? VisaIssuedNumber { get; set; }

    [StringLength(50)]
    public string? PlaceOfOriginCode { get; set; }

    [StringLength(500)]
    public string? BinCode { get; set; }

    public int Status { get; set; }

    public int Type { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal MinimumInventory { get; set; }

    [StringLength(100)]
    public string? CategoryNo { get; set; }

    [StringLength(100)]
    public string? CategoryTypeNo { get; set; }

    [StringLength(100)]
    public string? CategoryGroupNo { get; set; }

    [StringLength(100)]
    public string? Importer { get; set; }

    [StringLength(100)]
    public string? Visa { get; set; }

    [StringLength(100)]
    public string? Label { get; set; }

    [StringLength(100)]
    public string? ItemLocation { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal UnitPriceRegis { get; set; }

    [StringLength(100)]
    public string? MainIngredient { get; set; }

    [StringLength(100)]
    public string? RegistrationNo { get; set; }

    public bool Visible { get; set; }

    public bool? Hidden { get; set; }
    public List<CreateSalesPriceDto>? SalesPrices { get; set; } = new List<CreateSalesPriceDto>();
    public List<CreateItemUnitOfMeasureDto>? UnitOfMeasureDtos { get; set; } = new List<CreateItemUnitOfMeasureDto>();
}
