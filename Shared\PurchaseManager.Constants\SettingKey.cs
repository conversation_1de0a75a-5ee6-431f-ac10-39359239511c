﻿namespace PurchaseManager.Constants
{
    public enum SettingKey
    {
        MainConfiguration_Runtime,
        MainConfiguration_Theme,

        EmailConfiguration_SmtpServer,
        EmailConfiguration_SmtpPort,
        EmailConfiguration_SmtpUsername,
        EmailConfiguration_SmtpPassword,
        EmailConfiguration_SmtpUseSSL,
        EmailConfiguration_SmtpSslProtocol,

        EmailConfiguration_FromName,
        EmailConfiguration_FromAddress,
        EmailConfiguration_ReplyToAddress,

        EmailConfiguration_PopServer,
        EmailConfiguration_PopPort,
        EmailConfiguration_PopUsername,
        EmailConfiguration_PopPassword,
        EmailConfiguration_PopUseSSL,

        EmailConfiguration_ImapServer,
        EmailConfiguration_ImapPort,
        EmailConfiguration_ImapUsername,
        EmailConfiguration_ImapPassword,
        EmailConfiguration_ImapUseSSL,
    }
}
