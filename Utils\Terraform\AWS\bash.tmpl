#!/bin/bash
sudo dd if=/dev/zero of=/swapfile bs=1M count=2048
sudo mkswap /swapfile
sudo chmod 600 /swapfile
sudo swapon /swapfile
if ! grep -q 'init-poky' /etc/fstab ; then
    sudo echo '# init-poky' >> /etc/fstab
    sudo echo '/swapfile swap swap defaults 0 0' >> /etc/fstab
fi
sudo apt update -y
sudo apt-get install apt-transport-https ca-certificates curl gnupg lsb-release -y
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update -y
sudo apt-get install docker-ce docker-ce-cli containerd.io docker-compose -y
echo "{\"log-driver\": \"json-file\",\"log-opts\": {\"max-size\": \"20m\", \"max-file\": \"3\"}}" > /etc/docker/daemon.json
sudo systemctl enable docker
sudo systemctl start docker
echo "${docker_compose}" > /opt/docker-compose.yml
sudo docker-compose -f /opt/docker-compose.yml up -d
