﻿using System.Data;
using System.Diagnostics;
using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Constants.PurchaseSuggestedPayment;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using Serilog;
namespace PurchaseManager.Server.Managers;

public partial class PurchaseSuggestedPaymentManager
{
    private PurchaseSuggestedPaymentHeader CreateHeader(string documentNumber, string storeId)
    {
        var header = new PurchaseSuggestedPaymentHeader
        {
            DocumentType = 0,
            Number = documentNumber,
            OrderDate = DateTime.Now,
            PostingDate = DateTime.Now,
            ExpectedReceiptDate = DateTime.Now,
            DueDate = DateTime.Now,
            DocumentDate = DateTime.Now,
            Status = (int)PurchaseSuggestedPaymentEnum.New,
            SourceCode = storeId,
            DateReceived = DateTime.Now,
            DateSent = DateTime.Now,
            AccountNumber = "",// todo:
            AppliesToDocNumber = "",
            IssueDate = DateTime.Now,
            PmtDiscountDate = DateTime.Now,
            PromisedReceiptDate = DateTime.Now,
            RequestedReceiptDate = DateTime.Now,
            TimeReceived = DateTime.Now,
            TimeSent = DateTime.Today,
            LoginId = _adminManager.GetUserLogin()
        };
        return header;
    }

    private PurchaseSuggestedPaymentLine CreateLines(string documentNumber, int index, DemandItemInfo item, string storeId,
        string vatProductPostingGroup, string vendorNumber)
    {
        var line = new PurchaseSuggestedPaymentLine
        {
            DocumentType = 0,
            DocumentNumber = documentNumber,
            LineNumber = index + 1,
            Type = 0,
            Number = item.ItemNo,
            LocationCode = storeId,
            UnitOfMeasure = item.UnitOfMeasure,
            Quantity = item.Quantity,
            Vat = vatProductPostingGroup,
            Status = (int)PurchaseSuggestedPaymentEnum.Save,
            QtyPerUnitOfMeasure = 1,
            ExpirationDate = DateOnly.FromDateTime(DateTime.Now),
            LotNumber = "notset",
            ExpectedReceiptDate = DateTime.Now,
            IssueDate = DateTime.Now,
            OrderDate = DateTime.Now,
            PlannedReceiptDate = DateTime.Now,
            PromisedReceiptDate = DateTime.Now,
            RequestedReceiptDate = DateTime.Now,
            ReasonCode = null,
            BuyFromVendorNumber = vendorNumber,
            Tags = item.Tags
        };
        return line;
    }
    private static int GetIntValue(DataRow row, int index)
        => row.ItemArray.Length > index && int.TryParse(row.ItemArray[index]?.ToString(), out var value) ? value : 0;

    private static string GetStringValue(DataRow row, int index) => row.ItemArray.Length > index && row.ItemArray[index] != null
        ? row.ItemArray[index].ToString() : string.Empty;

    private async Task<ApiResponse> CreatePurchaseSuggestedPaymentV2Async(List<CreatePOFromInputDemandDto> createPOFromInputDemand)
    {
        var s = new Stopwatch();
        s.Start();
        await using var transaction = await _context.Database.BeginTransactionAsync();
        Console.WriteLine("Start at " + DateTime.Now);

        try
        {
            var itemNumbers = createPOFromInputDemand
                .SelectMany(d => d.ListDemand.Select(i => i.ItemNo.Trim()))
                .Distinct()
                .ToList();

            var listItems = await _context.Items
                .Where(i => itemNumbers.Contains(i.Number.Trim()))
                .AsNoTracking()
                .ToDictionaryAsync(
                keySelector: i => i.Number.Trim(),
                elementSelector: i => new
                {
                    i.VatproductPostingGroup, i.VendorNumber
                });

            Console.WriteLine("Start creating headers - time(ms): " + s.ElapsedMilliseconds);

            const int batchSize = 5000;
            var totalLinesInserted = 0;
            var totalDocumentsCreated = 0;

            var allHeaders = new List<PurchaseSuggestedPaymentHeader>();
            var allBatchLines = new List<PurchaseSuggestedPaymentLine>();

            foreach (var demand in createPOFromInputDemand)
            {
                var documentNumber = await _adminManager.CreateNumberSeries(Business, Branch);
                totalDocumentsCreated++;
                Console.WriteLine($"Document ID {totalDocumentsCreated}: {documentNumber}");

                var header = CreateHeader(documentNumber, demand.StoreId);
                allHeaders.Add(header);

                foreach (var (item, index) in demand.ListDemand.Select((v, i) => (v, i)))
                {
                    if (!listItems.TryGetValue(item.ItemNo.Trim(), out var result))
                    {
                        Console.WriteLine("Item not found: " + item.ItemNo);
                        continue;
                    }

                    var line = CreateLines(
                    documentNumber, index, item, demand.StoreId, result.VatproductPostingGroup, result.VendorNumber);

                    allBatchLines.Add(line);

                    if (allBatchLines.Count < batchSize)
                    {
                        continue;
                    }
                    await BulkInsertLinesAsync(allBatchLines);
                    totalLinesInserted += allBatchLines.Count;
                    Console.WriteLine(
                    $"Inserted {totalLinesInserted} lines at {DateTime.Now} - Time: {s.Elapsed.Minutes}m {s.Elapsed.Seconds}s {s.Elapsed.Milliseconds}ms");
                    allBatchLines.Clear();
                }
            }

            // Insert all headers at once
            if (allHeaders.Count != 0)
            {
                await _context.PurchaseSuggestedPaymentHeaders.AddRangeAsync(allHeaders);
                await _context.SaveChangesAsync();
            }

            // Insert remaining lines
            if (allBatchLines.Count != 0)
            {
                await BulkInsertLinesAsync(allBatchLines);
                totalLinesInserted += allBatchLines.Count;
                Console.WriteLine(
                $"Inserted {totalLinesInserted} lines at {DateTime.Now} - Time: {s.Elapsed.Minutes}m {s.Elapsed.Seconds}s {s.Elapsed.Milliseconds}ms");
            }

            await transaction.CommitAsync();
            s.Stop();
            Console.WriteLine(
            $"Complete - Total Documents Created: {totalDocumentsCreated}, Total Lines Inserted: {totalLinesInserted}, Total Time (ms): {s.ElapsedMilliseconds}");
            return new ApiResponse(201, "Created successfully");
        }
        catch (Exception e)
        {
            Log.Error(e.GetBaseException().Message);
            Console.WriteLine("Error at " + DateTime.Now + ": " + e);
            await transaction.RollbackAsync();
            return new ApiResponse(500, "Error creating record");
        }
    }


    // Phương thức hỗ trợ Bulk Insert
    private async Task BulkInsertLinesAsync(List<PurchaseSuggestedPaymentLine> lines) => await _context.BulkInsertAsync(lines,
    new BulkConfig
    {
        BatchSize = 5000, PreserveInsertOrder = true, SetOutputIdentity = true
    });
}
