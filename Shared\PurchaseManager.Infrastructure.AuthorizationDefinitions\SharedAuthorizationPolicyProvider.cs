﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;

namespace PurchaseManager.Infrastructure.AuthorizationDefinitions;

public class SharedAuthorizationPolicyProvider : DefaultAuthorizationPolicyProvider
{
    private readonly AuthorizationOptions _options;

    protected SharedAuthorizationPolicyProvider(IOptions<AuthorizationOptions> options) : base(options)
    {
        _options = options.Value;
    }

    public override async Task<AuthorizationPolicy> GetPolicyAsync(string policyName)
    {
        var policy = await base.GetPolicyAsync(policyName);

        if (policy != null)
        {
            return policy;
        }
        var created = false;
        switch (policyName)
        {
            //In DatabaseInitializer: await _userManager.AddClaimAsync(applicationUser, new Claim($"Is{role}", ClaimValues.trueString));
            case Policies.IsAdmin:
                policy = new AuthorizationPolicyBuilder()
                    .Combine(await GetPolicyAsync(Policies.IsUser))
                    .Combine(await GetPolicyAsync(Policies.IsVendor))
                    // .Combine(await GetPolicyAsync(Policies.IsVendorContact))
                    // .Combine(await GetPolicyAsync(Policies.IsPurchaseUser))
                    // .Combine(await GetPolicyAsync(Policies.IsWarehouseUser))
                    .Combine(await GetPolicyAsync(Policies.IsPurchaseManager))
                    .Combine(await GetPolicyAsync(Policies.IsWarehouseManager))
                    .Combine(await GetPolicyAsync(Policies.IsTSUser))
                    .RequireClaim(Policies.IsAdmin)
                    .Build();

                created = true;
                break;

            case Policies.IsUser:
                policy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .AddRequirements(new EmailVerifiedRequirement(true))
                    .Build();

                created = true;
                break;

            case Policies.IsVendorContact:
                policy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .RequireClaim(Policies.IsVendorContact)
                    .Build();

                created = true;
                break;

            case Policies.IsVendor:
                policy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .RequireClaim(Policies.IsVendor)
                    .Build();
                created = true;
                break;


            case Policies.IsPurchaseUser:
                policy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .RequireClaim(Policies.IsPurchaseUser)
                    .Build();

                created = true;
                break;

            case Policies.IsPurchaseManager:
                policy = new AuthorizationPolicyBuilder()
                    .Combine(await GetPolicyAsync(Policies.IsPurchaseUser))
                    .RequireAuthenticatedUser()
                    .RequireClaim(Policies.IsPurchaseManager)
                    .Build();

                created = true;
                break;

            case Policies.IsWarehouseManager:
                policy = new AuthorizationPolicyBuilder()
                    .Combine(await GetPolicyAsync(Policies.IsWarehouseUser))
                    .RequireAuthenticatedUser()
                    .RequireClaim(Policies.IsWarehouseManager)
                    .Build();

                created = true;
                break;

            case Policies.IsWarehouseUser:
                policy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .RequireClaim(Policies.IsWarehouseUser)
                    .Build();
                created = true;
                break;

            case Policies.IsTSUser:
                policy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .RequireClaim(Policies.IsPurchaseUser)
                    .RequireClaim(Policies.IsWarehouseUser)
                    .Build();
                created = true;
                break;

            case Policies.IsVendorAndPurchaseUser:
                policy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .RequireClaim(Policies.IsPurchaseUser)
                    .RequireClaim(Policies.IsVendor)
                    .Build();
                created = true;
                break;


            case Policies.IsMKT:
                policy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .RequireClaim(Policies.IsMKT)
                    .Build();
                created = true;
                break;

            //https://docs.microsoft.com/it-it/aspnet/core/security/authentication/mfa
            case Policies.TwoFactorEnabled:
                policy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .RequireClaim("amr", "mfa")
                    .Build();

                created = true;
                break;

            case Policies.IsMyEmailDomain:
                policy = new AuthorizationPolicyBuilder()
                    .RequireAuthenticatedUser()
                    .AddRequirements(new DomainRequirement("purchasemanager.com"))
                    .Build();

                created = true;
                break;
        }

        if (created)
        {
            _options.AddPolicy(policyName, policy);
        }

        return policy;
    }
}
