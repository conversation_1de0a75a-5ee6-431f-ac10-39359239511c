﻿@inherits EmailPage
@page "/admin/settings/email"
@layout AdminLayout
<PageTitle>@L["EmailSettings"]</PageTitle>
@if (settings == null)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    <MudStack Row Reverse Class="my-2">
        <MudButton ButtonType="ButtonType.Submit" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.List" OnClick="@(() => navigationManager.NavigateTo("/admin/settings/email/email-templates"))" Color="Color.Primary">@L["List Template"]</MudButton>
        <MudButton ButtonType="ButtonType.Submit" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Send" OnClick="@(() => navigationManager.NavigateTo("/admin/settings/email/send-email"))" Color="Color.Info">@L["Send"]</MudButton>
        <MudButton ButtonType="ButtonType.Submit" Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Send" OnClick="@(() => navigationManager.NavigateTo("/admin/settings/email/sent-email"))" Color="Color.Info">@L["List Sent Email"]</MudButton>
    </MudStack>
    <EditForm Model="@settings">
        <FluentValidationValidator />
        <MudValidationSummary />
        <MudText Typo="Typo.h4">@L["OutgoingEmail"]</MudText>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_FromAddress].Value" Label="@L["SenderEmail"]" AdornmentIcon="@Icons.Material.Filled.AlternateEmail" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_FromName].Value" Label="@L["SenderName"]" AdornmentIcon="@Icons.Material.Filled.Person" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_SmtpServer].Value" Label="@L["SmtpServer"]" AdornmentIcon="@Icons.Material.Filled.Dns" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_SmtpPort].ValueAsInt" Label="@L["Port"]" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_SmtpUsername].Value" Label="@L["UserName"]" AdornmentIcon="@Icons.Material.Filled.Person" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_SmtpPassword].Value" Label="@L["Password"]" InputType="InputType.Password" AdornmentIcon="@Icons.Material.Outlined.Lock" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudSwitch T="bool" @bind-Value="@settings[SettingKey.EmailConfiguration_SmtpUseSSL].ValueAsBool" Label="SSL" Color="Color.Primary" />
        @if (@settings[SettingKey.EmailConfiguration_SmtpUseSSL].ValueAsBool)
        {
            <MudSelect @bind-Value="@settings[SettingKey.EmailConfiguration_SmtpSslProtocol].Value" Label="SSL Protocol">
                @foreach (var item in sslProtocols)
                {
                    <MudSelectItem Value="@item">@item</MudSelectItem>
                }
            </MudSelect>
        }
        <MudDivider Class="my-4" />
        <MudText Typo="Typo.h4">@L["IncomingEmail"]</MudText>
        <MudText Typo="Typo.h4">POP3</MudText>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_PopServer].Value" Label="@L["PopServer"]" AdornmentIcon="@Icons.Material.Filled.Dns" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_PopPort].ValueAsInt" Label="@L["Port"]" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_PopUsername].Value" Label="@L["UserName"]" AdornmentIcon="@Icons.Material.Filled.Person" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_PopPassword].Value" Label="@L["Password"]" InputType="InputType.Password" AdornmentIcon="@Icons.Material.Outlined.Lock" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudSwitch T="bool" @bind-Value="@settings[SettingKey.EmailConfiguration_PopUseSSL].ValueAsBool" Label="SSL" Color="Color.Primary" />
        <MudDivider Class="my-4" />
        <MudText Typo="Typo.h4">IMAP</MudText>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_ImapServer].Value" Label="@L["ImapServer"]" AdornmentIcon="@Icons.Material.Filled.Dns" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_ImapPort].ValueAsInt" Label="@L["Port"]" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_ImapUsername].Value" Label="@L["UserName"]" AdornmentIcon="@Icons.Material.Filled.Person" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudTextField @bind-Value="@settings[SettingKey.EmailConfiguration_ImapPassword].Value" Label="@L["Password"]" InputType="InputType.Password" AdornmentIcon="@Icons.Material.Outlined.Lock" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
        <MudSwitch T="bool" @bind-Value="@settings[SettingKey.EmailConfiguration_ImapUseSSL].ValueAsBool" Label="SSL" Color="Color.Primary" />
    </EditForm>
    <MudButton OnClick="@(()=>apiClient.CancelChanges())">@L["Cancel"]</MudButton>
    <MudButton OnClick="@SaveChanges" Variant="Variant.Filled" Color="Color.Primary">@L["Save"]</MudButton>
    <MudButton OnClick="@(e => { isSendEmailDialogOpen = true; })">@L["Send test email"]</MudButton>
    <MudDialog @bind-Visible="@isSendEmailDialogOpen">
        <TitleContent>
            <MudText Typo="Typo.h6">
                <MudIcon Icon="@Icons.Material.Filled.Send" Class="mr-3 mb-n1" />
                @L["Send test email"]
            </MudText>
        </TitleContent>
        <DialogContent>
            <EditForm id="emailForm" Model="@email" OnValidSubmit="@SendTestEmail">
                <FluentValidationValidator />
                <MudValidationSummary />
                @* <MudTextField @bind-Value="@email.ToAddress" Label=@L["Email"] AdornmentIcon="@Icons.Material.Outlined.Email" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField> *@
                <MudSwitch T="bool" @bind-Value="@email.Queued" Label=@L["Queued"] Color="Color.Primary" />
            </EditForm>
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="@(e => { isSendEmailDialogOpen = false; })">@L["Cancel"]</MudButton>
            <MudButton ButtonType="ButtonType.Submit" form="emailForm" Variant="Variant.Filled" Color="Color.Primary">@L["Send"]</MudButton>
        </DialogActions>
    </MudDialog>
}
@code {
    [Inject] NavigationManager navigationManager { get; set; }
}
