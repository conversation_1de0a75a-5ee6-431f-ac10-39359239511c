﻿using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.AspNetCore.Razor.TagHelpers;
using Microsoft.Extensions.DependencyInjection;

using MudBlazor;
using MudBlazor.Extensions;
using MudBlazor.Services;

using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Theme.Material.Services;
using PurchaseManager.Theme.Material.TagHelpers;

namespace PurchaseManager.Theme.Material
{
    public class Module : IModule, ITheme
    {
        public static readonly string ContentPath = $"_content/{typeof(Module).Namespace.Replace("Material", "MudBlazor")}";
        public static readonly string Path = typeof(Module).Namespace.Replace("Material", "MudBlazor");
        public Module()
        {
            RootComponentMapping = new RootComponentMapping(typeof(App), "app");
        }

        public RootComponentMapping RootComponentMapping { get; }

        public string Name => "MudBlazor theme";

        public string Description => "MudBlazor theme";

        public int Order => 1;

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddTransient<ITagHelperComponent, ThemeTagHelperComponent>();
            services.AddTransient<ITagHelperComponent, AppTagHelperComponent>();

            services.AddAuthorizationCore();
            services.AddAuthenticationCore();
            services.AddMudServices(config =>
            {
                config.SnackbarConfiguration.PositionClass = Defaults.Classes.Position.BottomRight;

                config.SnackbarConfiguration.PreventDuplicates = true;
                config.SnackbarConfiguration.NewestOnTop = true;
                config.SnackbarConfiguration.ShowCloseIcon = true;
                config.SnackbarConfiguration.VisibleStateDuration = 10000;
                config.SnackbarConfiguration.HideTransitionDuration = 500;
                config.SnackbarConfiguration.ShowTransitionDuration = 500;
                config.SnackbarConfiguration.SnackbarVariant = Variant.Filled;
            });
            
            //MudBlaor Extentions
            services.AddMudExtensions();
            services.AddScoped<IViewNotifier, ViewNotifier>();
            // use this to add MudServices and the MudBlazor.Extensions
            services.AddMudServicesWithExtensions();

            // or this to add only the MudBlazor.Extensions but please ensure that this is added after mud servicdes are added. That means after `AddMudServices`
            services.AddMudExtensions();

        }

        public void ConfigureWebAssemblyServices(IServiceCollection services)
        {
            services.AddMudServices(config =>
            {
                config.SnackbarConfiguration.PositionClass = Defaults.Classes.Position.BottomLeft;

                config.SnackbarConfiguration.PreventDuplicates = false;
                config.SnackbarConfiguration.NewestOnTop = false;
                config.SnackbarConfiguration.ShowCloseIcon = true;
                config.SnackbarConfiguration.VisibleStateDuration = 10000;
                config.SnackbarConfiguration.HideTransitionDuration = 500;
                config.SnackbarConfiguration.ShowTransitionDuration = 500;
                config.SnackbarConfiguration.SnackbarVariant = Variant.Filled;
            });

            services.AddScoped<IViewNotifier, ViewNotifier>();
            // #region Automapper
            // //Automapper to map DTO to Models https://www.c-sharpcorner.com/UploadFile/1492b1/crud-operations-using-automapper-in-mvc-application/
            // var automapperConfig = new MapperConfiguration(configuration =>
            // {
            //     configuration.AddProfile(new MappingProfile());
            //     configuration.AddProfile(new ItemMappingProfile());
            // });

            // var autoMapper = automapperConfig.CreateMapper();

            // services.AddSingleton(autoMapper);
            // #endregion


        }
        public void ConfigureWebAssemblyHost(WebAssemblyHost webAssemblyHost)
        {
        }
    }
}
