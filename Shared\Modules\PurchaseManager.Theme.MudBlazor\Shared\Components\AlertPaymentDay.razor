@page "/test"
@inherits AlertPaymentDayBase
<MudTable Items="@Parameters" Hover="true" Outlined="false" Elevation="0" LoadingProgressColor="Color.Info">
    <HeaderContent>
        <MudTh>@L["VendorName"] / @L["VendorNumber"]</MudTh>
        <MudTh>@L["PONumber"]</MudTh>
        <MudTh>@L["RemainingDays"]</MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd DataLabel="VendorName">
            <MudText Typo="Typo.body1">@context.VendorName</MudText>
            <MudText Typo="Typo.caption"> @context.VendorNumber</MudText>
        </MudTd>
        <MudTd DataLabel="PONumber">@context.PONumber</MudTd>
        <MudTd DataLabel="RemainingDays">
            <MudText Typo="Typo.body1">@context.RemainingDays day left</MudText>
            <MudText Typo="Typo.caption"> (@context.ValidDate.ToString("ddd, dd/MM/yyyy"))</MudText>
        </MudTd>
    </RowTemplate>
</MudTable>