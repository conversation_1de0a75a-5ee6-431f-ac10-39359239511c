@* @attribute [Authorize(Policy = Policies.IsVendor)] *@
@inherits AddVendorItemPageBase
@using System.ComponentModel.DataAnnotations

<MudPaper Elevation="0">
    <MudOverlay ZIndex="99999" Visible="isLoading" LightBackground Absolute>
        <MudProgressCircular Color="Color.Primary" Indeterminate />
    </MudOverlay>
    <EditForm Model="@createVendorItemDto" OnValidSubmit="OnCreateNewVendorItemSubmit">
        <DataAnnotationsValidator />
        <MudCard>
            <MudCardContent>
                <MudGrid>
                    <MudItem sm="12">
                        <MudTextField Label="Name" @bind-Value="createVendorItemDto.Name"
                            For="@(() => createVendorItemDto.Name)" />
                    </MudItem>
                    <MudItem sm="12">
                        <MudTextField Label="ItemNumber" @bind-Value="createVendorItemDto.ItemNumber"
                            For="@(() => createVendorItemDto.ItemNumber)" />
                    </MudItem>
                    @* <MudItem sm="4">
                        <MudTextField Label="Price" @bind-Value="createVendorItemDto.Price"
                            For="@(() => createVendorItemDto.Price)" />
                    </MudItem> *@
                    @* <MudItem sm="12">
                        <MudTextField Label="Vat" Required @bind-Value="createVendorItemDto.Vat"
                            For="@(() => createVendorItemDto.Vat)" />
                    </MudItem> *@
                    <MudItem sm="12">
                        <MudSelect T="string" @bind-Value="@createVendorItemDto.UnitOfMeasure" Required
                            For="@(() => createVendorItemDto.UnitOfMeasure)" Label="Unit Of Measure"
                            AnchorOrigin="Origin.BottomCenter" Clearable>
                            @foreach (var o in listUnitOfMeasureDto)
                            {
                                <MudSelectItem Value="@o.Code" />
                            }
                        </MudSelect>
                    </MudItem>
                </MudGrid>
                <MudDivider />
                <MudStack Row Justify="Justify.FlexEnd" Class="mt-3">
                    <MudButton ButtonType="ButtonType.Submit" Variant="Variant.Filled" Color="Color.Primary"
                        Class="ml-auto">
                        @L["Add"]
                    </MudButton>
                </MudStack>
            </MudCardContent>
        </MudCard>
    </EditForm>
</MudPaper>