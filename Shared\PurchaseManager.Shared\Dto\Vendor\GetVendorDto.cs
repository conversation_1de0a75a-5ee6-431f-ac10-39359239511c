using System.ComponentModel.DataAnnotations.Schema;

namespace PurchaseManager.Shared.Dto.Vendor;

public class GetVendorDto
{
    public int RowId { get; set; }

    public string Number { get; set; }

    public string Name { get; set; }

    public string SearchName { get; set; }

    public string Address { get; set; }

    public string City { get; set; }

    public string Contact { get; set; }

    public string Phone { get; set; }

    public string Telex { get; set; }

    public string VendorPostingGroup { get; set; }

    public string CurrencyCode { get; set; }

    public string PurchaserCode { get; set; }

    public string ShipmentMethodCode { get; set; }

    public string ShippingAgentCode { get; set; }

    public string CountryCode { get; set; }

    public int Blocked { get; set; }

    public string PayToVendorNumber { get; set; }

    public string PaymentMethodCode { get; set; }

    public DateTime LastDateModified { get; set; }

    public string Fax { get; set; }

    public string VatRegistrationNumber { get; set; }

    public string PostCode { get; set; }

    public string County { get; set; }

    public string Email { get; set; }

    public string HomePage { get; set; }

    public string VatBusinessPostingGroup { get; set; }

    public string? LoginId { get; set; }

    public int? VendorGroup { get; set; }

    [NotMapped]
    public DateOnly? CreateDate { get; set; }

    public string? ImportLicense { get; set; }

    public string? CertificateOfRegistration { get; set; }

    [NotMapped]
    public DateOnly? RegistrationOfExpirationDate { get; set; }

    public string? LicenseNumber { get; set; }

    public DateTime? LicenseDate { get; set; }

    public DateTime? ExprireLicenseDate { get; set; }

    public string? LastUserModified { get; set; }

    public int? Internal { get; set; }

    public string? Description { get; set; }

    public int Status { get; set; }

    public DateTime? LastModifiedTime { get; set; }
}
