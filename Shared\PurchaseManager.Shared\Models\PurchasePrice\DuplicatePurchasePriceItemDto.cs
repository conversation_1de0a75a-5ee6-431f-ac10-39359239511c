﻿using PurchaseManager.Shared.Dto.PurchasePrices;
namespace PurchaseManager.Shared.Models.PurchasePrice;

public class DuplicatePurchasePriceItemDto
{
    public string VendorNumber { get; set; }
    public string ItemNumber { get; set; }
    public DateTime StartingDate { get; set; }
    public int DuplicateCount { get; set; }
    public List<CreatePurchasePriceDto> DuplicatedItems { get; set; } = [];
}
