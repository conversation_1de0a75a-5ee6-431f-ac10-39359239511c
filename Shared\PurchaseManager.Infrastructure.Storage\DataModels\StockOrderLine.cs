using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

[PrimaryKey("StockOrderNumber", "LineNumber")]
[Table("StockOrderLine")]
public class StockOrderLine : FullTrackingEntity
{
    [Key]
    [MaxLength(100)]
    public string StockOrderNumber { get; set; } = null!;

    [Key]
    public int LineNumber { get; set; }// Sequential line number within StockOrder (1, 2, 3...)

    [Required]
    [MaxLength(100)]
    public string POHeaderNumber { get; set; } = null!;

    [Required]
    public int POLineNumber { get; set; }// Reference to PO Line (for lookup only, no FK)

    [Required]
    public int DocumentType { get; set; }

    [Required]
    [MaxLength(100)]
    public string ItemNumber { get; set; } = null!;

    [MaxLength(100)]
    public string LotNo { get; set; } = string.Empty;

    public DateTime? ExpirationDate { get; set; }

    [Required]
    public int TotalQuantity { get; set; }

    [Required]
    public int QuantityReceived { get; set; }

    [MaxLength(200)]
    public string ItemName { get; set; } = string.Empty;

    [MaxLength(500)]
    public string Note { get; set; } = string.Empty;

    // Navigation properties
    public virtual StockOrderHeader StockOrderHeader { get; set; }
}
