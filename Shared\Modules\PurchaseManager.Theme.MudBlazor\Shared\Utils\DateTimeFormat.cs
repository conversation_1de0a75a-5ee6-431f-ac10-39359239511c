﻿using System.Globalization;

namespace PurchaseManager.Theme.Material.Shared.Utils
{
    public static class DateTimeFormat
    {

        public static string GetDateString(this DateTime date)
        {
            var dtFI = new CultureInfo("vi-VN", false).DateTimeFormat;
            return date.ToString("ddd, dd/MM/yyyy HH:mm:ss", dtFI);
        }
        public static string GetDateStringNoTime(this DateTime date)
        {
            var format = new CultureInfo("vi-VN", false).DateTimeFormat;
            return date.ToString("ddd, dd/MM/yyyy", format);
        }
        public static string GetDateStringNoTime(this DateOnly date)
        {
            var format = new CultureInfo("vi-VN", false).DateTimeFormat;
            return date.ToString("ddd, dd/MM/yyyy", format);
        }
    }
}
