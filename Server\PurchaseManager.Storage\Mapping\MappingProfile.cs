﻿using AutoMapper;
using Finbuckle.MultiTenant;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Admin;
using PurchaseManager.Shared.Dto.Contact;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Dto.Sample;
using PurchaseManager.Shared.Dto.Vendor;
using PurchaseManager.Shared.Models;
using DemandInput = PurchaseManager.Infrastructure.Storage.DataModels.DemandInput;
using Message = PurchaseManager.Infrastructure.Storage.DataModels.Message;
using PurchaseOrder = PurchaseManager.Infrastructure.Storage.DataModels.PurchaseOrder;
using PurchaseOrderHeader = PurchaseManager.Infrastructure.Storage.DataModels.PurchaseOrderHeader;
using QueueGenPurchaseOrder = PurchaseManager.Infrastructure.Storage.DataModels.QueueGenPurchaseOrder;
using Vendor = PurchaseManager.Infrastructure.Storage.DataModels.Vendor;
using Contact = PurchaseManager.Infrastructure.Storage.DataModels.Contact;
using GetVendorDto=PurchaseManager.Shared.Dto.Vendor.GetVendorDto;

namespace PurchaseManager.Storage.Mapping
{
    public class MappingProfile : Profile
    {
        /// <summary>
        /// Create automap mapping profiles
        /// </summary>
        public MappingProfile()
        {
            CreateMap<TenantInfo, TenantDto>().ReverseMap();
            CreateMap<Message, MessageDto>().ReverseMap();
            CreateMap<Schedule, PurchaseOrderHeader>().ReverseMap();
            CreateMap<Vendor, Shared.Models.GetVendorDto>().ReverseMap();
            CreateMap<Vendor, GetVendorDto>().ReverseMap();
            CreateMap<PurchaseOrder, DetailPoDto>();
            CreateMap<DetailPoDto, PurchaseOrder>();
            CreateMap<PurchaseOrder, UpdatePoDto>().ReverseMap();
            CreateMap<DetailPoDto, UpdatePoDto>().ReverseMap();

            CreateMap<PurchaseOrderDto.Line.Get, PurchaseOrderModels.Line.FullInsertLine>();
            CreateMap<PurchaseOrderDto.Line.Get, PurchaseOrderDto.Line.Get>();

            CreateMap<QueueGenPurchaseOrder, DemandInput>().ReverseMap();
            CreateMap<DemandItemInfo, CreatePOFromInputDemandDto>().ReverseMap();
            CreateMap<DemandInfo, DemandItemInfo>();
            CreateMap<DetailPurchaseSuggestedPaymentLineDto, DetailPurchaseSuggestedPaymentLineDto>();

            // Contacts
            // CreateMap<CreateContactDto, Contact>().ForMember(dest => dest.VendorNumberNavigation, opt => opt.Ignore()); // Bỏ qua VendorNumberNavigation
            CreateMap<Contact, CreateContactDto>();
            CreateMap<Contact, GetContactDto>()
                .ForMember(destinationMember: dest => dest.VendorName,
                memberOptions: opt => opt.MapFrom(src => src.VendorNumberNavigation.Name)).ReverseMap();
            // CreateMap<UpdateContactDto, Contact>();
            CreateMap<GetContactDto, UpdateContactDto>();
            CreateMap<GetContactDto, CreateContactDto>();
            // Contacts

            CreateMap<DetailVendorItemDto, DetailItemDto>()
                                                    .ForMember(dest => dest.BaseUnitOfMeasure, opt => opt.MapFrom(src => src.UnitOfMeasure))
                                                    .ForMember(dest => dest.Number, opt => opt.MapFrom(src => src.ItemNumber));

            CreateMap<PurchaseOrderHeader, PurchaseOrderDto.Header.Detail>()
                .ForMember(dest => dest.PayToVendorNumber, opt => opt.MapFrom(src => src.PayToCode))
                .ForMember(destinationMember: dest => dest.VATRegistrationNumber,
                memberOptions: opt => opt.MapFrom(src => src.VATRegistrationNumber))
                .ForMember(destinationMember: dest => dest.VATBusinessPostingGroup,
                memberOptions: opt => opt.MapFrom(src => src.VATBusinessPostingGroup))
                .ForMember(dest => dest.ModifiedID, opt => opt.MapFrom(src => src.ModifiedId))
                .ForMember(destinationMember: dest => dest.UsingID, memberOptions: opt => opt.MapFrom(src => src.UsingID))
                // .ForMember(dest => dest.ApprovalDate, opt => opt.MapFrom(src => src.ApprovalDate.HasValue ? src.ApprovalDate.Value : default))
                .ForMember(dest => dest.TotalAmount, opt => opt.Ignore()) // Cần tính toán từ các dòng chi tiết nếu có
                .ForMember(dest => dest.TotalQuantity, opt => opt.Ignore()) // Cần tính toán từ các dòng chi tiết nếu có
                .ForMember(dest => dest.TotalQuantityReceived, opt => opt.Ignore()) // Cần tính toán từ các dòng chi tiết nếu có
                .ForMember(dest => dest.UsingMinutes, opt => opt.Ignore()) // Tùy vào logic của bạn
                .ForMember(dest => dest.TotalRecord, opt => opt.Ignore()) // Tùy vào logic của bạn
                .ForMember(dest => dest.VendorApprovalBy, opt => opt.Ignore()) // Tùy vào logic của bạn
                .ForMember(dest => dest.PurchaserApprovalBy, opt => opt.Ignore()); // Tùy vào logic của bạn

            // CreateMap<PurchaseOrderHeader, PurchaseOrderDto.Header.Detail>()
            //     .ForMember(dest => dest.PayToVendorNumber, opt => opt.MapFrom(src => src.PayToCode))
            //     .ForMember(dest => dest.TotalAmount, opt => opt.MapFrom(src => (decimal)src.PaymentDiscount)) // Casting if necessary
            //     .ForMember(dest => dest.YourReference, opt => opt.MapFrom(src => src.YourReference))
            //     .ForMember(dest => dest.Checked, opt => opt.MapFrom(src => src.Checked ?? 0)) // Defaulting to 0 if null
            //     .ForMember(dest => dest.UsingID, opt => opt.MapFrom(src => src.UsingId ?? string.Empty)) // Defaulting to empty string if null
            //     .ForMember(dest => dest.ModifiedID, opt => opt.MapFrom(src => src.ModifiedId ?? string.Empty)) // Defaulting to empty string if null
            //     .ForMember(dest => dest.OverheadRate, opt => opt.MapFrom(src => src.OverheadRate.HasValue ? (decimal)src.OverheadRate.Value : 0)) // Handling nullable decimal
            //     .ForMember(dest => dest.DocNoOccurrence, opt => opt.MapFrom(src => src.DocNoOccurrence)); // Direct mapping
        }
    }
}
