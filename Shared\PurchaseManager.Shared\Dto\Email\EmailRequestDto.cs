﻿#nullable enable
namespace PurchaseManager.Shared.Dto.Email;

public class EmailRequestDto
{
    public List<EmailAddressDto> ToAddresses { get; set; } = default!;
    public string Subject { get; set; } = default!;
    public string Body { get; set; } = default!;
    public int TemplateId { get; set; }
    public bool Queued = true;
    public bool IsHtml { get; set; } = true;
    public List<EmailAddressDto>? CcAddresses { get; set; }
    public List<EmailAddressDto>? BccAddresses { get; set; }
}
