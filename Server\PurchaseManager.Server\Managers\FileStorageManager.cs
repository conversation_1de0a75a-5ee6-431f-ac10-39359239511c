﻿using System.Net.Http.Headers;
using System.Text;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using NetTopologySuite.Utilities;
using Newtonsoft.Json;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.FileStorage;
using PurchaseManager.Shared.Models.FileStorage;
using PurchaseManager.Storage;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Managers;

public class FileStorageManager : IFileStorageManager
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;
    private readonly ApplicationPersistenceManager _persistenceManager;
    private readonly IAdminManager _adminManager;
    private readonly IStringLocalizer<Global> L;
    private readonly ILogger<FileStorageManager> _logger;
    private const string Business = "FILES";
    private const string Branch = "AL";
    private const string UserName = "erpteams";
    private const string PassWord = "Since@2024";
    private const string UrlApiFilesUpload = "https://it.trungsonpharma.com:8989/uploads.php";

    public FileStorageManager(ApplicationDbContext context, IAdminManager adminManager, IMapper mapper, IStringLocalizer<Global> l, ILogger<FileStorageManager> logger, ApplicationPersistenceManager persistenceManager)
    {
        _context = context;
        _adminManager = adminManager;
        _mapper = mapper;
        L = l;
        _logger = logger;
        _persistenceManager = persistenceManager;
    }
    public async Task<ApiResponse> SaveFilesAsync(List<IFormFile> files, String documentNo, String prefix)
    {
        var fileRecordStorages = new List<FileStorage>();
        foreach (var file in files)
        {
            var fileId = await _adminManager.CreateNumberSeries(Business, Branch);
            var uploadResponse = await UploadFileToExternalService(UrlApiFilesUpload, UserName, PassWord, file);
            if (uploadResponse.StatusCode != 200 || uploadResponse.Result == null)
            {
                _logger.LogError("Error uploading file to external service");
                return new ApiResponse(Status500InternalServerError, "Error uploading file to external service");
            }

            var filesServerResponse = (FilesServerResponse)uploadResponse.Result;
            var fileUrl = filesServerResponse.Files.FirstOrDefault()!.Url;

            if (string.IsNullOrEmpty(fileUrl))
            {
                _logger.LogError("File URL is null or empty after upload");
                return new ApiResponse(Status500InternalServerError, "File URL is null or empty after upload");
            }
            var fileRecord = new CreateFileDto
            {
                FileId = fileId,
                FileName = file.FileName,
                FileType = file.ContentType,
                FileUrl = fileUrl,
                UploadDate = DateTime.Now,
                Description = "",
                Blocked = false,
                Status = StatusFileEnum.Saved,
                DocumentNo = documentNo,
                Prefix = prefix
            };

            var fileStorage = _mapper.Map<FileStorage>(fileRecord);
            fileStorage.LoginId = _adminManager.GetUserLogin();
            fileRecordStorages.Add(fileStorage);
        }
        try
        {
            await _context.FileStorages.AddRangeAsync(fileRecordStorages);
            await _context.SaveChangesAsync();
            return new ApiResponse(Status201Created, "Created successfully");
        }
        catch (Exception e)
        {
            _logger.LogError($"Save file exception: {e.GetBaseException().Message}");
            return new ApiResponse(Status404NotFound, L["Error create record"]);
        }
    }
    public async Task<ApiResponse> UpdateFileAsync(string fileId, IFormFile file, string description)
    {
        try
        {
            var fileRecord = await _context.FileStorages.FirstOrDefaultAsync(f => f.FileId == fileId);
            if (fileRecord == null)
            {
                return new ApiResponse(Status404NotFound, L["Record does not exist"]);
            }
            //var fileUrl = await UploadFileToExternalService(file);
            var fileUpdateDto = new UpdateFileDto
            {
                FileUrl = "fileUrl",
                Description = description,
                FileName = file.FileName,
                FileType = file.ContentType,
                Status = StatusFileEnum.Saved
            };
            _mapper.Map(fileUpdateDto, fileRecord);
            fileRecord.LastDateModified = DateTime.Now;
            fileRecord.LastUserModified = _adminManager.GetUserLogin();
            await _context.SaveChangesAsync();
            return new ApiResponse(Status200OK, "Updated successfully");
        }
        catch (Exception e)
        {
            _logger.LogError($"Update file exception: {e.GetBaseException().Message}");
            return new ApiResponse(Status404NotFound, L["Error create record"]);
        }
    }
    public IQueryable<DetailFileDto> GetFilesAsync(FileStorageFilter filter, string queryString)
    {
        IQueryable<FileStorage> files = _persistenceManager.GetEntities<FileStorage>().AsNoTracking()
            .Where(h =>
                (filter.Query == null || h.FileName.Contains(filter.Query)) &&
                (filter.FileId == null || h.FileId.Equals(filter.FileId)) && (
                (filter.DocumentNo == null || h.DocumentNo.Equals(filter.DocumentNo)) &&
                (filter.Prefix == null || h.Prefix.Equals(filter.Prefix))) &&
                h.Blocked == false)
            .OrderByDescending(f => f.RowId);

        if (!queryString!.Contains("take"))
        {
            files = files.Take(10);
        }

        if (!queryString!.Contains("skip"))
        {
            files = files.Skip(0);
        }
        var filesDto = _mapper.ProjectTo<DetailFileDto>(files);
        return filesDto;
    }
    public async Task<ApiResponse> DeleteFileAsync(string fileId)
    {
        try
        {
            var fileRecord = await _context.FileStorages.FirstOrDefaultAsync(f => f.FileId == fileId);
            if (fileRecord == null)
            {
                return new ApiResponse(Status404NotFound, L["Record does not exist"]);
            }
            fileRecord.Blocked = true;
            fileRecord.LastDateModified = DateTime.Now;
            fileRecord.LastUserModified = _adminManager.GetUserLogin();
            await _context.SaveChangesAsync();
            return new ApiResponse(Status200OK, L["Deleted successfully"]);
        }
        catch (Exception e)
        {
            _logger.LogError($"Delete file exception: {e.GetBaseException().Message}");
            return new ApiResponse(Status404NotFound, L["Error delete record"]);
        }
    }
    private async Task<ApiResponse> UploadFileToExternalService(string url, string username, string password, IFormFile file)
    {
        try
        {
            using var client = new HttpClient();
            var authToken = Encoding.ASCII.GetBytes($"{username}:{password}");
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(authToken));
            var content = new MultipartFormDataContent();
            // Add files to the form data
            try
            {
                var fileStream = file.OpenReadStream();
                var fileContent = new StreamContent(fileStream);
                fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse(file.ContentType);
                content.Add(fileContent, "Files[]", file.FileName);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error reading file '{file.FileName}': {ex.Message}");
                return new ApiResponse(500, $"Error reading file '{file.FileName}'");
            }

            var response = await client.PostAsync(url, content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("Response: " + responseContent);
                var responseData = JsonConvert.DeserializeObject<FilesServerResponse>(responseContent);
                return new ApiResponse(200, "Files uploaded to server successfully", responseData);
            }
            else
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Response: " + responseContent);
                _logger.LogError("Status Code: " + response.StatusCode);
                return new ApiResponse(500, "Error uploading files to server");
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e.GetBaseException().Message);
            return new ApiResponse(500, "Error uploading files to server");
        }
    }
    public class FilesServerResponse
    {
        public List<FileDataResponse> Files { get; set; }

        public string Message { get; set; }

        public int Total { get; set; }

        public int Status { get; set; }
    }

    public class FileDataResponse
    {
        public string Name { get; set; }

        public List<int> Size { get; set; }

        public List<string> Type { get; set; }
        public string Url { get; set; }
    }
}
