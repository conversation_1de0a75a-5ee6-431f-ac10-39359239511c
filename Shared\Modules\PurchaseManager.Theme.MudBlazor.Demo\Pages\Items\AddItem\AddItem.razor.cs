﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Localization;

using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.Db.Validators;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
namespace PurchaseManager.Theme.Material.Demo.Pages.Items.AddItem;
public partial class AddItemBase : ComponentBase
{
    [Inject] NavigationManager navigation { get; set; }
    [Inject] protected IApiClient apiClient { get; set; }
    [Inject] private IViewNotifier notifier { get; set; }
    [Inject] protected IStringLocalizer<Global> L { get; set; }
    protected CreateItemDto item = new CreateItemDto();
    protected CreateItemDtoValidator createItemDtoValidator;
    protected bool isLoading { get; set; }
    protected string selectedUnitOfMeasure { get; set; }
    protected EditContext editContext { get; set; }
    protected List<UnitOfMeasureDto> listUnitOfMeasureDto { get; set; } = new List<UnitOfMeasureDto>();
    protected Func<UnitOfMeasureDto, string> converter = p => p?.Code;
    protected UnitOfMeasureDto unitOfMeasureDto { get; set; } = new UnitOfMeasureDto();
    protected override async void OnInitialized()
    {

        item ??= new();
        editContext = new(item);
        var resp = await apiClient.GetAllUnitOfMeasure(null, null, null);
        listUnitOfMeasureDto = resp.ToList();
        base.OnInitialized();
        isLoading = false;
    }

    protected async void OnAddItem()
    {

        if (unitOfMeasureDto.Code != null)
        {

            item.BaseUnitOfMeasure = unitOfMeasureDto.Code;
            item.UnitOfMeasureDtos = new List<CreateItemUnitOfMeasureDto>() {
                new CreateItemUnitOfMeasureDto
                {
                    Code = unitOfMeasureDto.Code,
                    Description = unitOfMeasureDto.Description,
                    QuantityPerUnitOfMeasure = unitOfMeasureDto.QuantityPerUnitOfMeasure,
                    Type = unitOfMeasureDto.Type
                }
            };
            //TODO add new item
            var resp = await apiClient.CreateItem(item);
            if (!resp.IsSuccessStatusCode)
            {
                notifier.Show(resp.Message, ViewNotifierType.Error, L["Operation Failed"]);
                return;
            }
            var itemNumber = resp.Result;
            navigation.NavigateTo("/item/" + itemNumber);
        }
        else return;
    }
}
