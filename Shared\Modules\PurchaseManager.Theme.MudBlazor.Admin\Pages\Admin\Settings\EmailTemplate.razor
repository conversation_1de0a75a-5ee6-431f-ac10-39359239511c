﻿@page "/admin/settings/email/email-template/create"
@page "/admin/settings/email/email-template/edit/{EmailTemplateId:int}"
@inherits PurchaseManager.Theme.Material.Admin.Pages.Admin.Settings.EmailTemplateBase
@layout AdminLayout


<MudCard Elevation="0" Outlined Class="mt-2">
    <MudCardHeader>
        <CardHeaderContent>
            <MudStack Row>
                <MudTooltip Text="Back to list" Placement="Placement.End">
                    <MudIconButton OnClick="@(() => {navigation.NavigateTo("/admin/settings/email/email-templates");})" Icon="@Icons.Material.Filled.ArrowBack" Color="Color.Default" />
                </MudTooltip>
                <MudText Typo="Typo.h4" Class="mx-auto" Align="Align.Center">Preview Email Template </MudText>
            </MudStack>
        </CardHeaderContent>
        <CardHeaderActions>
            @*<MudTooltip Text="Send this email">
                <MudIconButton Icon="@Icons.Material.Filled.Send" Color="Color.Primary" />
            </MudTooltip>*@
        </CardHeaderActions>
    </MudCardHeader>
    <MudCardContent>
        <MudForm @ref="form" @bind-IsValid="@success" Class="mb-4" @bind-Errors="@errors">
            <MudGrid>
                <MudItem sm="4">
                    <MudTextField T="string" @bind-Value="@templateName" Label="TemplateName" Required="true"
                                  RequiredError="TemplateName is required!" />
                </MudItem>
                <MudItem sm="4">
                    <MudTextField T="string" @bind-Value="@subject" Label="Subject" Required="true" RequiredError="Subject is required!" />

                </MudItem>
                <MudItem sm="4">
                    <MudStack Class="mt-4">
                        <MudButton Variant="Variant.Filled" Color="Color.Primary" DropShadow="false"
                                   OnClick="@(async()=> await CreateEmailTemplate())">@(isCreate ? "Create" : "Update") </MudButton>
                    </MudStack>
                </MudItem>
            </MudGrid>
            <MudTextField T="string" Label="Html Input" Class="mb-4" Variant="Variant.Text" @onfocus="@(() => isShowPreview = false)" @bind-Value="@inputHtml" Lines="8" />
            <MudButton Variant="Variant.Filled" Color="Color.Primary " DropShadow="false"
                       OnClick="@(async () => { value = ""; value = inputHtml; isShowPreview = true; })">Preview</MudButton>
        </MudForm>
        @if (isShowPreview)
        {
            <MudBlazor.Extensions.Components.MudExHtmlEdit ReadOnly="false"
                                                           UpdateValueOnChange="true"
                                                           Value="@value"
                                                           OnClickStopPropagation="true"
                                                           OnContextMenuPreventDefault="true"
                                                           Height="500"
                                                           OnContextMenuStopPropagation="true">
            </MudBlazor.Extensions.Components.MudExHtmlEdit>
        }
    </MudCardContent>
    <MudCardActions>
    </MudCardActions>
</MudCard>

