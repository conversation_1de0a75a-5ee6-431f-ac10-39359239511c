﻿@inherits SendEmailBase
@layout AdminLayout

@page "/admin/settings/email/send-email"
@if (isLoading)
{
    <LoadingBackground />
}
else
{
    <MudPaper Elevation="0" Outlined Class="mt-2 px-4 pb-14 pt-2">
        <MudStack Row Justify="Justify.Center" StretchItems="StretchItems.All">
            <MudSpacer />
            <MudStack>
                <MudStack Row>
                    <MudAutocomplete T="PurchaseManager.Shared.Dto.Email.DetailEmailTemplate"
                                     ToStringFunc="@((o) => o is null ? "" : o.TemplateName)" Label="Template"
                                     @bind-Value="detailEmailTemplate" SearchFunc="@SearchTemplate" Variant="Variant.Text" />
                    <MudButton Variant="Variant.Text" Class="mt-2" Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Preview" DropShadow="false"
                               OnClick="@( async () => { bodyPreview = detailEmailTemplate.Body ; isShowPreview = true; await Task.Delay(2000);  StateHasChanged(); })">
                        Preview
                    </MudButton>
                </MudStack>
                <MudStack Row Justify="Justify.Center">
                    <MudChipSet Class="mt-2" T="string" @bind-SelectedValues="_selectedMode" CheckMark
                                SelectionMode="@SelectionMode.MultiSelection">
                        <MudChip Text="CC" Color="Color.Primary" Value="@("CC")">CC</MudChip>
                        <MudChip Text="BCC" Color="Color.Info" Value="@("BCC")">BCC</MudChip>
                    </MudChipSet>
                </MudStack>
            </MudStack>
            <MudSpacer />
        </MudStack>
        <MudForm @ref="form" @bind-IsValid="@success" Class="my-2" @bind-Errors="@errors">
            <MudGrid>
                <MudItem sm="@(_selectedMode.Contains("CC") && _selectedMode.Contains("BCC") ? 4 : (!_selectedMode.Contains("CC") && !_selectedMode.Contains("BCC") ? 12 : 6))">
                    <MudStack Row Justify="Justify.SpaceBetween">
                        <MudTextField T="string" Label="To Email Address" @ref="@toAddressRef"
                                      @bind-Value="@toAddressEditing" OnKeyUp="@(async(e) => await OnToAddressChanged(e))"
                                      Validation="@(new System.ComponentModel.DataAnnotations.EmailAddressAttribute() {ErrorMessage = "The email address is invalid"})" />
                    </MudStack>
                    @if (requestDto.ToAddresses is not null)
                    {
                        @foreach (var item in requestDto.ToAddresses)
                        {
                            <MudChip OnClick="@( async() => await OnClickToEditToAddress(item.Name))"
                                     T="PurchaseManager.Shared.Dto.Email.EmailAddressDto" Color="Color.Primary" Value="@item">
                                @item.Address
                            </MudChip>
                        }
                    }
                </MudItem>
                @if (_selectedMode.Contains("CC"))
                {
                    <MudItem sm="@(_selectedMode.Contains("CC") && _selectedMode.Contains("BCC") ? 4 : 6)">
                        <MudStack Row Justify="Justify.SpaceBetween">
                            <MudTextField T="string" Label="CC Email Address" @ref="@ccRef" @bind-Value="@ccEditing"
                                          OnKeyUp="@(async(e) => await OnCCChanged(e))"
                                          Validation="@(new System.ComponentModel.DataAnnotations.EmailAddressAttribute() {ErrorMessage = "The email address is invalid"})" />
                        </MudStack>
                        @if (requestDto.CcAddresses is not null)
                        {
                            @foreach (var item in requestDto.CcAddresses)
                            {
                                <MudChip OnClick="@(async () => await  OnClickToEditCC(item.Name))"
                                         T="PurchaseManager.Shared.Dto.Email.EmailAddressDto" Color="Color.Error" Value="@item">
                                    @item.Address
                                </MudChip>
                            }
                        }
                    </MudItem>
                }
                @if (_selectedMode.Contains("BCC"))
                {
                    <MudItem sm="@(_selectedMode.Contains("CC") && _selectedMode.Contains("BCC") ? 4 : 6)">
                        <MudStack Row Justify="Justify.SpaceBetween">
                            <MudTextField T="string" Label="BCC Email Address" @ref="@bccRef" @bind-Value="@bccEditing"
                                          OnKeyUp="@(async(e) => await OnBCCChanged(e))"
                                          Validation="@(new System.ComponentModel.DataAnnotations.EmailAddressAttribute() {ErrorMessage = "The email address is invalid"})" />
                        </MudStack>
                        @if (requestDto.BccAddresses is not null)
                        {
                            @foreach (var item in requestDto.BccAddresses)
                            {
                                <MudChip OnClick="@(async() => await  OnClickToEditBCC(item.Name))"
                                         T="PurchaseManager.Shared.Dto.Email.EmailAddressDto" Color="Color.Info" Value="@item">
                                    @item.Address
                                </MudChip>
                            }
                        }
                    </MudItem>
                }
            </MudGrid>
        </MudForm>
        <MudStack Row Justify="Justify.SpaceBetween" Class="mb-2">
            <MudText Align="Align.Center" Typo="Typo.h6" Class="mt-4">Preview and Send </MudText>
            @if (requestDto.ToAddresses is not null && requestDto.ToAddresses.Any())
            {
                <MudButton Variant="Variant.Filled" OnClick="@SendEmail" Class="mt-4" Color="Color.Primary"
                           StartIcon="@Icons.Material.Filled.Send" Disabled="@(requestDto.ToAddresses is not null && !requestDto.ToAddresses.Any())" DropShadow="false">Send</MudButton>
            }
        </MudStack>
        @if (isShowPreview && bodyPreview != "")
        {
            <MudBlazor.Extensions.Components.MudExHtmlEdit Style=" min-height:800px" ReadOnly="true" Value="@bodyPreview">
            </MudBlazor.Extensions.Components.MudExHtmlEdit>
        }
        else
        {
            <MudText Typo="Typo.subtitle2" Align="Align.Center"> Please chosse template</MudText>
        }
    </MudPaper>
}
