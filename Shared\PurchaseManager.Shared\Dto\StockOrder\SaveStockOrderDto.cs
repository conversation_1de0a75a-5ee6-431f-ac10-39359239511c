namespace PurchaseManager.Shared.Dto.StockOrder;

public class SaveStockOrderDto
{
  public string POHeaderNumber { get; set; } = null!;
  public int DocumentType { get; set; }
  public string Note { get; set; } = string.Empty;
  public List<SaveStockOrderLineDto> Lines { get; set; } = new List<SaveStockOrderLineDto>();
}

public class SaveStockOrderLineDto
{
  public string POHeaderNumber { get; set; } = null!;
  public int POLineNumber { get; set; }
  public int DocumentType { get; set; }
  public string ItemNumber { get; set; } = null!;
  public string LotNo { get; set; } = string.Empty;
  public DateTime? ExpirationDate { get; set; }
  public int TotalQuantity { get; set; }
  public int QuantityReceived { get; set; }
  public string ItemName { get; set; } = string.Empty;
  public string Note { get; set; } = string.Empty;
}
