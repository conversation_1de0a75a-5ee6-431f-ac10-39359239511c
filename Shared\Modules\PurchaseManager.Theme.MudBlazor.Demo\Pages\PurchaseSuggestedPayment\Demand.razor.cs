﻿using System.Data;
using System.Net.Http.Headers;
using System.Text;
using AutoMapper;
using ExcelDataReader;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Localization;
using MudBlazor;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.PO;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseSuggestedPayment;

public class DemandBase : ComponentBase
{
    [Parameter] public string Label { get; set; } = "Upload ";
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    [Inject]
    private IMapper Mapper { get; set; }
    protected bool IsFileRead { get; set; }
    protected bool IsFileLoading { get; set; }
    protected int TotalRecordAllowed { get; set; } = 50000;
    protected int MaxAllowedSize { get; set; } = 1024 * 1024 * 30;
    [Inject] protected IStringLocalizer<Global> L { get; set; }
    protected bool IsProcessing { get; set; }
    protected bool IsCreatePO { get; set; }
    protected bool IsAnyItemInvalid { get; set; }
    protected string GlobalMsg { get; set; }

    [Inject] protected IApiClient ApiClient { get; set; }
    protected bool IsAlertDialogOpen { get; set; }
    protected List<CheckPaymentDayResponse> ListCheckPaymentDayResponse { get; set; } = [];
    protected List<DemandInfo> ListDemandDto { get; set; }
    protected List<InvalidItem> ListInvalidItem { get; set; } = [];
    protected List<DemandInfo> DuplicateItems = [];

    private MultipartFormDataContent FormData
    {
        get;
    } = new MultipartFormDataContent();
    protected readonly TableGroupDefinition<DemandInfo> GroupDefinitionUploadItems = new TableGroupDefinition<DemandInfo>
    {
        GroupName = "Store", Indentation = false, Expandable = false, Selector = e => e.StoreId
    };
    protected readonly TableGroupDefinition<InvalidItem> GroupDefinitionInvalidItems = new TableGroupDefinition<InvalidItem>
    {
        GroupName = "Item Number", Indentation = false, Expandable = false, Selector = e => e.ItemNumber
    };
    protected override void OnInitialized()
    {
        ListDemandDto = [];
        base.OnInitialized();
    }
    protected async Task UploadFiles(IBrowserFile file)
    {
        GlobalMsg = "";
        IsFileLoading = true;
        IsProcessing = true;

        if (file == null)
        {
            return;
        }
        if (!Path.GetExtension(file.Name).Equals(".xlsx", StringComparison.OrdinalIgnoreCase) &&
            !Path.GetExtension(file.Name).Equals(".xls", StringComparison.OrdinalIgnoreCase))
        {
            ViewNotifier.Show("Only Excel file", ViewNotifierType.Warning, "Operation Failed");
            return;
        }

        ListDemandDto = [];
        try
        {
            Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

            var stream = new MemoryStream();
            await file.OpenReadStream(MaxAllowedSize).CopyToAsync(stream);
            stream.Position = 0;
            using var reader = ExcelReaderFactory.CreateReader(stream);
            var result = reader.AsDataSet(new ExcelDataSetConfiguration
            {
                ConfigureDataTable = _ => new ExcelDataTableConfiguration
                {
                    UseHeaderRow = true
                }
            });

            var memoryStreamCopy = new MemoryStream(stream.ToArray());
            var streamContent = new StreamContent(memoryStreamCopy);
            streamContent.Headers.ContentType = new MediaTypeHeaderValue(file.ContentType);

            FormData.Add(streamContent, "file", file.Name);
            if (file.Size > MaxAllowedSize)
            {
                ViewNotifier.Show($"File size exceeds the limit of {MaxAllowedSize} bytes.", ViewNotifierType.Error);
                return;
            }

            var table = result.Tables[0];

            var listDtPo = new List<DemandInfo>();
            for (var i = 0; i < table.Rows.Count; i++)
            {
                var row = table.Rows[i];
                if (row.ItemArray.Length < 5 || row.ItemArray[0] == null)
                {
                    ViewNotifier.Show("File không đúng định dạng. Vui lòng kiểm tra lại.", ViewNotifierType.Error);
                    return;
                }

                listDtPo.Add(new DemandInfo
                {
                    Index = i,
                    ItemNo = GetIntValue(row, 0),
                    StoreId = GetStringValue(row, 1),
                    QtyDemand = GetIntValue(row, 2),
                    UnitOfMeasure = GetStringValue(row, 3),
                    Tags = GetStringValue(row, 4),
                    QtyRequest = 0,
                    Quantity = GetIntValue(row, 2)
                });
            }

            Parallel.ForEach(listDtPo, body: currentItem =>
            {
                currentItem.UnitOfMeasure = currentItem.UnitOfMeasure.ToUpper();
                if (string.IsNullOrWhiteSpace(currentItem.Tags))
                {
                    return;
                }
                var listTags = currentItem.Tags.Split(',')
                    .Where(item => !string.IsNullOrWhiteSpace(item))
                    .Select(x => x.Trim())
                    .Distinct(StringComparer.OrdinalIgnoreCase)
                    .OrderBy(x => x.ToLower())
                    .ToList();
                currentItem.Tags = string.Join(",", listTags);
            });

            DuplicateItems = listDtPo
                .GroupBy(i => new
                {
                    i.ItemNo, i.StoreId, i.UnitOfMeasure
                })
                .Where(g => g.Count() > 1)
                .SelectMany(g => g)
                .ToList();

            if (DuplicateItems.Count != 0)
            {
                ViewNotifier.Show("SKUs bị trùng lặp. Vui lòng kiểm tra lại.", ViewNotifierType.Warning);
            }

            ListDemandDto = listDtPo;

            await ValidateItemsInDemandV2();
            IsFileRead = true;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
        finally
        {
            IsProcessing = false;
            IsFileLoading = false;
            StateHasChanged();
        }
    }
    protected void Clear()
    {
        ListDemandDto = [];
        IsAnyItemInvalid = false;
        IsFileRead = false;
    }


    protected async Task ValidateItemsInDemandV2()
    {
        try
        {
            IsProcessing = true;

            const int batchSize = 150000;
            var batches = ListDemandDto
                .Select((item, index) => new
                {
                    item, index
                })
                .GroupBy(x => x.index / batchSize)
                .Select(g => g.Select(x => x.item).ToList())
                .ToList();

            var totalValidatedCount = 0;
            foreach (var mappedItems in batches.Select(batch => Mapper.Map<List<DemandItemInfo>>(batch)))
            {
                var resp = await ApiClient.ValidateItemsInDemandV2(mappedItems);

                if (resp.IsSuccessStatusCode)
                {
                    var result = resp.Result;
                    if (result is { Count: > 0 })
                    {
                        ListInvalidItem = result;
                        IsAnyItemInvalid = true;
                    }
                    else
                    {
                        IsAnyItemInvalid = false;
                    }

                    var batchCount = mappedItems.Count;
                    totalValidatedCount += batchCount;
                    Console.WriteLine($"Validated {batchCount} records in this batch. Total validated: {totalValidatedCount}");
                }
                else
                {
                    var errorContent = resp.Message;
                    ViewNotifier.Show($"API error: {resp.StatusCode} - {errorContent}", ViewNotifierType.Error);
                    break;
                }

                await Task.Delay(500);
            }

            Console.WriteLine($"Total validated records: {totalValidatedCount}");
        }
        catch (Exception ex)
        {
            ViewNotifier.Show($"Error calling API: {ex.GetBaseException().Message}", ViewNotifierType.Error);
            Console.WriteLine(ex.Message);
        }
        finally
        {
            IsProcessing = false;
            IsFileLoading = false;
            StateHasChanged();
        }
    }


    protected void RemoveDuplicateItem(int index)
    {
        try
        {
            var deleteItem = ListDemandDto.Find(x => x.Index == index);
            if (deleteItem != null)
            {
                ListDemandDto.Remove(deleteItem);
            }
            var deleteItemInDuplicateList = DuplicateItems.Find(x => x.Index == index);
            if (deleteItemInDuplicateList != null)
            {
                DuplicateItems.Remove(deleteItemInDuplicateList);
                DuplicateItems = ListDemandDto
                    .GroupBy(i => new
                    {
                        i.ItemNo, i.StoreId, i.UnitOfMeasure
                    })
                    .Where(g => g.Count() > 1)
                    .SelectMany(g => g)
                    .ToList();

                if (DuplicateItems.Count != 0)
                {
                    ViewNotifier.Show("Tồn tại SKUs trùng lập.", ViewNotifierType.Warning);
                }
            }
            ViewNotifier.Show(L["Item Removed"], ViewNotifierType.Success);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error);
        }
    }
    protected bool IsItemDuplicate(int index, int itemNo, string storeId) => ListDemandDto.Where(x => x.ItemNo == itemNo
                                                                                 && x.StoreId == storeId).ToList().Count > 1
                                                                             && DuplicateItems.Where(x => x.ItemNo == itemNo
                                                                                 && x.StoreId == storeId
                                                                                 && x.Index == index).ToList().Count != 0;
    protected async Task CheckPaymentDayByVendorNumber()
    {
        try
        {
            IsProcessing = true;
            var req = ListDemandDto
                .Select(d => d.ItemNo.ToString())
                .Distinct()
                .ToList();
            var resp = await ApiClient.CheckPaymentDayByItemNumber(req);
            if (resp.Result.Count > 0)
            {
                ListCheckPaymentDayResponse = resp.Result;
                IsAlertDialogOpen = true;
            }
            else
            {
                StateHasChanged();
                await CreatePO();

            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        finally
        {
            IsProcessing = false;
            StateHasChanged();
        }
    }
    protected async Task CreatePO()
    {
        IsCreatePO = true;
        IsProcessing = true;
        IsAlertDialogOpen = false;
        try
        {
            Console.WriteLine("Start create POs at: " + DateTime.UtcNow.AddHours(7));
            Console.WriteLine("Total item: " + ListDemandDto.Count);

            var resp = await ApiClient.CreatePurchaseSuggestedPaymentV2(FormData);

            if (!resp.IsSuccessStatusCode)
            {
                ViewNotifier.Show(resp.Message, ViewNotifierType.Error, L["Operation failed"]);
            }
            else
            {
                ViewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation successful"]);
                Clear();
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error, L["Operation failed"]);
            Clear();
        }
        finally
        {
            Console.WriteLine("End create POs at: " + DateTime.UtcNow.AddHours(7));
            IsCreatePO = false;
            IsProcessing = false;
            StateHasChanged();
        }
    }
    private static int GetIntValue(DataRow row, int index)
        => row.ItemArray.Length > index && int.TryParse(row.ItemArray[index]?.ToString(), out var value) ? value : 0;

    private static string GetStringValue(DataRow row, int index) => row.ItemArray.Length > index && row.ItemArray[index] != null
        ? row.ItemArray[index].ToString() : string.Empty;

}
