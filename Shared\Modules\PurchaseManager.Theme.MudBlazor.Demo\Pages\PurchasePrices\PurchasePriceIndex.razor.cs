using System.Net.Http.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.PurchasePrice;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchasePrices;

public partial class PurchasePriceIndex : ComponentBase
{
    [Parameter]
    public string VendorNumberParam { get; set; }
    [Parameter]
    public string VendorNameParam { get; set; }
    [Inject]
    private IStringLocalizer<Global> L { get; set; }
    public List<GetPurchasePriceDto> ListPurchasePriceDtos { get; set; } = [];
    private GetVendorDto SelectedVendor { get; set; } = new GetVendorDto();
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    [Inject]
    private IDialogService DialogService { get; set; }
    private HashSet<GetPurchasePriceDto> ListPurchaseUnitSelected { get; set; } = [];
    private MudTable<GetPurchasePriceDto> TableHeaderRef { get; set; }
    [Inject]
    private IPurchasePriceApiClient PurchasePriceApiClient { get; set; }
    private bool IsLoading { get; set; }
    private int PageIndex { get; set; }
    private int PageSize { get; set; } = 10;
    private int TotalItems { get; set; }
    private bool IsAddFormValid { get; set; }
    protected MudForm addFormRef;
    private List<CreatePurchasePriceDto> ListPurchasePriceDtoToCreate { get; set; } = [];
    protected HashSet<CreatePurchasePriceDto> ListPurchaseUnitSelectedWhenAdd { get; set; } = [];
    [Inject]
    private IVendorApiClient VendorApiClient { get; set; }
    [Inject]
    private NavigationManager NavigationManager { get; set; }
    protected GetPurchasePriceDto ItemToEdit { get; set; } = new GetPurchasePriceDto();
    private PurchasePriceFilter PurchasePriceFilter { get; set; } = new PurchasePriceFilter();
    [Parameter] public string PONumber { get; set; }
    [CascadingParameter]
    [Inject] private HttpClient HttpClient { get; set; }
    private bool IsShowAddFormLine { get; set; }
    protected bool EditDrawerOpen { get; set; }
    protected MudForm EditForm { get; set; }
    protected GetVendorDto VendorSelectedToAdd { get; set; } = new GetVendorDto();
    protected override async Task OnInitializedAsync()
    {
        await LoadAllPurchasePriceAsync();
        await base.OnInitializedAsync();
    }
    private async Task LoadAllPurchasePriceAsync()
    {
        try
        {
            IsLoading = true;
            TotalItems = 0;
            PurchasePriceFilter.PageIndex = PageIndex;
            PurchasePriceFilter.PageSize = PageSize;

            if (ListPurchasePriceDtos.Count != 0)
            {
                ListPurchasePriceDtos.Clear();
            }
            PurchasePriceFilter.VendorNumber = null;
            if (!string.IsNullOrEmpty(SelectedVendor?.Number))
            {
                PurchasePriceFilter.VendorNumber = SelectedVendor.Number;
            }
            var responseApi = await PurchasePriceApiClient.GetPurchasePriceByFilterAsync(PurchasePriceFilter);
            if (responseApi is null)
            {
                ViewNotifier.Show(L["LoadDataFailed"], ViewNotifierType.Error, L["Operation Failed"]);
            }
            else
            {
                if (responseApi.Result.RowCount > 0)
                {
                    ListPurchasePriceDtos = [.. responseApi.Result.Data.ToList()];
                    TotalItems = responseApi.Result.RowCount;
                }
                else
                {
                    ViewNotifier.Show("Not found any data", ViewNotifierType.Info);
                }
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    protected override Task OnParametersSetAsync()
    {
        SelectedVendor = new GetVendorDto();
        if (!string.IsNullOrEmpty(VendorNumberParam))
        {
            SelectedVendor.Number = VendorNumberParam;
        }
        if (string.IsNullOrEmpty(VendorNameParam))
        {
            return base.OnParametersSetAsync();
        }
        SelectedVendor.Name = VendorNameParam;
        return base.OnParametersSetAsync();
    }

    private async Task LoadPurchasePriceByNumberAsync(string number)
    {
        try
        {
            var response = await PurchasePriceApiClient.GetPurchasePriceByPriceNumberAsync(number);
            if (!response.IsSuccessStatusCode) ViewNotifier.Show(L["LoadDataFailed"], ViewNotifierType.Error, L["Operation Failed"]);
            else
            {
                OpenEditDrawer(response.Result);
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }

    private void ShowCreatePurchasePriceDialog()
    {
        ListPurchasePriceDtoToCreate.Clear();
        VendorSelectedToAdd = new GetVendorDto();
        IsShowAddFormLine = true;
    }
    private void ShowCreatePurchasePriceDialogChoseVendor()
    {
        ListPurchasePriceDtoToCreate.Clear();
        VendorSelectedToAdd = SelectedVendor;
        OnAddNewItemToList();
        IsShowAddFormLine = true;
    }

    private async Task OnSearchByVendorChanged(GetVendorDto selectedValue)
    {
        if (!string.IsNullOrEmpty(selectedValue?.Number))
        {
            SelectedVendor = selectedValue;
            NavigationManager.NavigateTo($"/purchase-price/{selectedValue.Number}/{selectedValue.Name}");
        }
        else
        {
            NavigationManager.NavigateTo("/purchase-price");
        }
        StateHasChanged();
        await TableHeaderRef.ReloadServerData();
    }
    private async Task<IEnumerable<GetVendorDto>> SearchFuncVendorByNumber(string value, CancellationToken token)
    {
        var resultList = new List<GetVendorDto>();
        var response = await VendorApiClient.GetVendorsByNumber(value, token);
        if (!response.IsSuccessStatusCode || response.Result.Count == 0)
        {
            return resultList;
        }
        resultList.AddRange([.. response.Result]);
        return resultList;
    }
    private async Task OnDeleteAllHeaderSelectedAsync()
    {
        try
        {
            var result = await DialogService.ShowMessageBox(L["Confirmation"], L["DoYouWantToDelete"], L["Delete"],
            cancelText: "Cancel");
            if (result is null or false)
            {
                return;
            }
            var listErrorWhenDelete = new List<string>();
            foreach (var item in ListPurchaseUnitSelected)
            {
                var resp = await PurchasePriceApiClient.DeletePurchasePriceByPriceNumberAsync(item.Number);
                if (!resp.IsSuccessStatusCode)
                {
                    listErrorWhenDelete.Add(item.Number + " : " + resp.Message);
                }
            }
            if (listErrorWhenDelete.Count == 0)
            {
                ViewNotifier.Show("Data deleted", ViewNotifierType.Success);
                await TableHeaderRef.ReloadServerData();
            }
            else
            {
                ViewNotifier.Show(string.Join(",", listErrorWhenDelete), ViewNotifierType.Error, L["Operation Failed"]);
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    private async Task<TableData<GetPurchasePriceDto>> ServerReload(TableState state, CancellationToken token)
    {
        OnPage(state.Page, state.PageSize);
        await LoadAllPurchasePriceAsync();
        return new TableData<GetPurchasePriceDto>
        {
            TotalItems = TotalItems,
            Items = ListPurchasePriceDtos
        };
    }
    protected void OnItemSelectedInAutoComplete(DetailItemDto selectedItem, CreatePurchasePriceDto dto)
    {
        if (selectedItem == null) return;
        if (selectedItem.ItemUnitOfMeasures != null)
        {
            dto.ListUnitToChoose = [.. selectedItem.ItemUnitOfMeasures.Select(x => x.Code)];
        }
        if (selectedItem.Blocked == 1)
            ViewNotifier.Show("Item was blocked", ViewNotifierType.Warning, "Operation warning");
        dto.ItemNumber = selectedItem.Number;
    }
    protected async Task<IEnumerable<DetailItemDto>> ItemSearch(string value, CancellationToken token)
    {
        try
        {
            IEnumerable<DetailItemDto> result = [];
            if (token.IsCancellationRequested) return default;

            var apiResponse = await HttpClient.GetFromJsonAsync<ApiResponseDto<List<DetailItemDto>>>(
            $"api/data/SearchItem?number={(value is null ? "" : value.Trim())}", token);

            if (!apiResponse.IsSuccessStatusCode)
            {
                return result;
            }
            result = apiResponse.Result;
            return result;
        }
        catch (OperationCanceledException)
        {
            return default;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            return default;
        }
    }

    protected void OnSearchByVendor(GetVendorDto selectedValue)
    {
        if (selectedValue == null)
        {
            return;
        }
        VendorSelectedToAdd = selectedValue;
        if (selectedValue.Blocked == 1)
            ViewNotifier.Show("Vendor was blocked", ViewNotifierType.Warning, "Operation warning");

        if (ListPurchasePriceDtoToCreate.Count == 0)
        {
            OnAddNewItemToList();
        }
    }
    protected async Task<IEnumerable<GetVendorDto>> ItemSearchInMainView(string value, CancellationToken token)
    {
        var result = new List<GetVendorDto>();
        var vendorNumber = value.Split('-')[0].Trim();
        var apiResponse = await HttpClient.GetFromJsonAsync<ApiResponseDto<List<GetVendorDto>>>(
        $"api/vendor/Search?number={vendorNumber}",
        token);
        if (!apiResponse.IsSuccessStatusCode)
        {
            return result;
        }
        result.AddRange(apiResponse.Result);
        return result;
    }
    private async Task OnClearFilterByVendor()
    {
        SelectedVendor = null;
        await TableHeaderRef.ReloadServerData();
        NavigationManager.NavigateTo("/purchase-price");
    }
    private static string GetStatusName(int status) => status switch
    {
        0 => "New",
        1 => "Saved",
        _ => "Không xác định"
    };
    private void OnPage(int index, int size)
    {
        PageSize = size;
        PageIndex = index;
    }

    protected void OnAddNewItemToList() => ListPurchasePriceDtoToCreate.Add(new CreatePurchasePriceDto
    {
        RowId = Guid.NewGuid().ToString(),
        VendorNumber = VendorSelectedToAdd.Number
    });

    protected void OnCancelAddNewPurchaseUnit()
    {
        IsShowAddFormLine = false;
        ListPurchaseUnitSelectedWhenAdd.Clear();
    }
    protected void OnRemoveNewItemsInList()
    {
        if (ListPurchaseUnitSelectedWhenAdd is not null && ListPurchaseUnitSelectedWhenAdd.Count > 0)
        {
            var listRowIdToRemove = ListPurchaseUnitSelectedWhenAdd.Select(x => x.RowId).ToList();
            var listItemToAdd = new List<CreatePurchasePriceDto>();
            listItemToAdd.AddRange(ListPurchasePriceDtoToCreate.Where(x => !listRowIdToRemove.Contains(x.RowId)));
            ListPurchasePriceDtoToCreate.Clear();
            ListPurchasePriceDtoToCreate = [.. listItemToAdd];
        }
    }
    protected async Task OnCreateNewPurchaseUnitAsync()
    {
        await addFormRef.Validate();
        if (!IsAddFormValid)
        {
            return;
        }
        try
        {
            var listErrorWhenAdd = new List<string>();
            foreach (var item in ListPurchasePriceDtoToCreate)
            {
                var resp = await PurchasePriceApiClient.CreatePurchasePriceAsync(item);
                if (!resp.IsSuccessStatusCode)
                {
                    listErrorWhenAdd.Add(item.VendorNumber + " : " + resp.Message);
                }
            }
            if (listErrorWhenAdd.Count == 0)
            {
                ViewNotifier.Show("Data added", ViewNotifierType.Success);
                ListPurchasePriceDtoToCreate.Clear();
                IsShowAddFormLine = false;
                await TableHeaderRef.ReloadServerData();
            }
            else
            {
                ViewNotifier.Show(string.Join(",", listErrorWhenAdd), ViewNotifierType.Error, L["Operation Failed"]);
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }

    private void OpenEditDrawer(GetPurchasePriceDto item)
    {
        ItemToEdit = item;
        EditDrawerOpen = true;
    }

    private async Task SaveEditAsync()
    {
        await EditForm.Validate();

        if (EditForm.IsValid)
        {

            var priceUpdateDto = new UpdatePurchasePriceDto
            {
                Number = ItemToEdit.Number,
                DiscountBySkus = ItemToEdit.DiscountBySkus,
                FoCPromotion = ItemToEdit.FoCPromotion,
                GroupProduct = ItemToEdit.GroupProduct,
                Price = ItemToEdit.PriceBefVat,
                VAT = ItemToEdit.VAT
            };
            var response = await PurchasePriceApiClient.UpdatePurchasePriceAsync(priceUpdateDto);
            if (!response.IsSuccessStatusCode)
            {
                ViewNotifier.Show(response.Message, ViewNotifierType.Error, L["Operation Failed"]);
                return;
            }
            EditDrawerOpen = false;
            await TableHeaderRef.ReloadServerData();
        }
    }
    private void CloseEditDrawer()
    {
        EditDrawerOpen = false;
        StateHasChanged();
    }
    private void OnUnitSelected(string unit, CreatePurchasePriceDto dto)
    {
        var unitExist =
            ListPurchasePriceDtoToCreate.FirstOrDefault(x => x.ItemNumber == dto.ItemNumber
                                                             && x.PurchasingUnit == unit
                                                             && x.VendorNumber == dto.VendorNumber);
        if (unitExist is not null)
        {
            ViewNotifier.Show($"Item {dto.PurchasingUnit} was already added, please select another unit ", ViewNotifierType.Error,
            "Operation error");
        }
        else
        {
            dto.PurchasingUnit = unit;
        }
    }
}
