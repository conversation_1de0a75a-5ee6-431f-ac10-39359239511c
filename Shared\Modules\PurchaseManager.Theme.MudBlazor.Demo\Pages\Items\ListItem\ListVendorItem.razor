@page "/po/vendor/items"
@inherits ListVendorItemPageBase
@attribute [Authorize]
<MudCard Elevation="0">
    <MudCardHeader Class="">
        <CardHeaderContent>
            <div>
                <MudText Typo="Typo.h5">Items</MudText>
                <MudText Typo="Typo.caption">List Items</MudText>
            </div>
        </CardHeaderContent>
        <CardHeaderActions>
            <MudTooltip Placement="Placement.Top" Text="Reload">
                <MudIconButton Icon="@Icons.Material.Filled.Refresh" OnClick="@(() => Reload())" Size="Size.Medium"
                               Class="ma-2"/>
            </MudTooltip>
        </CardHeaderActions>
    </MudCardHeader>
    <MudCardContent>

        <MudTable T="PurchaseManager.Shared.Dto.Item.DetailVendorItemDto"
                  ServerData="@(new Func<TableState, CancellationToken, Task<TableData<PurchaseManager.Shared.Dto.Item.DetailVendorItemDto>>>(ServerReload))"
                  Dense="true" FixedHeader="true" Outlined Hover Class=" pa-2" Elevation="0" LoadingProgressColor="Color.Info"
                  Loading="@isBusy" RowClass="" @ref="table">
            <ToolBarContent>
                <MudSpacer/>
                <MudTextField T="String" Clearable ValueChanged="@(s => OnSearchItemNumber(s))" Label="Item number"
                              Placeholder="@(L["Search by item number"])" @ref="SearchNumberRef" Adornment="Adornment.Start"
                              AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Small" Class="mt-0">
                </MudTextField>
                <MudTextField T="String" Clearable ValueChanged="@(s => OnSearchItemName(s))" Label="Item name"
                              Placeholder="@(L["Search by item name"])" @ref="SearchNameRef" Adornment="Adornment.Start"
                              AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Small" Class="mt-0">
                </MudTextField>
                @* <MudIconButton Icon="@Icons.Material.Filled.Clear" Color="Color.Error"
                        OnClick="@(async() => await SearchRef.Clear())" Size="Size.Medium" Class="ma-2" />
                    <MudSpacer /> *@
                </ToolBarContent>
                <HeaderContent>
                    <MudTh>
                        <MudTableSortLabel SortLabel="Number" T="PurchaseManager.Shared.Dto.Item.DetailVendorItemDto">
                            Number
                        </MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="ItemNumber" T="PurchaseManager.Shared.Dto.Item.DetailVendorItemDto">
                            @L["ItemNumber"]
                        </MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="Vat" T="PurchaseManager.Shared.Dto.Item.DetailVendorItemDto">
                            @L["Vat"]
                        </MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="LastUpdatedAt"
                            T="PurchaseManager.Shared.Dto.Item.DetailVendorItemDto">
                            @L["LastUpdatedAt"]
                        </MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="LastUpdatedBy"
                            T="PurchaseManager.Shared.Dto.Item.DetailVendorItemDto">
                            @L["LastUpdatedBy"]
                        </MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel SortLabel="Blocked" T="PurchaseManager.Shared.Dto.Item.DetailVendorItemDto">
                            @L["Blocked"]
                        </MudTableSortLabel>
                    </MudTh>
                </HeaderContent>
                <RowTemplate Context="row">
                    <MudTd DataLabel="Name">
                        <MudLink Href="@("/vendor/item/" + @row.VendorNumber + "/" + @row.ItemNumber + "/detail")">
                            @row.Name</MudLink>
                    </MudTd>
                    <MudTd DataLabel="ItemNumber">@row.ItemNumber</MudTd>
                    <MudTd DataLabel="Vat">@row.Vat</MudTd>
                    <MudTd DataLabel="LastUpdatedAt">@row.LastUpdatedAt </MudTd>
                    <MudTd DataLabel="LastUpdatedBy">@row.LastUpdatedBy </MudTd>
                    <MudTd DataLabel="Blocked">
                        <MudTooltip Placement="Placement.Top"
                            Text="@($"Item {row.ItemNumber} {(row.Blocked == 1 ? "was blocked" : " is active")}")">
                            <MudIconButton Color="@(row.Blocked == 1 ? Color.Error : Color.Success)"
                                Icon="@Icons.Material.Filled.CheckCircle">
                            </MudIconButton>
                        </MudTooltip>
                    </MudTd>
                </RowTemplate>
                <PagerContent>
                    <MudTablePager RowsPerPageString=@L["Rows per page"] />
                </PagerContent>
            </MudTable>
        </MudCardContent>
    </MudCard>
}