﻿@page "/vendors/{VendorNumberParam}/contacts"
@using PurchaseManager.Shared.Dto.Contact
@inherits VendorPage;
@attribute [Authorize]


@if (IsLoading)
{
    <MudProgressLinear Indeterminate="@IsLoading" />
}
else
{
    @if (isAuthorized)
    {
        <MudCard Elevation="0" Class="pa-4">
            <MudCardHeader>
                <CardHeaderContent>
                    <MudStack Row>
                        @if (isAdmin || isPurchaseUser)
                        {
                            <MudIconButton Color="Color.Default" Icon="@Icons.Material.Filled.ArrowBack"
                                OnClick="@OnNavigateToListVendor" Variant="Variant.Text" />
                        }
                        <MudStack Justify="Justify.Center">
                            <MudText Typo="Typo.h6">
                                @L["Contacts"]
                            </MudText>
                        </MudStack>

                    </MudStack>
                </CardHeaderContent>
                <CardHeaderActions>
                    <MudStack Justify="Justify.FlexEnd" AlignItems="AlignItems.Center">
                        <MudStack Spacing="0" AlignItems="AlignItems.End">
                            @if (!string.IsNullOrEmpty(VendorName))
                            {
                                <MudText Typo="Typo.subtitle2">
                                    <b>@VendorName</b>
                                </MudText>
                                <MudText Typo="Typo.caption">
                                    @VendorNumberParam
                                </MudText>
                            }
                        </MudStack>
                    </MudStack>
                </CardHeaderActions>
            </MudCardHeader>
            <MudCardContent>
                <MudTable Items="@listGetContactDto" Striped Dense Hover Elevation="0" Outlined
                    T="GetContactDto">
                    <ToolBarContent>
                        <MudStack Row Justify="Justify.SpaceBetween">
                            <MudTextField @bind-Value="@contactFilter.Name" @onkeyup="@OnKeyUpAsync" Label=@L["FilterByName"]
                                Margin="Margin.Dense">
                            </MudTextField>
                            @* <MudSpacer /> *@
                            <MudTextField @bind-Value="@contactFilter.Phone" @onkeyup="@OnKeyUpAsync" Label=@L["FilterByPhone"]
                                Margin="Margin.Dense">
                            </MudTextField>
                            @* <MudSpacer /> *@
                            <MudTextField @bind-Value="@contactFilter.Address" @onkeyup="@OnKeyUpAsync"
                                Label=@L["FilterByAddress"] Margin="Margin.Dense">
                            </MudTextField>
                            <MudStack Justify="Justify.FlexEnd" Class="ms-3">
                                <MudTooltip Text="Refresh">
                                    <MudIconButton OnClick="@OnReloadTableAsync" Icon="@Icons.Material.Filled.Refresh"
                                        Color="Color.Primary" Size="Size.Small" />
                                </MudTooltip>
                            </MudStack>
                        </MudStack>
                        <MudSpacer />
                        <MudButton StartIcon="@Icons.Material.Filled.Add" Size="Size.Small" OnClick="@OnNavigateToAddContact"
                            Variant="Variant.Filled" Color="Color.Success">
                            @L["New"]
                        </MudButton>
                    </ToolBarContent>
                    <HeaderContent>
                        <MudTh>@L["Contact Name"]</MudTh>
                        <MudTh>@L["Address"]</MudTh>
                        <MudTh>@L["Email"]</MudTh>
                        <MudTh>@L["Phone"]</MudTh>
                        <MudTh>@L["Tags"]</MudTh>
                        <MudTh>@L["Tax Number"]</MudTh>
                        <MudTh>@L["HasAccount"]</MudTh>
                        <MudTh>@L["Actions"]</MudTh>
                    </HeaderContent>
                    <RowTemplate Context="Contact">
                        <MudTd>
                            <MudText Inline="false">
                                <b>@Contact.Name</b>
                            </MudText>
                            <small><i>@Contact.VendorName</i></small>
                        </MudTd>
                        <MudTd>
                            <div>@Contact.Address</div>
                        </MudTd>
                        <MudTd>
                            <div>@Contact.Email</div>
                        </MudTd>
                        <MudTd>
                            <div>@Contact.Phone</div>
                        </MudTd>
                        <MudTd>
                            <div>
                                @if (RenderTags(Contact.Tags).Count > 0)
                                {
                                    @foreach (var x in RenderTags(@Contact.Tags))
                                    {
                                        <MudChip Size="Size.Small" T="String" Color="Color.Primary"> @x </MudChip>
                                    }
                                }
                            </div>
                        </MudTd>
                        <MudTd>
                            <div>@Contact.Tax</div>
                        </MudTd>
                        <MudTd>
                            <div>
                                @if (Contact.HasAccount)
                                {
                                    <MudTooltip Placement="Placement.Top" Text="Contact has account to login">
                                        <MudIcon Icon="@Icons.Material.Outlined.SupervisedUserCircle" Color="@Color.Primary" />
                                    </MudTooltip>
                                }
                            </div>
                        </MudTd>
                        <MudTd>
                            <MudStack Row>
                                <MudTooltip Placement="Placement.Top"
                                    Text="@(Contact.Block ? "Contact was blocked, click to activate" : "Contact is active, click to block")">
                                    <MudToggleIconButton Toggled="@Contact.Block"
                                        ToggledChanged="@(async _ => await OnToggleContactStatusAsync(Contact.Number))"
                                        Icon="@Icons.Material.Outlined.Check" Color="@Color.Success"
                                        ToggledIcon="@Icons.Material.Outlined.LockPerson" ToggledColor="@Color.Error"
                                        title="@(Contact.Block ? "Blocked" : "Active")" />
                                </MudTooltip>
                                <MudStack Justify="Justify.Center">
                                    <MudTooltip Text="Edit" Placement="Placement.Top" Arrow>
                                        <MudIconButton OnClick="@(_ => OnNavigateToDetailContact(Contact.Number))"
                                            Icon="@Icons.Material.Filled.Edit" Color="Color.Primary" />
                                    </MudTooltip>
                                </MudStack>
                            </MudStack>
                        </MudTd>
                    </RowTemplate>
                    <PagerContent>
                        <MudTablePager RowsPerPageString=@L["Rows per page"] />
                    </PagerContent>
                    <NoRecordsContent>
                        No Data
                    </NoRecordsContent>
                </MudTable>
            </MudCardContent>
        </MudCard>
    }
    else
    {
        <UserNotAuthorized />
    }
}
