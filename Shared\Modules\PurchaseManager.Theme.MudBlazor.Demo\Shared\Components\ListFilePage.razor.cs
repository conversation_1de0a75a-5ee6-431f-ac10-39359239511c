﻿using Microsoft.AspNetCore.Components;

using PurchaseManager.Shared.Dto.FileStorage;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.FileStorage;
using PurchaseManager.Theme.Material.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Shared.Components;

public class ListFileBasePage : ItemsTableBase<DetailFileDto>
{

    [Parameter] public string DocumentNo { get; set; }
    [Parameter] public string Prefix { get; set; }
    [Parameter] public bool IsAllowDelete { get; set; } = true;
    [Parameter] public string Caption { get; set; }

    protected HashSet<DetailFileDto> selectedItems = new HashSet<DetailFileDto>();
    protected DetailFileDto selectedFile = new DetailFileDto();
    protected bool isLoading { get; set; } = true;
    protected bool isShowPreviewFile { get; set; }
    protected FileStorageFilter fileStorageFilter { get; set; } = new FileStorageFilter();
    protected HashSet<DetailFileDto> receivedSelectedFiles = new HashSet<DetailFileDto>();

    protected override void OnInitialized()
    {
        from = "GetFiles";
        fileStorageFilter.DocumentNo = DocumentNo;
        fileStorageFilter.Prefix = Prefix;
        queryParameters = fileStorageFilter;
        base.OnInitialized();
        isLoading = false;
    }

    protected override async Task OnParametersSetAsync()
    {

        if (table is not null)
        {

            fileStorageFilter.DocumentNo = DocumentNo;
            fileStorageFilter.Prefix = Prefix;
            queryParameters = fileStorageFilter;
            await Reload();
        }
        await base.OnParametersSetAsync();
    }


    protected async Task<bool> DeleteFile(string fileId, bool isBulk = false)
    {
        var isSuccess = false;
        var resp = await apiClient.DeleteFile(fileId);
        if (resp.IsSuccessStatusCode)
        {
            if (!isBulk)
            {
                viewNotifier.Show($"File has been deleted", ViewNotifierType.Success);
                await Reload();
            }
            isSuccess = true;
        }
        else viewNotifier.Show(L["Delete file failed"], ViewNotifierType.Error);
        return isSuccess;
    }
    protected async Task DeleteAllFileAsync()
    {
        var isDeleted = true;
        selectedItems.ToList().ForEach(async item =>
        {
            isDeleted = await DeleteFile(item.FileId, true);
        });
        if (isDeleted)
        {
            viewNotifier.Show($"All File has been deleted", ViewNotifierType.Success);
            selectedItems.Clear();
            await Reload();
        }
        else viewNotifier.Show(L["Delete files failed"], ViewNotifierType.Error);
    }

    public async Task ReloadFromPage()
    {
        await Reload();
    }
    protected void ShowFilePreview(DetailFileDto item)
    {
        selectedFile = item;
        isShowPreviewFile = true;
    }
}
