namespace PurchaseManager.Shared.Dto.StockOrder;

public class EditStockOrderDto
{
    public int LineNumber { get; set; }
    public string ItemNumber { get; set; }
    public decimal QuantityRemaining { get; set; }
    public decimal QuantityReceived { get; set; }
    public decimal QuantityInvoiced { get; set; }
    public string LotNo { get; set; } = string.Empty;
    public DateOnly? ExpirationDate { get; set; }
    public string ItemName { get; set; } = string.Empty;
    public int RowId { get; set; }
    public string UOM { get; set; } = string.Empty;

}
