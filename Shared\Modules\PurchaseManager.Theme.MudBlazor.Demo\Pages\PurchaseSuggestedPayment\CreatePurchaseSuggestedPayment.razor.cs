using Microsoft.AspNetCore.Http;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.PO;
using PurchaseManager.Theme.Material.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseSuggestedPayment;
public partial class CreatePurchaseSuggestedPaymentBase : ItemsTableBase<ApproveDocumentDto>
{
    protected bool isCreatePO { get; set; }
    protected bool isLoading { get; set; }
    protected List<CheckPaymentDayResponse> listCheckPaymentDayResponse { get; set; } = [];
    protected bool isAlertDialogOpen { get; set; }
    protected HashSet<ApproveDocumentDto> selectedItems = new HashSet<ApproveDocumentDto>();
    protected override void OnInitialized()
    {
        isLoading = true;
        from = "get-approved-documents";
        isLoading = false;
        base.OnInitialized();
    }

    public async Task CheckPaymentDayBySuggestDocumentNumber()
    {
        try
        {
            var req = selectedItems
                            .Select(d => d.Number.ToString())
                            .Distinct()
                            .ToList();
            var resp = await apiClient.CheckPaymentDayBySuggestDocumentNumber(req);
            if (resp.Result.Count > 0)
            {
                listCheckPaymentDayResponse = resp.Result;
                isAlertDialogOpen = true;
            }
            else await CreatePO();
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    protected async Task CreatePO()
    {
        try
        {
            isCreatePO = true;
            if(isAlertDialogOpen) isAlertDialogOpen = false;
            await Task.Delay(3000);
            var resp = await apiClient.CreatePOFromPsp(selectedItems.ToList());
            if (resp.StatusCode != StatusCodes.Status201Created)
            {
                viewNotifier.Show(resp.Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
            else
            {
                viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation Successful"]);
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, "");
        }
        finally
        {
            isCreatePO = false;
            StateHasChanged();
        }
    }
}
