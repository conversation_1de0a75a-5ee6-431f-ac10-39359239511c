﻿@inherits RegisterPage
@using System.Text.RegularExpressions;
@page "/account/register"

@layout LoginLayout

<MudStack Justify="Justify.Center" Style="height: 98vh; min-width: 400px;">
    <EditForm Model="@registerViewModel" OnValidSubmit="@(async _ => await RegisterVendor())">
        <MudCard Elevation="2" Class="pa-8 mt-8">
            <MudCardHeader>
                <CardHeaderContent>
                    <div class="logo">
                        <MudStack Row="true" Spacing="4" Justify="Justify.Center" AlignItems="AlignItems.Center">
                            @* <a href="/" title="@appState.AppName Home"><img src=@($"{Module.ContentPath}/images/logo.svg") style="width:100px;" title="@appState.AppName Home" alt="@appState.AppName" /><br />@appState.AppName</a> *@
                            <a href="/" title="@appState.AppName Home"><img
                                    src=@($"{Module.ContentPath}/images/logo-trungson.png") style="width:96px;"
                                    title="@appState.AppName Home" alt="@appState.AppName" /></a>
                            @* <a href="/" title="@appState.AppName Home"><img
                                    src=@($"{Module.ContentPath}/images/logo-dongwha.png") style="width:96px;"
                                    title="@appState.AppName Home" alt="@appState.AppName" /></a> *@
                        </MudStack>
                        <br />
                    </div>
                    <MudText Typo="Typo.h5" Align="Align.Center">@L["Registration"]</MudText>
                </CardHeaderContent>
            </MudCardHeader>
            <MudCardContent>
                @if (!string.IsNullOrEmpty(respMsg))
                {
                    <MudAlert Severity="Severity.Warning" Class="mb-4">@respMsg</MudAlert>
                }
                @if (!string.IsNullOrEmpty(companyName))
                {
                    <MudText Align="Align.Center" Class="my-3">@companyName</MudText>
                }
                @if (isValidVendor)
                {
                    <FluentValidationValidator />
                    <MudValidationSummary />
                    <MudTextField @bind-Value="@registerViewModel.UserName" Label=@L["UserName"] Adornment="Adornment.End"
                        FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>
                    <MudTextField @bind-Value="@registerViewModel.Password" Label=@L["Password"] Adornment="Adornment.End"
                        FullWidth="true" Required="true" RequiredError=@L["Required"] InputType="InputType.Password">
                    </MudTextField>
                    <MudTextField @bind-Value="@registerViewModel.PasswordConfirm" Label=@L["Password Confirmation"]
                        Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]
                        InputType="InputType.Password">
                    </MudTextField>
                    <MudCheckBox @bind-Value="isTSEmployee" Label="Trung Son's Employee?" Color="Color.Primary">
                    </MudCheckBox>
                    @if (isTSEmployee)
                    {
                        <MudTextField @bind-Value="@registerViewModel.EmployeeCode" Label=@L["Employee Code"]
                            Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]
                            InputType="InputType.Text">
                        </MudTextField>
                    }
                }
                else
                {
                    <MudTextField T="string" TextChanged="@OnTaxCodeChanged" Converter="@Converter"
                        @bind-Value="@registerViewModel.TaxCode" Label=@L["TaxCode"] Adornment="Adornment.End"
                        FullWidth="true" Required="true" RequiredError=@L["Required"] InputType="InputType.Text">
                    </MudTextField>
                }
            </MudCardContent>
            <MudCardActions>

                @if (isValidVendor)
                {
                    <MudButton ButtonType="ButtonType.Submit" FullWidth Variant="Variant.Filled" Color="Color.Primary"
                        Class="ml-auto">
                        @L["Register"]</MudButton>
                }
                else
                {
                    <MudButton ButtonType="ButtonType.Button" FullWidth OnClick="@VerifyVendor" Variant="Variant.Filled"
                        Color="Color.Primary" Class="ml-auto">@L["Check"]</MudButton>
                }
            </MudCardActions>
            <MudButton Href="account/login" Color="Color.Primary">
                @L["AlreadyRegistered"]
            </MudButton>
        </MudCard>
    </EditForm>
</MudStack>

@code {
    private MudBlazor.Converter<string> Converter => new()
        {
            SetFunc = value =>
            {
                return Regex.Replace(value ?? string.Empty, @"[^0-9-]", "");
            },
            GetFunc = value =>
            {
                return value;
            }
        };
}
