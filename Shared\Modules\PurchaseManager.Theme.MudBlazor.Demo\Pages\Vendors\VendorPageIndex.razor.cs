﻿using Humanizer;
using Microsoft.AspNetCore.Components;
using MudBlazor;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models;
using PurchaseManager.Theme.Material.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Pages.Vendors;

public class VendorPageBase : ItemsTableBase<Vendor>
{
    [Inject]
    protected NavigationManager NavigationManager { get; set; }
    private readonly VendorFilter _vendorFilter = new VendorFilter();
    protected MudTextField<string> SearchByVendorNameRef { get; set; }
    protected MudTextField<string> SearchByVendorPhoneRef { get; set; }
    protected MudTextField<string> SearchByItemNumberRef { get; set; }

    protected override void OnInitialized()
    {
        from = "Vendors";
        queryParameters = _vendorFilter;
        base.OnInitialized();
    }

    protected override async Task LoadItems()
    {
        try
        {
            isBusy = true;

            await InvokeAsync(StateHasChanged);


            masterDataApiClient.ClearEntitiesCache();

            from ??= $"{nameof(Vendor)}".Pluralize();

            var prop = queryParameters?.GetType().GetProperties().SingleOrDefault(i => i.Name == "Filter");

            prop?.SetValue(queryParameters, filter);


            var result = await masterDataApiClient.GetItemsByFilter<Vendor>(from, orderByDefaultField, filter, orderBy, orderByDescending, pageSize, pageIndex * pageSize, queryParameters?.ToDictionary());

            items = [.. result];
            if (result.InlineCount != null)
            {
                totalItemsCount = (int)result.InlineCount.Value;
            }

            if (totalItemsCount == 0 && filter == null)
            {
                viewNotifier.Show(L["Data not available."], ViewNotifierType.Info);
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        finally
        {
            isBusy = false;

            await InvokeAsync(StateHasChanged);
        }
    }
    protected async Task OnSearchByVendorName(string text)
    {
        _vendorFilter.Name = text;
        apiClient.ClearEntitiesCache();
        await Reload();
    }
    protected async Task OnSearchByVendorPhone(string text)
    {
        _vendorFilter.Phone = text;
        apiClient.ClearEntitiesCache();
        await Reload();
    }
    protected async Task OnSearchByVendorNumber(string text)
    {
        _vendorFilter.Number = text;
        apiClient.ClearEntitiesCache();
        await Reload();
    }
    protected void OnNavigationToVendorContacts(string vendorNumber) => NavigationManager.NavigateTo($"/vendors/{vendorNumber}/contacts");
}
