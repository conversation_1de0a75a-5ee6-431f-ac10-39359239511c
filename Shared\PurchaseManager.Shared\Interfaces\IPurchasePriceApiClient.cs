using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Models.PurchasePrice;
namespace PurchaseManager.Shared.Interfaces;

public interface IPurchasePriceApiClient
{
    Task<ApiResponseDto<List<GetPurchasePriceDto>>> GetPurchasePriceAsync(string itemNumber, string vendorNumber);
    Task<ApiResponseDto> CreatePurchasePriceAsync(CreatePurchasePriceDto createPurchasePriceDto);
    Task<ApiResponseDto<PagedResultDto<GetPurchasePriceDto>>> GetPurchasePriceByFilterAsync(PurchasePriceFilter filter);
    Task<ApiResponseDto<GetPurchasePriceDto>> GetPurchasePriceByPriceNumberAsync(string priceNumber);
    Task<ApiResponseDto> UpdatePurchasePriceAsync(UpdatePurchasePriceDto updatePurchasePriceDto);
    Task<ApiResponseDto> DeletePurchasePriceByPriceNumberAsync(string priceNumber);


}
