﻿namespace PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;

public class DetailPurchaseSuggestedPaymentLineDto
{
    public int DocumentType { get; set; }
    public string DocumentNumber { get; set; }
    public int LineNumber { get; set; }
    public string BuyFromVendorNumber { get; set; }
    public int Type { get; set; }
    public string Number { get; set; }
    public string LocationCode { get; set; }
    public string UnitOfMeasure { get; set; }
    public decimal Quantity { get; set; }
    public string Vat { get; set; }
    public int Status { get; set; }
    public int QtyPerUnitOfMeasure { get; set; }
    public string LotNumber { get; set; }
    public int RequestQuantity { get; set; } = 0;
    public int ConfirmQuantity { get; set; } = 0;
    public string ReasonCode { get; set; }
    public DateOnly? ExpirationDate { get; set; }
    public string ItemName { get; set; }
    public string ReasonName { get; set; }
    public string Tags { get; set; }
    public string Rate { get; set; }
}
