﻿using FluentValidation;

using Microsoft.Extensions.Localization;

using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Validators;

namespace PurchaseManager.Shared.Dto.Db.Validators;
public class CreateItemDtoValidator : LocalizedAbstractValidator<CreateItemDto>
{
    public CreateItemDtoValidator(IStringLocalizer<Global> l) : base(l)
    {
        RuleFor(p => p.Name)
            .NotEmpty()
            .MaximumLength(160).WithName(L["Name"]);
        RuleFor(p => p.Name)
           .NotEmpty()
           .MaximumLength(160).WithName(L["Description"]);
    }
}
