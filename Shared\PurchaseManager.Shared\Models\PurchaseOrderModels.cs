﻿using System.ComponentModel.DataAnnotations;
using System.Globalization;
namespace PurchaseManager.Shared.Models;

public class PurchaseOrderModels
{
    public class Header
    {
        public class FilterGetsHeader//Required for filter all header
        {
            [Required]
            public DateTime FromDate { get; set; } = DateTime.Today;
            [Required]
            public DateTime ToDate { get; set; } = DateTime.Today;
            [Required(ErrorMessage = "Title is required")]
            public int PageIndex { get; set; }
            [Required(ErrorMessage = "Title is required")]
            public int PageSize { get; set; }
        }
        public class FullUpdateHeader//Required for update header
        {
            [Required]
            public string Number { get; set; } = null!;
            [Required]
            public string VendorNo_ { get; set; } = null!;
            [Required]
            public string OrderDate { get; set; } = DateTime.Today.ToString(CultureInfo.InvariantCulture);//không sử dụng trên API
            [Required]
            public string DueDate { get; set; } = DateTime.Today.ToString(CultureInfo.InvariantCulture);
            public string? PurchaseUser { get; set; }
            public string? PostingDescription { get; set; }
            public string? VendorApprovalBy { get; set; }
            public string? PurchaserApprovalBy { get; set; }
            public int DocNoOccurrence { get; set; }
            public string? YourReference { get; set; }
        }
    }

    public class Line
    {
        public class FullInsertLine//Required for add new line and update line
        {
            [Required]
            public string DocumentNumber { get; set; } = null!;
            [Required]
            public int LineNumber { get; set; }
            [Required]
            public int DocumentType { get; set; }
            [Required]
            public string ItemNumber { get; set; } = null!;
            [Required]
            public string UnitOfMeasure { get; set; } = null!;
            /// <summary>
            /// Số lượng đặt hàng
            /// </summary>
            [Required]
            public decimal Quantity { get; set; }
            /// <summary>
            /// Còn lại
            /// </summary>
            [Required]
            public decimal QuantityToReceive { get; set; }
            /// <summary>
            /// Đã nhận hàng
            /// </summary>
            [Required]
            public decimal QuantityReceived { get; set; }
            [Required]
            public decimal Vat { get; set; }
            [Required]
            public decimal LineDiscountPercent { get; set; }
            [Required]
            public decimal LineDiscountAmount { get; set; }
            [Required]
            public decimal Amount { get; set; }
            [Required]
            public decimal AmountIncludingVat { get; set; }
            [Required]
            public decimal ProfitPercent { get; set; }
            [Required]
            public decimal Vatamount { get; set; }
            [Required]
            public decimal VatbaseAmount { get; set; }
            [Required]
            public decimal UnitCost { get; set; }
            [Required]
            public string LotNo { get; set; } = null!;
            [Required]
            public DateTime ExpirationDate { get; set; }
            [Required]
            public decimal LastUnitCost { get; set; }
            public string? Desc { get; set; }
            public int RowID { get; set; }
        }

        public class FileData
        {
            public byte[] ImageBytes { get; set; }
            public string FileName { get; set; }
            public string FileType { get; set; }
            public long FileSize { get; set; }
        }
    }
}
