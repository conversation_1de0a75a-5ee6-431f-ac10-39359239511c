using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Controllers;
// [Authorize]
// [SecurityHeaders]
[Route("api/[controller]")]
[ApiController]
public class SyncController : ControllerBase
{
        private readonly IGrpcManager _syncService;
        private readonly IStringLocalizer<Global> L;


        public SyncController(IGrpcManager syncService, IStringLocalizer<Global> l)
        {
                _syncService = syncService;
                L = l;

        }

        [HttpPost("sync-items")]
        public async Task<ApiResponse> SyncItems(SyncRequest syncRequest, CancellationToken cancellationToken)
                => ModelState.IsValid ? await _syncService.SyncItem(syncRequest, cancellationToken) : new ApiResponse(Status400BadRequest, L["Token is null, Try again"]);
        [HttpPost("sync-vendor")]
        public async Task<ApiResponse> SyncVendor(SyncRequest syncRequest, CancellationToken cancellationToken)
                => ModelState.IsValid ? await _syncService.SyncVendor(syncRequest, cancellationToken) : new ApiResponse(Status400BadRequest, L["Token is null, Try again"]);
        [HttpPost("sync-iuom")]
        public async Task<ApiResponse> SyncIUOM(SyncRequest syncRequest, CancellationToken cancellationToken)
                => ModelState.IsValid ? await _syncService.SyncIUOM(syncRequest, cancellationToken) : new ApiResponse(Status400BadRequest, L["Token is null, Try again"]);
        [HttpPost("sync-uom")]
        public async Task<ApiResponse> SyncUOM(SyncRequest syncRequest, CancellationToken cancellationToken)
                => ModelState.IsValid ? await _syncService.SyncUOM(syncRequest, cancellationToken) : new ApiResponse(Status400BadRequest, L["Token is null, Try again"]);

}
