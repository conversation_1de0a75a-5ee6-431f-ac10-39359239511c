﻿using AutoMapper;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Storage.Mapping;

public class POMappingProfile : Profile
{
    public POMappingProfile()
    {
        CreateMap<CreatePOHeaderDto, PurchaseOrderHeader>().ReverseMap();
        CreateMap<UpdatePOHeaderDto, PurchaseOrderHeader>().ReverseMap();

        CreateMap<POLineGetDto, PurchaseOrderLine>().ReverseMap();
    }
}
