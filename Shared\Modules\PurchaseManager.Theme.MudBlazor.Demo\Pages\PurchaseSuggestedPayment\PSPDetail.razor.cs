using System.Security.Claims;
using AutoMapper;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using ObjectCloner.Extensions;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Models.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Providers;
using PurchaseManager.Theme.Material.Demo.Shared.Components;
using PurchaseManager.Theme.Material.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseSuggestedPayment;
public partial class PSPDetailBase : ItemsTableBase<DetailPurchaseSuggestedPaymentLineDto>
{
    [Parameter]
    public string PSPNumber { get; set; }
    [Inject] AuthenticationStateProvider authStateProvider { get; set; } = default;
    protected bool isShowAddFormLine;
    protected bool isLoading { get; set; }
    protected bool isProcessing { get; set; }
    protected bool isEditing { get; set; } = false;
    protected UserViewModel userViewModel { get; set; } = new();
    protected ClaimsPrincipal claimsPrincipal { get; set; } = new();
    protected DetailPurchaseSuggestedPaymentHeaderDto pSPHeader { get; set; }
    protected List<DetailPurchaseSuggestedPaymentLineDto> listPSPLine { get; set; } = [];
    protected List<DetailPurchaseSuggestedPaymentLineDto> backupItem { get; set; } = []; // original items
    protected DetailPurchaseSuggestedPaymentLineDto pSPLineToCreate { get; set; } =
        new DetailPurchaseSuggestedPaymentLineDto();
    protected MudDatePicker orderDateRef { get; set; } = new MudDatePicker();

    protected IEnumerable<DetailDemandReasonDto> listDetailDemandReasonDto { get; set; } = [];
    protected MudDatePicker dueDateRef { get; set; } = new MudDatePicker();
    protected DateTime? orderDate { get; set; } = DateTime.Now;
    protected DateTime? dueDate { get; set; } = DateTime.Now;
    protected string selectedTagsString { get; set; } = "";
    protected bool isPurchaseManager { get; set; }
    protected bool isPurchaser { get; set; }
    protected MudNumericField<int> requestQuantityRef { get; set; } = new MudNumericField<int>();
    protected bool isWarningQuantityDialogOpen { get; set; }
    protected IEnumerable<string> tagsToSelect { get; set; } = new HashSet<string>();
    protected DetailDemandReasonDto? detailDemandReasonDto { get; set; } = new DetailDemandReasonDto();
    protected ListFileBasePage listFilePageRef { get; set; }
    protected DetailPurchaseSuggestedPaymentLineDto _currentLine;
    protected PurchaseSuggestedPaymentLineFilter filter { get; set; } = new();
    [Inject] protected IMapper _mapper { get; set; }
    protected override async Task OnInitializedAsync()
    {
        isLoading = true;
        filter.Tag = null;
        queryParameters = filter;
        await LoadPSPHeaderDto();
        from = $"get-line/{pSPHeader.Number}";
        await LoadReason();
        await base.OnInitializedAsync();
        isLoading = false;
    }

    protected async Task OnFilterByTagsChanged(string value)
    {
        try
        {
            filter.Tag = value == "" ? value : null;
            if (!string.IsNullOrEmpty(value))
            {
                var listTags = value.Split(",").OrderBy(x => x.Trim().ToLower()).Select(x => x.Trim()).ToList();
                if (listTags.Count() > 0) filter.Tag = string.Join(", ", listTags);
                apiClient.ClearEntitiesCache();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("Tags filter has error" + ex.Message);
        }
        finally
        {
            if (value != null) await Reload();
        }
    }

    protected async Task OnClearFilterByTags()
    {
        filter.Tag = null;
        selectedTagsString = "";
        StateHasChanged();
        await Reload();
    }
    protected override async Task OnParametersSetAsync()
    {
        userViewModel = null;
        claimsPrincipal = (await authenticationStateTask).User;
        isPurchaser = claimsPrincipal.IsPurchaseUser();
        isPurchaseManager = claimsPrincipal.IsPurchaseManager();
        if (claimsPrincipal.Identity.IsAuthenticated)
            userViewModel = await ((IdentityAuthenticationStateProvider)authStateProvider).GetUserViewModel();
    }

    protected void LoadTagsFilter()
    {
        // backupItem = items.DeepClone();
        var tags = items.Where(item => item.Tags != null)
                       .SelectMany(item => item.Tags.Split(','))
                       .Select(tag => tag.Trim())
                       .Distinct()
                       .OrderBy(x => x.ToLower())
                       .ToList();
        if (tagsToSelect.Count() == 0) tagsToSelect = tags;
    }
    protected async Task LoadReason()
    {
        try
        {
            var resp = await apiClient.GetAllDemandReason();
            if (resp.IsSuccessStatusCode)
            {
                listDetailDemandReasonDto = resp.Result;
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["Operation failed!"]);
        }
    }
    protected async Task LoadPSPHeaderDto()
    {
        try
        {
            var resp = await apiClient.GetPurchaseSuggestedPaymentHeaderByDocumentNo(
                PSPNumber,
                new PurchaseSuggestedPaymentHeaderFilter()
            );
            if (resp is null)
                viewNotifier.Show("No data", ViewNotifierType.Error, L["Operation failed!"]);
            else
            {
                pSPHeader = resp.Result;
                dueDate = pSPHeader.DueDate;
                orderDate = pSPHeader.OrderDate;
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["Operation failed!"]);
        }
    }
    protected void ResetItemToOriginalValues(object element)
    {
        _mapper.Map(_currentLine, element);
    }
    protected void BackupItem(object element)
    {
        _currentLine = (DetailPurchaseSuggestedPaymentLineDto)element.DeepClone(); // _mapper.Map<PurchaseOrderDto.Line.Get>(element);
    }

    protected void UpdateRate(string number, int requestQuantity, decimal quantity)
    {
        try
        {
            var foundItem = items.Where(x => x.Number == number).FirstOrDefault();
            if (foundItem == null) return;
            if (foundItem.RequestQuantity != 0)
            {
                var rate = Math.Round(requestQuantity / quantity * 100, 2, MidpointRounding.ToEven);
                if (rate >= 200) isWarningQuantityDialogOpen = true;
                foundItem.Rate = rate + "";
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("Has error while update Rate!!!" + ex);
        }
    }


    protected async Task ItemHasBeenCommitted(object element)
    {
        DetailPurchaseSuggestedPaymentLineDto dto = (DetailPurchaseSuggestedPaymentLineDto)element;
        try
        {
            var req = new UpdatePurchaseSuggestedPaymentLineDto()
            {
                Rate = dto.Rate,
                Number = dto.Number,
                Quantity = dto.Quantity,
                ReasonCode = dto.ReasonCode,
                LineNumber = dto.LineNumber,
                DocumentType = dto.DocumentType,
                UnitOfMeasure = dto.UnitOfMeasure,
                DocumentNumber = dto.DocumentNumber,
                ExpirationDate = dto.ExpirationDate,
                RequestQuantity = dto.RequestQuantity,
                // RequestQuantity = -1,
                ConfirmQuantity = dto.ConfirmQuantity,
                BuyFromVendorNumber = dto.BuyFromVendorNumber,
            };
            var resp = await apiClient.UpdatePurchaseSuggestedPaymentLine(req);
            if (resp.IsSuccessStatusCode)
            {
                viewNotifier.Show("Update successfully ", ViewNotifierType.Success);
            }
            else
                viewNotifier.Show("Update failed: " + resp.Result, ViewNotifierType.Error);
        }
        catch (Exception e)
        {
            viewNotifier.Show("Update failed: " + e.GetBaseException().Message, ViewNotifierType.Error);
        }
        await Reload();
    }
    protected async Task ToggleHeaderStatus()
    {
        try
        {
            if (!isEditing)
            {
                var resp = await apiClient.OpenPurchaseSuggestedPayment(PSPNumber);
                if (resp.IsSuccessStatusCode)
                {
                    isEditing = true;
                }
                viewNotifier.Show(resp.Message, ViewNotifierType.Info, "");
            }
            else
            {
                var resp = await apiClient.ClosePurchaseSuggestedPayment(PSPNumber);
                if (resp.IsSuccessStatusCode)
                {
                    isEditing = false;
                }
                viewNotifier.Show(resp.Message, ViewNotifierType.Info, "");
            }
        }
        catch (Exception e)
        {
            viewNotifier.Show($"Open document is fail: " + e.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        finally
        {
            StateHasChanged();
        }
    }
    protected async Task Approve()
    {
        try
        {
            var resp = await apiClient.ApprovePurchaseSuggestedPayment(PSPNumber);
            viewNotifier.Show(resp.Message, ViewNotifierType.Info, "");
        }
        catch (Exception e)
        {
            viewNotifier.Show($"Approve document is fail: " + e.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
}
