﻿using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using PurchaseManager.Shared.Models;

namespace PurchaseManager.Shared.Services
{
    public class MasterDataApiClient : BaseApiClient, IMasterDataApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly NavigationManager _navigationManager;
        private readonly IJSRuntime _jsRuntime;

        public MasterDataApiClient(NavigationManager navigationManager, HttpClient httpClient, ILogger<MasterDataApiClient> logger, IJSRuntime jsRuntime) :
            base(httpClient, logger, "api/vendor/")
        {
            _navigationManager = navigationManager;
            _httpClient = httpClient;
            _jsRuntime = jsRuntime;
        }

        public async Task<ApiResponseDto<GetVendorDto>> BuildVendorViewModel(string number)
        {
            return await _httpClient.PostJsonAsync<ApiResponseDto<GetVendorDto>>("api/Vendor/BuildViewModel?number=" + number, number);
        }

        public async Task<ApiResponseDto<bool>> ToggleVendorStatus(string vendorNumber)
        {
            return await _httpClient.PostJsonAsync<ApiResponseDto<bool>>("api/Vendor/toggle-status", vendorNumber);
        }


        public async Task<ApiResponseDto> UpdateVendor(GetVendorDto vendorViewModel)
        {
            return await _httpClient.PutJsonAsync<ApiResponseDto>("api/Vendor", vendorViewModel);
        }
    }
}
