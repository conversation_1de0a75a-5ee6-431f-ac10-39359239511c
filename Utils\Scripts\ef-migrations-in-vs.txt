Add-Migration -StartupProject "PurchaseManager.Server" -Project "PurchaseManager.Storage" -Name CreateLocalizationDb -Context LocalizationDbContext -Verbose -OutputDir "Migrations/LocalizationDb"
Add-Migration -StartupProject "PurchaseManager.Server" -Project "PurchaseManager.Storage" -Name CreateTenantStoreDb -Context TenantStoreDbContext -Verbose -OutputDir "Migrations/TenantStoreDb"
Add-Migration -StartupProject "PurchaseManager.Server" -Project "PurchaseManager.Storage" -Name CreateApplicationDb -Context ApplicationDbContext -Verbose -OutputDir "Migrations/ApplicationDb"
