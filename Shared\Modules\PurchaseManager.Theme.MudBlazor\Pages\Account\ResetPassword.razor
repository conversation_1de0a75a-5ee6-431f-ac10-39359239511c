﻿@inherits ResetPasswordPage
@page "/account/resetpassword"
@page "/account/resetpassword/{UserId}"
@layout LoginLayout


<EditForm Model="@resetPasswordViewModel" OnValidSubmit="@SendResetPassword">
    <MudCard Elevation="2" Class="pa-4">
        <MudCardHeader>
            <CardHeaderContent>
                <MudIconButton Icon="@Icons.Material.Filled.Home" Class="ml-auto" Href="/" />
                <div class="logo">
                    <a href="/" title="@appState.AppName Home"><img src=@($"{Module.ContentPath}/images/logo.svg") style="width:100px;" title="@appState.AppName Home" alt="@appState.AppName" /><br />@appState.AppName</a>
                    <br />
                </div>
                <MudText Typo="Typo.h5" Align="Align.Center">@L["Password Reset"]</MudText>
            </CardHeaderContent>
        </MudCardHeader>
        <MudCardContent>
            <FluentValidationValidator />
            <MudValidationSummary />
            <MudTextField @bind-Value="@resetPasswordViewModel.Password" Label=@L["Password"] AdornmentIcon="@Icons.Material.Outlined.Lock" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"] InputType="InputType.Password"></MudTextField>

            <MudTextField @bind-Value="@resetPasswordViewModel.PasswordConfirm" Label=@L["Password Confirmation"] AdornmentIcon="@Icons.Material.Outlined.Lock" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"] InputType="InputType.Password"></MudTextField>
        </MudCardContent>
        <MudCardActions>
            <MudButton ButtonType="ButtonType.Submit" Variant="Variant.Filled" Color="Color.Primary" Class="ml-auto">@L["Reset Password"]</MudButton>
        </MudCardActions>
        <MudButton Href="/" Color="Color.Primary">
            @L["Cancel"]
        </MudButton>
    </MudCard>
</EditForm>

@code {

}
