﻿namespace PurchaseManager.Shared.Dto.FileStorage;

public class CreateFileDto
{
    public string FileId { get; set; } = null!;
    public string FileName { get; set; }
    public string FileType { get; set; }
    public string FileUrl { get; set; }
    public DateTime UploadDate { get; set; }
    public string Description { get; set; }
    public bool Blocked = false;
    public StatusFileEnum Status { get; set; }
    public string DocumentNo { get; set; }
    public string Prefix { get; set; }
}
