﻿namespace PurchaseManager.Shared.Helpers;

public static class PurchasePriceCalculator
{
    public static decimal CalcPriceAftVat(decimal price, decimal vat)
        => Math.Round(price * (1 + vat / 100), 2);
    public static decimal CalcPriceAftDiscountNoVat(decimal price, decimal discount)
        => Math.Round(price * (1 - discount / 100), 2);
    public static decimal CalcPriceAftDiscountVat(decimal price, decimal vat, decimal discount)
        => Math.Round(price * (1 + vat / 100) * (1 - discount / 100), 2);
}
