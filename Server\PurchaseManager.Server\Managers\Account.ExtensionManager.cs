﻿using System.Security.Claims;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.AuthorizationDefinitions;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Extensions;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Storage;
using PurchaseManager.Theme.Material;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Managers;

public partial class AccountManager
{
    private const string Business = "CONTACT";
    private const string Branch = "AL";
    private readonly IAdminManager _adminManager;
    private readonly ApplicationDbContext _dbContext;
    public async Task<ApiResponse> RegisterAccountVendor(RegisterVendorViewModel parameters)
    {
        if (parameters == null)
        {
            return new ApiResponse(Status400BadRequest, L["Invalid parameters"]);
        }
        var user = _userManager.Users.FirstOrDefault(x => x.UserName == parameters.UserName);
        if (user != null)
        {
            return new ApiResponse(Status400BadRequest, L["Account already exists"]);
        }
        if (parameters.Password != parameters.PasswordConfirm)
        {
            return new ApiResponse(Status400BadRequest, L["Passwords do not match"]);
        }
        var vendor = _dbContext.Vendors.FirstOrDefault(x
            => x.VatregistrationNumber == parameters.TaxCode && x.Blocked == VendorBlockedType.NoBlock);
        if (vendor == null)
        {
            return new ApiResponse(Status400BadRequest, L["Vendor not exists"]);
        }
        var email = parameters.UserName.ToLower() + "@trungsonpharma.com";
        var contactNumber = await _adminManager.CreateNumberSeries(Business, Branch);

        var newContact = new Contact
        {
            Number = contactNumber,
            Name = parameters.UserName.ToLower(),
            Address = vendor.Address,
            Phone = parameters.UserName,
            Tax = string.Empty,
            Email = email,
            Block = false,
            Tags = string.Empty,
            VendorNumber = vendor.Number
        };
        _dbContext.Contacts.Add(newContact);

        await _dbContext.SaveChangesAsync();
        await RegisterNewVendorAsync(parameters, email, contactNumber, vendor.Number);

        return new ApiResponse(201, L["Vendor created successfully"]);
    }
    public async Task<ApiResponse> CheckTaxCode(string taxCode)
    {
        var vendor = await _dbContext.Vendors.FirstOrDefaultAsync(x => x.VatregistrationNumber == taxCode);
        return vendor == null ? new ApiResponse(Status400BadRequest, L["Vendor already exists"])
            : new ApiResponse(200, L["Vendor exists"], vendor.Name);
    }
    public async Task<ApiResponse> GetsAccountVendor(AccountVendorFilter filter)
    {
        var role = await _roleManager.FindByNameAsync("VENDOR");
        if (role == null)
        {
            throw new DomainException("Role VENDOR not found");
        }

        var query = _userManager.Users
            .Include(u => u.UserRoles)
            .Where(u =>
                u.UserRoles.Any(ur => ur.Role.Name == role.Name) &&
                u.ContactCode != null &&
                (filter.UserName == null || u.UserName.Contains(filter.UserName!)) &&
                (filter.AllBlocked == null || u.Block == filter.AllBlocked) &&
                (filter.AllDeactivate == null || u.EmailConfirmed == !filter.AllDeactivate)
            );

        var totalRecords = await query.CountAsync();

        if (filter.PageSize == 0)
        {
            filter.PageSize = 10;
        }

        var pagedUsers = await query
            .Skip(filter.PageSize * filter.PageIndex)
            .Take(filter.PageSize)
            .ToListAsync();

        var vendors = pagedUsers.Select(user => new UserVendorViewModel
        {
            UserName = user.UserName,
            IsBlock = user.Block,
            ContactCode = user.ContactCode,
            EmployeeCode = user.EmployeeCode,
            IsActive = user.EmailConfirmed,
            VendorName = user.ContactCode == null ? string.Empty : _dbContext.Contacts
                .Include(c => c.VendorNumberNavigation!)
                .Where(c => c.Number == user.ContactCode)
                .Select(c => c.VendorNumberNavigation!.Name)
                .FirstOrDefault()!,
            VendorNumber = user.ContactCode == null ? string.Empty : _dbContext.Contacts
                .Include(c => c.VendorNumberNavigation!)
                .Where(c => c.Number == user.ContactCode)
                .Select(c => c.VendorNumberNavigation!.Number)
                .FirstOrDefault()!,
            TaxCode = user.ContactCode == null ? string.Empty : _dbContext.Contacts
                .Include(c => c.VendorNumberNavigation!)
                .Where(c => c.Number == user.ContactCode)
                .Select(c => c.VendorNumberNavigation!.VatregistrationNumber)
                .FirstOrDefault()!
        }).OrderBy(x => x.IsActive)
        .ToList();

        return new ApiResponse(200, L["Operation successful"], new PagedResult<UserVendorViewModel>
        {
            Data = vendors,
            CurrentPage = filter.PageIndex,
            PageSize = filter.PageSize,
            RowCount = totalRecords,

        });
    }
    public async Task<ApiResponse> GetsAccountVendor(AccountUserFilter filter)
    {
        var query = _userManager.Users
            .Include(u => u.UserRoles)
            .Where(u =>
                u.ContactCode != null &&
                (filter.UserName == null || u.UserName.Contains(filter.UserName!)) &&
                (filter.AllBlocked == null || u.Block == filter.AllBlocked) &&
                (filter.AllDeactivate == null || u.EmailConfirmed == !filter.AllDeactivate)
            );

        var totalRecords = await query.CountAsync();

        if (filter.PageSize == 0)
        {
            filter.PageSize = 10;
        }

        var pagedUsers = await query
            .Skip(filter.PageSize * filter.PageIndex)
            .Take(filter.PageSize)
            .ToListAsync();

        return new ApiResponse(200, L["Operation successful"], new PagedResult<ApplicationUser>
        {
            Data = pagedUsers,
            CurrentPage = filter.PageIndex,
            PageSize = filter.PageSize,
            RowCount = totalRecords

        });
    }

    public async Task<ApiResponse> ActiveAccountVendor(string userName)
    {
        var account = _userManager.Users.FirstOrDefault(x => x.UserName == userName);
        if (account == null)
        {
            return new ApiResponse(Status400BadRequest, L["Account not found"]);
        }
        account.EmailConfirmed = true;
        await _dbContext.SaveChangesAsync();
        return new ApiResponse(200, L["Account activated successfully"]);
    }
    public async Task<ApiResponse> BlockAccountVendor(string userName)
    {
        var account = _userManager.Users.FirstOrDefault(x => x.UserName == userName);
        if (account == null)
        {
            return new ApiResponse(Status400BadRequest, L["Account not found"]);
        }
        account.Block = !account.Block;

        await _dbContext.SaveChangesAsync();
        return new ApiResponse(200, L[$"Account {(account.Block ? "blocked" : "no-blocked")} successfully"]);
    }

    private async Task RegisterNewVendorAsync(RegisterVendorViewModel parameters, string email, string contactCode = null, string vendorCode = null)
    {
        var user = new ApplicationUser
        {
            UserName = parameters.UserName.ToLower(),
            Email = email,
            VendorCode = vendorCode,
            ContactCode = contactCode,
            EmployeeCode = parameters.EmployeeCode
        };

        await RegisterNewVendorAsync(user, parameters.Password);
    }
    private async Task RegisterNewVendorAsync(ApplicationUser user, string password)
    {
        var result = password == null ?
            await _userManager.CreateAsync(user) :
            await _userManager.CreateAsync(user, password);

        if (!result.Succeeded)
        {
            throw new DomainException(result.GetErrors());
        }

        await _userManager.AddClaimsAsync(user, new[]
        {
            new Claim(Policies.IsVendor, string.Empty),
            new Claim(ClaimTypes.Name, user.UserName!),
            new Claim(ClaimTypes.Email, user.Email!),
            new Claim(ApplicationClaimTypes.EmailVerified, ClaimValues.FalseString, ClaimValueTypes.Boolean)
        });
        var role = await _roleManager.FindByNameAsync("VENDOR");
        if (role == null)
        {
            throw new DomainException("Role VENDOR not found");
        }
        await _userManager.AddToRoleAsync(user, role.Name!);
    }

    public async Task<ApiResponse> ResetVendorPassword(string userName)
    {
        try
        {
            var user = await _userManager.FindByNameAsync(userName);
            if (user is null)
            {
                return new ApiResponse(404, L["Account not found"]);
            }
            // For more information on how to enable account confirmation and password reset please visit http://go.microsoft.com/fwlink/?LinkID=532713
            var token = await _userManager.GeneratePasswordResetTokenAsync(user);

            // reset password
            var defaultPassword = "123456@";
            var result = await _userManager.ResetPasswordAsync(user, token, defaultPassword);

            if (result.Succeeded)
            {
                _logger.LogError($"Fail to reset password for user {user.UserName}");
                return new ApiResponse(Status200OK, L["Vendor account {0}'s password was reset to `{1}`", user.UserName, defaultPassword]);
            }
            else
            {
                var msg = result.GetErrors();
                _logger.LogWarning("Error while resetting the password: {0}", msg);
                return new ApiResponse(Status400BadRequest, msg);
            }
        }
        catch (Exception ex)
        {
            return new ApiResponse(Status400BadRequest, L["Error while resetting the password: {0}", ex.GetBaseException().Message]);
        }
    }
}
