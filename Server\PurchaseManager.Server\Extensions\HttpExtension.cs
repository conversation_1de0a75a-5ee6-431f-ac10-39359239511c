using Grpc.Net.Client;

namespace PurchaseManager.Server.Extensions;

public static class HttpExtension
{
    public static HttpClient SetHeader(Dictionary<string, string> headers, HttpClient httpClient = null)
    {
        var defaultHttpClient = httpClient ?? new HttpClient();
        foreach (var header in headers)
        {
            defaultHttpClient.DefaultRequestHeaders.Add(header.Key, header.Value);
        }
        return defaultHttpClient;
    }
    public static GrpcChannel GetGrpcChannel(string channelUrl, string token)
    {
        var channel = GrpcChannel.ForAddress(channelUrl, new GrpcChannelOptions
        {
            HttpClient = SetHeader(new Dictionary<string, string>() { { "Authorization", token } }),
        });
        return channel;
    }
}
