﻿namespace PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;

public class UpdatePurchaseSuggestedPaymentHeaderDto
{
    public string Number { get; set; }
    public string BuyFromVendorNumber { get; set; }
    public string PayToVendorNumber { get; set; }
    public string PayToName { get; set; }
    public string PayToAddress { get; set; }
    public DateTime OrderDate { get; set; }
    public DateTime PostingDate { get; set; }
    public DateTime ExpectedReceiptDate { get; set; }
    public DateTime DueDate { get; set; }
    public string PurchaserCode { get; set; }// purchaser username 
    public DateTime DocumentDate { get; set; }
    public string PaymentMethodCode { get; set; }//CASH OR BANK
}
