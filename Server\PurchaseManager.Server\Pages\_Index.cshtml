﻿@page "/"
@namespace PurchaseManager.Server.Pages
@using PurchaseManager.Infrastructure.Server
@using PurchaseManager.Shared.Models
@using PurchaseManager.Shared.Providers
@using Microsoft.AspNetCore.Hosting
@using Microsoft.AspNetCore.Localization
@using System.Globalization
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers
@addTagHelper *, PurchaseManager.Shared
@inject IWebHostEnvironment env
@inject ITenantSettings<MainConfiguration> mainConfiguration

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <meta name="author" content="Enkodellc / <PERSON>, <PERSON>">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-touch-fullscreen" content="yes">
    @if (mainConfiguration.Value.Runtime == Shared.Dto.Db.BlazorRuntime.Server)
    {
        <title>Purchase Manager (Server)</title>
    }
    else
    {
        <title>Purchase Manager</title>
    }
    <base href="~/" />
    <link href="manifest.json" rel="manifest" />
</head>
<body>
    @{
        if (HttpContext.Request.Cookies[CookieRequestCultureProvider.DefaultCookieName] == null)
            HttpContext.Response.Cookies.Append(
               CookieRequestCultureProvider.DefaultCookieName,
               CookieRequestCultureProvider.MakeCookieValue(
                 new RequestCulture(
                     CultureInfo.CurrentCulture,
                     CultureInfo.CurrentUICulture)),
            new CookieOptions
            {
                Expires = DateTimeOffset.Now.AddDays(30)
            });
    }

    @if (mainConfiguration.Value.Runtime == Shared.Dto.Db.BlazorRuntime.Server)
    {
        @(Html.AntiForgeryToken())
        <app>
            <component type="ModuleProvider.RootComponentMapping.ComponentType" render-mode="Server" />
        </app>
    }
    else
    {
        <app wasm>
        </app>
    }

    <div id="blazor-error-ui" data-nosnippet>
        <i class="material-icons">error</i>
        <environment include="Staging,Production">
            An error has occurred. This application may no longer respond until reloaded.
        </environment>
        <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment>
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

    <!-- load remaining JS after blazor boot loads -->
    <script src="js/interop.js"></script>
    <!-- https://docs.microsoft.com/en-us/aspnet/core/blazor/progressive-web-app -->
    <script>navigator.serviceWorker.register('service-worker.js');</script>

    @if (mainConfiguration.Value.Runtime == Shared.Dto.Db.BlazorRuntime.Server)
    {
        <script src="_framework/blazor.server.js"></script>
    }
    else
    {
        <script src="_framework/blazor.webassembly.js"></script>
    }
</body>
</html>
