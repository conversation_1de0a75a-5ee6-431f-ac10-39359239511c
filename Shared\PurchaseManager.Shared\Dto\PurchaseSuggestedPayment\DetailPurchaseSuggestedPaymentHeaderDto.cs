﻿namespace PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;

public class DetailPurchaseSuggestedPaymentHeaderDto
{
    public string DocumentType { get; set; }
    public string Number { get; set; }
    public string LocationCode { get; set; }
    public string BuyFromVendorNumber { get; set; }
    public string PayToVendorNumber { get; set; }
    public string PayToName { get; set; }
    public string PayToAddress { get; set; }
    public DateTime OrderDate { get; set; }
    public DateTime PostingDate { get; set; }
    public DateTime ExpectedReceiptDate { get; set; }
    public DateTime DueDate { get; set; }
    public string PurchaserCode { get; set; }// purchaser username 
    public DateTime DocumentDate { get; set; }
    public string PaymentMethodCode { get; set; }//CASH OR BANK
    public int Status { get; set; }//Enum 0 - New, 1 - Edit, 2 - Save, 3 - Approve
    public string SourceCode { get; set; }
    public string PurchaserApprovalBy { get; set; }
    public string VendorApprovalBy { get; set; }
    public string LoginID { get; set; }
    public string PostingDescription { get; set; }
}
