﻿using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Server.Services.PurchaseOrder.Interface;

public interface ILineServices
{
    Task<ApiResponse> CreateLineAsync(POLineAddOrUpdate poLineAdd);
    Task<ApiResponse> CreateMultipleLinesAsync(List<POLineAddOrUpdate> poLineAdds);
    Task<ApiResponse> UpdateLineAsync(POLineAddOrUpdate poLineAdd);
    Task<ApiResponse> DeleteLineAsync(string documentNumber, string itemNumber, int rowId);
    /// <summary>
    /// Save pdf poNumber
    /// </summary>
    /// <param name="number"></param>
    /// <returns></returns>
    Task<ApiResponse> SavePdfAsync(string number);
}
