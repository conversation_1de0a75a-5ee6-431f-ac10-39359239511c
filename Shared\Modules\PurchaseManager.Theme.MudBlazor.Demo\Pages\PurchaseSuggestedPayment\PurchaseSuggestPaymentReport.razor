@page "/po/purchase-suggest-payment/reports"
@inherits PurchaseSuggestPaymentReportPage
@if (isLoading)
{
    <MudProgressLinear Indeterminate="@isLoading" />
}
else
{
    <MudStack Row>
        <MudText Typo="Typo.h4">Purchase Suggest Payment Reports</MudText>
        <MudSpacer />
        <MudSpacer />
        <MudSpacer />
        <MudSpacer />
        <MudSpacer />
        <MudSpacer />
        @if (selectedView == "All")
        {
            <MudSelect Clearable Variant="Variant.Outlined" T="string" Label="Filter by tags" Dense Margin="Margin.Dense"
                MultiSelection Value="selectedTags" ValueChanged="@(async value => await OnFilterByTagsChanged(value))">
                @foreach (var tag in listTagToSelect)
                {
                    <MudSelectItem Value="tag">@tag</MudSelectItem>
                }
            </MudSelect>
        }
        <MudSelect Clearable Variant="Variant.Outlined" T="string" Label="View by" Dense Margin="Margin.Dense"
            MultiSelection Value="selectedView" ValueChanged="@(async value => await OnViewChanged(value))">
            @foreach (var view in listView)
            {
                <MudSelectItem Value="view">@view</MudSelectItem>
            }
        </MudSelect>
    </MudStack>
    <MudTable @ref="table" Striped="false" Bordered="false" Outlined="false" Dense Hover FixedHeader FixedFooter
        GroupBy="@_groupDefinition" GroupHeaderStyle="background-color:var(--mud-palette-background-gray)" Class="mt-3"
        ServerData="@(new Func<TableState, CancellationToken, Task<TableData<PurchaseManager.Shared.Dto.PurchaseSuggestedPayment.PurchaseSuggestedReport>>>(ServerReload))"
        T="PurchaseManager.Shared.Dto.PurchaseSuggestedPayment.PurchaseSuggestedReport" Height="735px">
        <ColGroup>
            @if (selectedView is not null && selectedView.Contains("Item"))
            {
                @if (_groupDefinition.Expandable)
                {
                    <col style="width: 60px;" />
                }
                <col style="width: 60px;" />
                <col />
                <col />
                <col />
                <col />
                <col />
                <col />
                <col />
                <col />
            }
        </ColGroup>
        <GroupHeaderTemplate>
            @if (selectedView is not null && selectedView.Contains("Item"))
            {
                <MudTh Class="mud-table-cell-custom-group" colspan="10">
                    <MudText Align="Align.Center">@($"{context.GroupName}: {context.Key}")</MudText>
                </MudTh>
            }
        </GroupHeaderTemplate>
        <HeaderContent>
            <MudTh>@L["Item Number"]</MudTh>
            <MudTh>@L["Item Name"]</MudTh>
            <MudTh>@L["Location Code"]</MudTh>
            <MudTh>@L["Request Quantity"]</MudTh>
            <MudTh>@L["Confirm Quantity"]</MudTh>
            <MudTh>@L["UOM"]</MudTh>
            <MudTh>@L["Rate"]</MudTh>
            @if (selectedView == "All")
            {
                <MudTh>@L["Tags"]</MudTh>
            }
            <MudTh>@L["Reason Name"]</MudTh>
            <MudTh>@L["Quantity"]</MudTh>
        </HeaderContent>
        <RowTemplate Context="Row">
            <MudTh>@Row.ItemNumber</MudTh>
            <MudTh>@Row.ItemName</MudTh>
            <MudTh>@Row.LocationCode</MudTh>
            <MudTh>@Row.RequestQuantity</MudTh>
            <MudTh>@Row.ConfirmQuantity</MudTh>
            <MudTh>@Row.UnitOfMeasure</MudTh>
            <MudTh>@Row.Rate</MudTh>
            @if (selectedView == "All")
            {
                <MudTh>
                    @foreach (var tag in @Row.Tags.Split(",").ToList())
                    {
                        <MudChip Size="Size.Small" Color="Color.Primary" Variant="Variant.Text" Text="@tag" T="string" />
                    }
                </MudTh>
            }
            <MudTh>@Row.ReasonName</MudTh>
            <MudTh>@Row.Quantity</MudTh>
        </RowTemplate>
        <PagerContent>
            <MudTablePager RowsPerPageString=@L["Rows per page"] />
        </PagerContent>
        <NoRecordsContent>
            No Data
        </NoRecordsContent>
    </MudTable>
}