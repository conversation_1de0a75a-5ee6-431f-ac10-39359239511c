﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NuGet.Protocol;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.Permissions;
using PurchaseManager.Shared.Dto.PO;
using static Microsoft.AspNetCore.Http.StatusCodes;
namespace PurchaseManager.Server.Controllers;

public partial class PurchaseOrderController
{
    //[AllowAnonymous]
    [HttpGet("line/{number}")]
    [Authorize(Permissions.PO.Read)]
    [ProducesResponseType(Status204NoContent)]
    public async Task<ApiResponse> GetLines(string number)
    {
        return ModelState.IsValid ? await _lineManager.GetLinesAsync(number) : _invalidData;
    }

    //[AllowAnonymous]
    [HttpPost("line")]
    [Authorize(Permissions.PO.Create)]
    public async Task<ApiResponse> AddLine([FromBody] POLineAddOrUpdate addNewDto)
    {
        return ModelState.IsValid ? await _lineServices.CreateLineAsync(addNewDto) : _invalidData;
    }

    [HttpPost("multiple-lines")]
    [Authorize(Permissions.PO.Create)]
    public async Task<ApiResponse> AddMultipleLines([FromBody] List<POLineAddOrUpdate> addNewDto)
    {
        return ModelState.IsValid ? await _lineServices.CreateMultipleLinesAsync(addNewDto) : _invalidData;
    }


    [HttpPut("line")]
    [Authorize(Permissions.PO.Update)]
    public async Task<ApiResponse> UpdateLine(POLineAddOrUpdate updateDto)
    {
        return ModelState.IsValid
            ? await _lineServices.UpdateLineAsync(updateDto)
            : new ApiResponse(Status400BadRequest, ModelState.Values.SelectMany(v => v.Errors).ToJson());
    }

    /// <summary>
    ///     Update quantity in po line
    /// </summary>
    /// <param name="updatePoDto"></param>
    /// <returns></returns>
    [AllowAnonymous]
    [HttpPut("line/quantity")]
    public async Task<ApiResponse> UpdatePurchaseOrder(UpdatePoDto updatePoDto)
    {
        return ModelState.IsValid ? await _lineManager.UpdatePurchaseOrder(updatePoDto)
            : new ApiResponse(Status400BadRequest, _i18N["InvalidData"]);
    }

    //DELETE: api/PurchaseOrder/5/6/7
    [HttpDelete("{documentNumber}/{itemNumber}/{rowId}")]
    [Authorize(Permissions.PO.Delete)]
    public async Task<ApiResponse> DeleteLine(string documentNumber, string itemNumber, int rowId)
    {
        return ModelState.IsValid ? await _lineServices.DeleteLineAsync(documentNumber, itemNumber, rowId) : _invalidData;
    }

    [HttpGet("DownloadPO")]
    [Authorize(Permissions.PO.Read)]
    public async Task<ApiResponse> SavePdfsv(string number)
    {
        return ModelState.IsValid ? await _lineServices.SavePdfAsync(number) : _invalidData;
    }
}
