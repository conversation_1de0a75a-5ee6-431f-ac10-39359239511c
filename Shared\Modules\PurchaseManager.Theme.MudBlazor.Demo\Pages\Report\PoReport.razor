@inherits PurchaseManager.Theme.Material.Demo.Pages.Report.PoReportBase
@using System.Reflection
@using System.ComponentModel.DataAnnotations
@if (isLoading)
{
    <MudProgressLinear Color="Color.Primary" Indeterminate />
}
else
{
    <MudCard Elevation="0" Class="my-16 py-2 px-4" Outlined Style="position:relative;">
        <MudOverlay Visible="@isDownloading" DarkBackground="true" LightBackground="true" Absolute="true">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
        </MudOverlay>
        <MudCardHeader>
            <CardHeaderContent>
                <MudText Typo="Typo.h5">@L["Setting up export file"]</MudText>
            </CardHeaderContent>
            <CardHeaderActions>
                <MudButton Color="Color.Primary" Disabled="@(selectedFields is null || !selectedFields.Any())"
                    OnClick="@DownloadPoReport" Variant="Variant.Filled" StartIcon="@Icons.Material.Outlined.Download">
                    Download
                </MudButton>
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent>
            <MudStack Row Class="mb-2">
                <MudDateRangePicker @ref="fromToDatePickerRef" Class="" PickerVariant="PickerVariant.Inline"
                    Margin="Margin.Dense" PlaceholderStart="Start Date" PlaceholderEnd="End Date"
                    @bind-DateRange="dateRange" Label="By create date" AutoClose="@true">
                    <PickerActions>
                        <MudButton OnClick="@(() => fromToDatePickerRef.CloseAsync(false))">Cancel</MudButton>
                        <MudButton Color="Color.Primary" OnClick="@(() => FilterByDate())">Ok</MudButton>
                    </PickerActions>
                </MudDateRangePicker>
                <MudAutocomplete T="PurchaseManager.Shared.Models.GetVendorDto" ShrinkLabel="true" Label="By vendor"
                    ShowProgressIndicator="true" @ref="vendorAutoRef" ValueChanged="@OnSearchContactByVendorName"
                    ToStringFunc="dto => dto == null ? null : string.Concat(dto.Number, ' ', ('-'), ' ', dto.Name)"
                    AdornmentIcon="@Icons.Material.Filled.Clear" AdornmentColor="Color.Error"
                    OnAdornmentClick="@(async _ => { await vendorAutoRef.ResetAsync(); })" ResetValueOnEmptyText
                    SearchFunc="@ItemSearch" Margin="Margin.Dense" Dense="true">
                </MudAutocomplete>
            </MudStack>
            <MudText Typo="Typo.caption" Class="mb-2">@L["By status"]</MudText>
            <MudChipSet T="object" @bind-SelectedValue="selectedStatus" CheckMark="true"
                SelectionMode="SelectionMode.SingleSelection">
                @foreach (var enumValue in Enum.GetValues(typeof(PurchaseOrderEnum)))
                {
                    <MudChip Variant="Variant.Filled" Value="@enumValue" SelectedColor="Color.Info" Color="Color.Info">
                        @L[@enumValue.ToString()]
                    </MudChip>
                }
            </MudChipSet>
            <MudDivider Class="my-3" />
            <MudText Typo="Typo.caption" Class="mb-2">@L["Fields to show"]</MudText>
            <MudChipSet T="bool" @bind-SelectedValue="@selectAllField" CheckMark="true"
                SelectionMode="SelectionMode.ToggleSelection">
                <MudChip OnClick="@OnClickCheckAllField" Variant="Variant.Filled" Value="@true"
                    SelectedColor="Color.Success" Color="Color.Success">
                    Check all
                </MudChip>
            </MudChipSet>
            <MudChipSet T="string" @bind-SelectedValues="selectedFields" CheckMark="true"
                SelectionMode="SelectionMode.MultiSelection">
                @foreach (var field in listFieldCanSelect)
                {
                    <MudChip Variant="Variant.Filled" OnClick="@OnClickChip" Value="@field" SelectedColor="Color.Success"
                        Color="Color.Primary">
                        @L[@field]
                    </MudChip>
                }
            </MudChipSet>
        </MudCardContent>
    </MudCard>
}
