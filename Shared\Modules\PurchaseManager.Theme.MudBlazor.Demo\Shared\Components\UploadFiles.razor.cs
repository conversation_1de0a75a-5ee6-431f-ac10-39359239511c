﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Localization;

using MudBlazor;

using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Theme.Material.Shared.Utils;

namespace PurchaseManager.Theme.Material.Demo.Shared.Components;

public class UploadFilesBasePage : ComponentBase
{
    [Parameter] public string PONumber { get; set; }
    [Parameter] public string Prefix { get; set; }
    [Parameter] public string Caption { get; set; }
    [Parameter] public bool Disabled { get; set; }
    [Parameter] public EventCallback<MouseEventArgs> OnReloadFileTable { get; set; }
    [Inject] protected IStringLocalizer<Global> L { get; set; }
    [Inject] protected IApiClient apiClient { get; set; }

    [Inject] protected IViewNotifier viewNotifier { get; set; }
    protected bool isFileUploading { get; set; }
    protected MudFileUpload<IReadOnlyList<IBrowserFile>> _fileUploadRef;

    protected const string DefaultDragClass = "relative rounded-lg border-2 border-dashed pa-4 mt-4 mud-width-full mud-height-full";
    protected UploadFileUtil fileUtil { get; set; } = new UploadFileUtil();


    protected string _dragClass = DefaultDragClass;
    protected readonly List<string> fileNames = new();

    #region upload receive files

    protected async Task ClearReceiveFiles()
    {
        await (_fileUploadRef?.ClearAsync() ?? Task.CompletedTask);
        fileNames.Clear();
        ClearDragClass();
    }

    protected void OnInputFileChanged(InputFileChangeEventArgs e)
    {
        ClearDragClass();
        var files = e.GetMultipleFiles();
        foreach (var file in files)
        {
            fileNames.Add(file.Name);
        }
    }
    protected void ClearDragClass()
        => _dragClass = DefaultDragClass;


    #endregion

    protected void SetDragClass()
            => _dragClass = $"{DefaultDragClass} mud-border-primary";

    protected async Task Submit()
    {
        isFileUploading = true;
        var resp = await fileUtil.UploadFiles(_fileUploadRef, apiClient, PONumber, Prefix);
        if (resp.IsSuccessStatusCode)
        {
            viewNotifier.Show(@L["Upload files success"], ViewNotifierType.Success);
            await ClearReceiveFiles();
            await OnReloadFileTable.InvokeAsync();
        }
        else viewNotifier.Show(@L["Upload files fail"], ViewNotifierType.Error);
        StateHasChanged();
        isFileUploading = false;
    }
}

