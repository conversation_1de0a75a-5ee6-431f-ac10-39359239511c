﻿using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.StockOrder;
namespace PurchaseManager.Shared.Services;

public class StockOrderApiClient : BaseApiClient, IStockOrderApiClient
{

    public StockOrderApiClient(HttpClient httpClient, ILogger<BaseApiClient> logger, string rootApiPath = "api/data/") : base(
    httpClient, logger, rootApiPath)
    {
    }
    public async Task<ApiResponseDto> CreateMultipleStockOrder(CreateStockOrderDto stockOrders)
        => await httpClient.PostJsonAsync<ApiResponseDto>("api/StockOrder/create", stockOrders);

    public async Task<ApiResponseDto<List<GetStockOrderDto>>> GetStockOrderByPoHeader(string poHeader)
        => await httpClient.GetJsonAsync<ApiResponseDto<List<GetStockOrderDto>>>($"api/StockOrder/by-po-header/{poHeader}");

    public async Task<ApiResponseDto<PagedResultDto<GetStockOrderDto>>> GetStockOrderByPoHeaderAsync(StockOrderFilter filter)
        => await httpClient.GetFromJsonAsync<ApiResponseDto<PagedResultDto<GetStockOrderDto>>>("/api/StockOrder/Gets?" +
        filter.ToQuery());

    public async Task<ApiResponseDto> UpdateStockOrderAsync(string number, UpdateStockOrderDto dto)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>($"api/StockOrder/update/{number}", dto);
    }

    public async Task<ApiResponseDto> SaveDraftStockOrdersAsync(string headerNumber)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>($"api/StockOrder/save-drafts/{headerNumber}", new { });
    }

    public async Task<ApiResponseDto<List<SOLineGetDto>>> GetLinesForStockOrder(string purchaseOrderNumber)
    {
        return await httpClient.GetJsonAsync<ApiResponseDto<List<SOLineGetDto>>>(
        $"api/StockOrder/stock-order-lines/{purchaseOrderNumber}");
    }

    // New Header/Line methods
    public async Task<ApiResponseDto<PagedResultDto<GetStockOrderHeaderDto>>> GetStockOrderHeadersAsync(StockOrderFilter filter)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<PagedResultDto<GetStockOrderHeaderDto>>>(
            $"/api/StockOrder/headers?{filter.ToQuery()}");
    }

    public async Task<ApiResponseDto<GetStockOrderHeaderDto>> GetStockOrderHeaderByNumberAsync(string number)
    {
        return await httpClient.GetJsonAsync<ApiResponseDto<GetStockOrderHeaderDto>>(
            $"api/StockOrder/headers/{number}");
    }

    public async Task<ApiResponseDto> CreateDraftStockOrderAsync(CreateStockOrderHeaderDto createDto)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("api/StockOrder/headers/draft", createDto);
    }

    public async Task<ApiResponseDto> UpdateDraftStockOrderAsync(string stockOrderNumber, SaveStockOrderDto updateDto)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>($"api/StockOrder/headers/{stockOrderNumber}/draft", updateDto);
    }

    public async Task<ApiResponseDto> FinalizeStockOrderAsync(string stockOrderNumber, SaveStockOrderDto finalizeDto)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"api/StockOrder/headers/{stockOrderNumber}/finalize", finalizeDto);
    }

    /// <summary>
    /// Tạo StockOrderHeader và Lines một lần (không draft, chỉ tạo final)
    /// </summary>
    public async Task<ApiResponseDto> CreateFinalStockOrderAsync(CreateStockOrderHeaderDto createDto)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>("api/StockOrder/headers/final", createDto);
    }

    /// <summary>
    /// Cập nhật status của nhiều StockOrder cùng lúc
    /// </summary>
    public async Task<ApiResponseDto> BatchUpdateStockOrderStatusAsync(BatchUpdateStockOrderStatusDto dto)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto>("api/StockOrder/batch-update-status", dto);
    }
}
