using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Dto.Response;
using PurchaseManager.Shared.Models.PurchasePrice;
namespace PurchaseManager.Infrastructure.Server;

public interface IPurchasePriceManager
{
    Task<ApiResponse<List<ResponseWithDetailError>>> ValidateData(List<CreatePurchasePriceDto> listDataToAdd);
    Task<ApiResponse> ImportFileData(List<CreatePurchasePriceDto> listDataToAdd);
    Task<ApiResponse> CreatePurchasePrice(CreatePurchasePriceDto createPurchasePriceDto);
    Task<ApiResponse> GetPurchasePrices(PurchasePriceFilter filter);
    Task<ApiResponse> GetPurchasePrice(string priceNumber);
    Task<ApiResponse> UpdatePurchasePrice(UpdatePurchasePriceDto updatePurchasePriceDto);
    Task<ApiResponse> DeletePurchasePrice(string priceNumber);
    Task<ApiResponse<List<GetPurchasePriceDto>>> GetPriceAndUnit(string itemNumber, string vendorNumber);
}
