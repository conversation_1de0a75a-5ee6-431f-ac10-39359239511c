﻿namespace PurchaseManager.Shared.Dto.PO;

public class POReportPDF
{
    public string Number { get; set; }
    public string BuyFromVendorNumber { get; set; }
    public string BuyFromVendorName { get; set; }
    public string PayToVendorNumber { get; set; }
    public string PayToName { get; set; }
    public string AppliesToDocumentNumber { get; set; }
    public string PayToAddress { get; set; }
    public string ShipToName { get; set; }
    public string ShipToAddress { get; set; }
    public DateTime OrderDate { get; set; }
    public DateTime DocumentDate { get; set; }
    public DateTime PostingDate { get; set; }
    public string ExternalDocumentNumber { get; set; }
    public DateTime DueDate { get; set; }
    public decimal PaymentDiscount { get; set; }
    public string SourceCode { get; set; }
    public string LocationCode { get; set; }
    public string VendorPostingGroup { get; set; }
    public string CurrencyCode { get; set; }
    public string PurchaserCode { get; set; }
    public string VATRegistrationNumber { get; set; }
    public string BuyFromAddress { get; set; }
    public string VATBusinessPostingGroup { get; set; }
    public string PostingDescription { get; set; }
    public int Status { get; set; }
    public DateTime RequestedReceiptDate { get; set; }
    public DateTime PromisedReceiptDate { get; set; }
    public int Ship { get; set; }
    public DateTime DateReceived { get; set; }
    public DateTime TimeReceived { get; set; }
    public DateTime DateSent { get; set; }
    public DateTime TimeSent { get; set; }
    public string LoginId { get; set; }
    public decimal OverheadRate { get; set; }
    public int Checked { get; set; }
    public string YourReference { get; set; }
    public int Invoice { get; set; }
    public int Receive { get; set; }
    public decimal TotalAmount { get; set; }
    public string UsingID { get; set; }
    public DateTime BeginUsingTime { get; set; }
    public int UsingMinutes { get; set; }
    public DateTime DocumentTime { get; set; }
    public string ModifiedID { get; set; }
    public DateTime LastModifiedTime { get; set; }
    public string BuyerName { get; set; }
    public string BuyerPhone { get; set; }
    public string Description { get; set; }
    /*line*/
    public string ItemNumber { get; set; }
    public string? ItemName { get; set; }
    public string? UOM { get; set; }
    public int Quantity { get; set; }
    public int QtyPerUnit { get; set; }
    public int UnitPrice { get; set; }
    public decimal UnitCost { get; set; }
    public decimal Vat { get; set; }
    public decimal LineDiscountAmount { get; set; }// tien CK
    public decimal Amount { get; set; }// sl * gia mua(unit cost)
    public decimal VatBaseAmount { get; set; }
    public decimal AmountIncludingVat { get; set; }
    public string LineDescription { get; set; }
}
