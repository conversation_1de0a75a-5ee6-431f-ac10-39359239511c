﻿using FluentValidation;
namespace PurchaseManager.Shared.Dto.Item.Validators;

public class UpdateItemValidator
{
    public class UpdateItemDtoValidator : AbstractValidator<UpdateItemDto>
    {
        public UpdateItemDtoValidator()
        {
            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("Item name is required.");

            RuleFor(x => x.Description)
                .NotEmpty().WithMessage("Item description is required.");

            RuleFor(x => x.BaseUnitOfMeasure)
                .NotEmpty().WithMessage("Base unit of measure is required.");
        }
    }
}
