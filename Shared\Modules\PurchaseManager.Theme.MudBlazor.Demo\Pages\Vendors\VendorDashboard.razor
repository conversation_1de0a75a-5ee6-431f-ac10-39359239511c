@attribute [Authorize]

<MudGrid Class="my-4">
    <MudItem xs="4">
        <MudButton OnClick="@(_ => navigation.NavigateTo("/po"))"
            Class="d-flex align-center justify-center mud-width-full py-8" Variant="Variant.Outlined"
            Color="Color.Primary">
            Purchase Orders
        </MudButton>
    </MudItem>
    <MudItem xs="4">
        <MudButton OnClick="@(_ => navigation.NavigateTo("/po/create"))"
            Class="d-flex align-center justify-center mud-width-full py-8" Variant="Variant.Outlined"
            Color="Color.Success">
            Create PO
        </MudButton>
    </MudItem>
    <MudItem xs="4">
        <MudButton OnClick="@(_ => navigation.NavigateTo("/vendor-document"))"
            Class="d-flex align-center justify-center mud-width-full py-8" Variant="Variant.Outlined"
            Color="Color.Error">
            Documents
        </MudButton>
    </MudItem>
    @* <MudItem xs="4">
        <MudPaper Elevation="0" Class="d-flex align-center justify-center mud-width-full py-8">Coming Soon</MudPaper>
    </MudItem> *@
</MudGrid>