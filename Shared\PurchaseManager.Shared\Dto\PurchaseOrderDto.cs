﻿using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Shared.Dto.Item;
namespace PurchaseManager.Shared.Dto
{
    public class PurchaseOrderDto
    {
        public class Header
        {
            public class Gets//gets multi
            {
                public int PageIndex { get; set; }
                public int PageSize { get; set; }
                public int TotalRecord { get; set; }

                public List<Detail> details { get; set; }
            }
            public class Detail//(chi tiết của header không phải line)//get one
            {
                public string Number { get; set; } = null!;
                public string BuyFromVendorNumber { get; set; } = null!;
                public string BuyFromVendorName { get; set; } = null!;
                public string PayToVendorNumber { get; set; } = null!;
                public string PayToName { get; set; } = null!;
                public string AppliesToDocumentNumber { get; set; } = null!;
                public string PayToAddress { get; set; } = null!;
                public string ShipToName { get; set; } = null!;
                public string ShipToAddress { get; set; } = null!;
                public string PostingDescription { get; set; } = null!;
                public string ExternalDocumentNumber { get; set; } = null!;
                public decimal PaymentDiscount { get; set; }
                public string SourceCode { get; set; } = null!;
                public string LocationCode { get; set; } = null!;
                public string VendorPostingGroup { get; set; } = null!;
                public string CurrencyCode { get; set; } = null!;
                public string PurchaserCode { get; set; } = null!;
                public string VATRegistrationNumber { get; set; } = null!;
                public string BuyFromAddress { get; set; } = null!;
                public string VATBusinessPostingGroup { get; set; } = null!;
                public int Status { get; set; }
                public int Ship { get; set; }
                public string? LoginId { get; set; }
                public decimal? OverheadRate { get; set; }
                public int? Checked { get; set; }
                public string YourReference { get; set; } = null!;
                public int Invoice { get; set; }
                public int Receive { get; set; }
                public decimal TotalAmount { get; set; }
                public decimal TotalQuantityReceived { get; set; }
                public decimal TotalQuantity { get; set; }
                public string? UsingID { get; set; }
                public int UsingMinutes { get; set; }
                public string? ModifiedID { get; set; }
                public decimal TotalRecord { get; set; }
                public string? VendorApprovalBy { get; set; }
                public string? PurchaserApprovalBy { get; set; }
                public int DocNoOccurrence { get; set; }
                public DateTime DocumentDate { get; set; }
                public DateTime PostingDate { get; set; }
                public DateTime OrderDate { get; set; }
                public DateTime DueDate { get; set; }
                public DateTime? DocumentTime { get; set; }
                public DateTime? LastModifiedTime { get; set; }
                public DateTime? BeginUsingTime { get; set; }
                public DateTime? DateReceived { get; set; }
                public DateTime? TimeReceived { get; set; }
                public DateTime? DateSent { get; set; }
                public DateTime? RequestedReceiptDate { get; set; }
                public DateTime? PromisedReceiptDate { get; set; }
                public DateTime? TimeSent { get; set; }
                // public DateOnly? ApprovalDate { get; set; }
            }

            public class GetFullDoc// header+line
            {
                public string Number { get; set; }
                public string BuyFromVendorNumber { get; set; }
                public string PayToVendorNumber { get; set; }
                public string PayToName { get; set; }
                public string AppliesToDocumentNumber { get; set; }
                public string PayToAddress { get; set; }
                public string ShipToName { get; set; }
                public string ShipToAddress { get; set; }
                public DateTime OrderDate { get; set; }
                public DateTime DocumentDate { get; set; }
                public DateTime PostingDate { get; set; }
                public string ExternalDocumentNumber { get; set; }
                public DateTime DueDate { get; set; }
                public decimal PaymentDiscount { get; set; }
                public string SourceCode { get; set; }
                public string LocationCode { get; set; }
                public string VendorPostingGroup { get; set; }
                public string CurrencyCode { get; set; }
                public string PurchaserCode { get; set; }
                public string VATRegistrationNumber { get; set; }
                public string BuyFromAddress { get; set; }
                public string VATBusinessPostingGroup { get; set; }
                public int Status { get; set; }
                public DateTime RequestedReceiptDate { get; set; }
                public DateTime PromisedReceiptDate { get; set; }
                public int Ship { get; set; }
                public DateTime DateReceived { get; set; }
                public DateTime TimeReceived { get; set; }
                public DateTime DateSent { get; set; }
                public DateTime TimeSent { get; set; }
                public string LoginId { get; set; }
                public decimal OverheadRate { get; set; }
                public int Checked { get; set; }
                public string YourReference { get; set; }
                public int Invoice { get; set; }
                public int Receive { get; set; }
                public decimal TotalAmount { get; set; }
                public string UsingID { get; set; }
                public DateTime BeginUsingTime { get; set; }
                public int UsingMinutes { get; set; }
                public DateTime DocumentTime { get; set; }
                public string ModifiedID { get; set; }
                public DateTime LastModifiedTime { get; set; }
                public List<Line.Get> Line { get; set; }
            }

            public class ReportPrinting
            {
                public string Number { get; set; }
                public string BuyFromVendorNumber { get; set; }
                public string BuyFromVendorName { get; set; }
                public string PayToVendorNumber { get; set; }
                public string PayToName { get; set; }
                public string AppliesToDocumentNumber { get; set; }
                public string PayToAddress { get; set; }
                public string ShipToName { get; set; }
                public string ShipToAddress { get; set; }
                public DateTime OrderDate { get; set; }
                public DateTime DocumentDate { get; set; }
                public DateTime PostingDate { get; set; }
                public string ExternalDocumentNumber { get; set; }
                public DateTime DueDate { get; set; }
                public decimal PaymentDiscount { get; set; }
                public string SourceCode { get; set; }
                public string LocationCode { get; set; }
                public string VendorPostingGroup { get; set; }
                public string CurrencyCode { get; set; }
                public string PurchaserCode { get; set; }
                public string VATRegistrationNumber { get; set; }
                public string BuyFromAddress { get; set; }
                public string VATBusinessPostingGroup { get; set; }
                public string PostingDescription { get; set; }
                public int Status { get; set; }
                public DateTime RequestedReceiptDate { get; set; }
                public DateTime PromisedReceiptDate { get; set; }
                public int Ship { get; set; }
                public DateTime DateReceived { get; set; }
                public DateTime TimeReceived { get; set; }
                public DateTime DateSent { get; set; }
                public DateTime TimeSent { get; set; }
                public string LoginId { get; set; }
                public decimal OverheadRate { get; set; }
                public int Checked { get; set; }
                public string YourReference { get; set; }
                public int Invoice { get; set; }
                public int Receive { get; set; }
                public decimal TotalAmount { get; set; }
                public string UsingID { get; set; }
                public DateTime BeginUsingTime { get; set; }
                public int UsingMinutes { get; set; }
                public DateTime DocumentTime { get; set; }
                public string ModifiedID { get; set; }
                public DateTime LastModifiedTime { get; set; }
                public string BuyerName { get; set; }
                public string BuyerPhone { get; set; }
                public string Description { get; set; }
                /*line*/
                public string ItemNumber { get; set; }
                public string? ItemName { get; set; }
                public string? UOM { get; set; }
                public int Quantity { get; set; }
                public int QtyPerUnit { get; set; }
                public int UnitPrice { get; set; }
                public decimal UnitCost { get; set; }
                public decimal Vat { get; set; }
                public decimal LineDiscountAmount { get; set; }// tien CK
                public decimal Amount { get; set; }// sl * gia mua(unit cost)
                public decimal VatbaseAmount { get; set; }
                public decimal AmountIncludingVat { get; set; }
                public string LineDescription { get; set; }
            }

            public partial class Vendor
            {
                public int RowId { get; set; }

                public string Number { get; set; }

                public string Name { get; set; }

                public string SearchName { get; set; }

                public string Address { get; set; }

                public string City { get; set; }

                public string Contact { get; set; }

                public string Phone { get; set; }

                public string Telex { get; set; }

                public string VendorPostingGroup { get; set; }

                public string CurrencyCode { get; set; }

                public string PurchaserCode { get; set; }

                public string ShipmentMethodCode { get; set; }

                public string ShippingAgentCode { get; set; }

                public string CountryCode { get; set; }

                public int Blocked { get; set; }

                public string PayToVendorNumber { get; set; }

                public string PaymentMethodCode { get; set; }

                public DateTime LastDateModified { get; set; }

                public string Fax { get; set; }

                public string VatregistrationNumber { get; set; }

                public string PostCode { get; set; }

                public string County { get; set; }

                public string Email { get; set; }

                public string HomePage { get; set; }

                public string VatbusinessPostingGroup { get; set; }

                public string? LoginId { get; set; }

                public int? VendorGroup { get; set; }

                public DateTime? CreateDate { get; set; }

                public string? ImportLicense { get; set; }

                public string? CertificateOfRegistration { get; set; }

                public DateTime? RegistrationOfExpirationDate { get; set; }

                public string? LicenseNumber { get; set; }

                public DateTime? LicenseDate { get; set; }

                public DateTime? ExprireLicenseDate { get; set; }

                public string? LastUserModified { get; set; }

                public int? Internal { get; set; }

                public string? Description { get; set; }

                public int Status { get; set; }

                public DateTime? LastModifiedTime { get; set; }
            }
        }

        public class Line
        {
            public class Get
            {
                public int DocumentType { get; set; }
                public string DocumentNumber { get; set; }
                public int LineNumber { get; set; }
                public int Type { get; set; }
                public string ItemNumber { get; set; }
                public string LocationCode { get; set; }
                public string PostingGroup { get; set; }
                public DateTime ExpectedReceiptDate { get; set; }
                public string Description { get; set; }
                public string Description2 { get; set; }
                public string UnitOfMeasure { get; set; }
                public decimal Quantity { get; set; } = 1;
                public decimal OutstandingQuantity { get; set; }
                public decimal QuantityToInvoice { get; set; }
                public decimal QuantityToReceive { get; set; }
                public decimal Vat { get; set; }// % vat
                public decimal LineDiscountPercent { get; set; }
                public decimal LineDiscountAmount { get; set; }
                public decimal Amount { get; set; }
                public decimal AmountIncludingVat { get; set; }// thanh tien cuoi cung, = tien vat + tien sau CK
                public int AllowInvoiceDiscount { get; set; }
                public decimal QuantityReceived { get; set; }
                public decimal QuantityInvoiced { get; set; }
                public string ReceiptNumber { get; set; }
                public int ReceiptLineNumber { get; set; }
                public decimal ProfitPercent { get; set; }
                public decimal Vatamount { get; set; }
                public decimal UnitCost { get; set; }// gia mua
                public int Status { get; set; }
                public decimal LineAmount { get; set; }
                public decimal VatbaseAmount { get; set; } // thanh tien sau chiec khau: (- CK) = amount - line discount amount
                public string UnitOfMeasureCode { get; set; }
                public string CrossReferenceNumber { get; set; }
                public string? LotNo { get; set; }
                public DateTime? ExpirationDate { get; set; } // han dung
                public string? CategoryCode { get; set; }
                public string? ItemName { get; set; }
                public int Rowid { get; set; }
                public decimal? UnitPrice { get; set; }
                public decimal QtyPerUnitofMeasure { get; set; }
                public decimal LastUnitCost { get; set; }
                [NotMapped]
                public DetailItemDto? item { get; set; }
                [NotMapped]
                public int TempReceive { get; set; }
                [NotMapped]
                public bool IsArrowDisabled { get; set; }
            }
        }
    }
}
