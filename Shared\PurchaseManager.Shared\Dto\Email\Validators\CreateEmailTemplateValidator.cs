﻿using FluentValidation;
namespace PurchaseManager.Shared.Dto.Email.Validators;

public class CreateEmailTemplateValidator
{
    public class UnitOfMeasureDtoValidator : AbstractValidator<CreateEmailTemplateDto>
    {
        public UnitOfMeasureDtoValidator()
        {
            RuleFor(x => x.TemplateName)
                .NotEmpty().WithMessage("Template name is required.");
            RuleFor(x => x.Body)
                .NotEmpty().WithMessage("Body is required.");
            RuleFor(x => x.Subject)
                .NotEmpty().WithMessage("Subject is required.");
        }
    }
}
