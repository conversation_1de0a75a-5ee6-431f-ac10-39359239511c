using System.Globalization;
using System.Net.Http.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using MudBlazor;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Contact;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Models.Contact;
using PurchaseManager.Shared.Models.PO;
using PurchaseManager.Shared.Providers;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder;
public partial class CreatePo : ComponentBase
{
    #region DIs
    [Inject]
    private HttpClient HttpClient { get; set; }
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    [Inject]
    private NavigationManager NavigationManager { get; set; }
    [Inject]
    private AuthenticationStateProvider AuthStateProvider { get; set; }
    [Inject]
    private IStringLocalizer<Global> L { get; set; }
    [Inject]
    private IApiClient ApiClient { get; set; }
    [Inject]
    private IPurchaseOrderApiClient PurchaseOrderApiClient { get; set; }
    #endregion DIs
    private List<CheckPaymentDayResponse> ListCheckPaymentDayResponse { get; set; } = [];
    private MudAutocomplete<GetVendorDto> AutoComplete { get; set; }
    private bool IsAlertDialogOpen { get; set; }
    private bool IsLoading { get; set; }
    private string SelectedVendorNumber { get; set; }
    private string SelectedContactNumber { get; set; }
    private TypeOfProviderForPOEnum SelectedType { get; set; } = TypeOfProviderForPOEnum.Vendor;
    private UserViewModel UserViewModel { get; set; } = new UserViewModel();
    private DocNoOccurrenceEnum DocNoOccurrence { get; set; } = DocNoOccurrenceEnum.Order;
    protected override async Task OnInitializedAsync()
    {
        IsLoading = true;
        UserViewModel = await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
        await CheckVendorCreatePO();
        await base.OnInitializedAsync();
        IsLoading = false;
    }
    private async Task CheckPaymentDayByVendorNumber()
    {
        try
        {
            var resp = await PurchaseOrderApiClient.CheckPaymentDayByVendorNumberAsync([AutoComplete?.Value?.Number ?? SelectedVendorNumber]);
            if (resp.IsSuccessStatusCode && resp.Result.Count > 0)
            {
                ListCheckPaymentDayResponse = resp.Result;
                IsAlertDialogOpen = true;
            }
            else await Create();
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    private async Task CheckVendorCreatePO()
    {
        try
        {
            if (!string.IsNullOrEmpty(UserViewModel.VendorCode))
            {
                var cancellationToken = new CancellationToken();
                var foundVendors = await ItemSearch(UserViewModel.VendorCode, cancellationToken);
                var vendor = foundVendors.FirstOrDefault();
                if (vendor is not null)
                {
                    SelectedVendorNumber = vendor.Number;
                }
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error, L["UserCreationFailed"]);
        }
    }
    private async Task Create()
    {
        IsAlertDialogOpen = false;
        if (SelectedType.Equals(TypeOfProviderForPOEnum.Vendor))
        {
            var apiResponse = await PurchaseOrderApiClient.CreateHeaderAsync(SelectedVendorNumber, string.Empty);

            if (!apiResponse.IsSuccessStatusCode)
            {
                ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Error);
                return;
            }
            var headers = apiResponse.Result;
            if (DocNoOccurrence == DocNoOccurrenceEnum.Consigned)
            {
                var headerDto = new UpdatePOHeaderDto
                {
                    Number = headers.Number,
                    DueDate = headers.DueDate,
                    OrderDate = headers.OrderDate,
                    PurchaseUser = UserViewModel.UserName,
                    VendorNo = headers.BuyFromVendorNumber,
                    DocNoOccurrence = 1,
                    PostingDescription = "",
                    PurchaserApprovalBy = "",
                    VendorApprovalBy = "",
                };
                var apiResponseUpdateHeader = await PurchaseOrderApiClient.UpdateHeader(headerDto);
                if (!apiResponseUpdateHeader.IsSuccessStatusCode)
                {
                    ViewNotifier.Show(apiResponseUpdateHeader.Message, ViewNotifierType.Error);
                    return;
                }
            }
            NavigationManager.NavigateTo($"/po/{headers.Number}/detail");
        }
        else
        {
            await CreateHeaderByContactNumberAsync();
        }
    }
    private async Task<IEnumerable<GetVendorDto>> ItemSearch(string value, CancellationToken token)
    {
        var apiResponse = await
            HttpClient.GetFromJsonAsync<ApiResponseDto<List<GetVendorDto>>>($"api/vendor/search?number={value}",
                cancellationToken: token);
        return apiResponse.IsSuccessStatusCode ? apiResponse.Result : new List<GetVendorDto>();
    }
    private async Task<IEnumerable<GetContactDto>> SearchContactAsync(string value, CancellationToken token)
    {
        try
        {
            var contactFilter = new ContactFilter { Name = value, Take = 10 };
            var resp = await ApiClient.GetAllContacts(contactFilter);
            var data = resp.Results.ToList();
            return data;
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error, L["UserCreationFailed"]);
            return default;
        }
    }
    private async Task CreateHeaderByContactNumberAsync()
    {
        try
        {
            var resp = await PurchaseOrderApiClient.CreateHeaderAsync(string.Empty, SelectedContactNumber);
            if (resp.IsSuccessStatusCode)
                NavigationManager.NavigateTo($"/po/{resp.Result.Number}/detail");
            else
                ViewNotifier.Show(resp.Message, ViewNotifierType.Error, L["UserCreationFailed"]);
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error, L["UserCreationFailed"]);
        }
    }

    private void OnTypeOfPORadioGroupChanged(TypeOfProviderForPOEnum value)
    {
        SelectedType = value;
        if (value.Equals(TypeOfProviderForPOEnum.Vendor))
        {
            SelectedContactNumber = null;
        }
        else
        {
            SelectedVendorNumber = null;
        }
    }
    private bool CanCreate() => !string.IsNullOrEmpty(SelectedVendorNumber) || !string.IsNullOrEmpty(SelectedContactNumber);
}
