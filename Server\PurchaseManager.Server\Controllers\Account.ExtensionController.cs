using Microsoft.AspNetCore.Mvc;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Models.Account;
namespace PurchaseManager.Server.Controllers;

public partial class AccountController
{
    [HttpGet("check-tax-code/{taxCode}")]
    public async Task<ApiResponse> CheckTaxCode(string taxCode)
        => await _accountManager.CheckTaxCode(taxCode);

    [HttpPost("create-account-vendor")]
    public async Task<ApiResponse> CreateAccountVendor([FromBody] RegisterVendorViewModel dto)
        => await _accountManager.RegisterAccountVendor(dto);

    [HttpGet("get-account-vendor")]
    public async Task<ApiResponse> GetsAccountVendor([FromQuery] AccountVendorFilter filter)
        => await _accountManager.GetsAccountVendor(filter);

    [HttpGet("get-account-vendor-user")]
    public async Task<ApiResponse> GetsAccountVendor([FromQuery] AccountUserFilter filter)
        => await _accountManager.GetsAccountVendor(filter);

    [HttpGet("active-account-vendor/{useName}")]
    public async Task<ApiResponse> ActiveAccountVendor(string useName)
        => await _accountManager.ActiveAccountVendor(useName);

    [HttpGet("block-account-vendor/{useName}")]
    public async Task<ApiResponse> BlockAccountVendor(string useName)
        => await _accountManager.BlockAccountVendor(useName);
    [HttpPost("reset-vendor-password")]
    public async Task<ApiResponse> ResetVendorPassword([FromBody] string useName)
        => await _accountManager.ResetVendorPassword(useName);
}
