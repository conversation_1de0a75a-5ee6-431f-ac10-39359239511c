﻿namespace PurchaseManager.Shared.Dto.PO;

public class CreatePOHeaderDto
{
    public string Number { get; set; } = null!;
    public string BuyFromVendorNumber { get; set; }
    public string PayToCode { get; set; }
    /// <summary>
    /// Người tạo PO
    /// </summary>
    public string PurchaserCode { get; set; }
    public DateTime? DocumentTime { get; set; }
    public DateTime? BeginUsingTime { get; set; }
    public string BuyFromVendorName { get; set; }
    public string BuyFromAddress { get; set; }
    public string BuyFromCity { get; set; }
    public string BuyFromContact { get; set; }
    public string LoginId { get; set; }
    public string LeadTimeCalculation = string.Empty;
    public string VATRegistrationNumber = string.Empty;
    public int DocumentType = 0;
    public string PayToName = string.Empty;
    public string PayToAddress = string.Empty;
    public string PayToCity = string.Empty;
    public string YourReference = string.Empty;
    public string ShipToCode = string.Empty;
    public string ShipToName = string.Empty;
    public string ShipToAddress = string.Empty;
    public string ShipToCity = string.Empty;
    public DateTime OrderDate = DateTime.Now.Date;
    public DateTime PostingDate = DateTime.Now.Date;
    public string PostingDescription = string.Empty;
    public DateTime DueDate = DateTime.Now.Date;
    public decimal PaymentDiscount = 0;
    public DateTime PaymentDiscountDate = DateTime.Now.Date;
    public string ShipmentMethodCode = string.Empty;
    public string LocationCode = string.Empty;
    public string VendorPostingGroup = "NOIDIA";
    public string CurrencyCode = "VND";
    public string OnHold = string.Empty;
    public int AppliesToDocumentType = 0;
    public string AppliesToDocumentNumber = string.Empty;
    public int Receive = 0;
    public int Invoice = 0;
    public string ReceivingNumber = string.Empty;
    public string ExternalDocumentNumber = string.Empty;
    public DateTime DocumentDate = DateTime.Now.Date;
    public string PaymentMethodCode = string.Empty;
    public string SourceCode = "AL";
    public string VATBusinessPostingGroup = "NOIDIA";
    public int Status = 0;
    public DateTime RequestedReceiptDate = DateTime.Now.Date;
    public DateTime PromisedReceiptDate = DateTime.Now.Date;
    public int Ship = 0;
    public DateTime DateReceived = DateTime.Now.Date;
    public DateTime TimeReceived = DateTime.Now.Date;
    public DateTime DateSent = DateTime.Now.Date;
    public DateTime TimeSent = DateTime.Now.Date;
    public string OrderingUser = string.Empty;
    public string DeliveryUser = string.Empty;
    public string InvoicingUser = string.Empty;
    public string WarehouseUser = string.Empty;
    public string PickingUser = string.Empty;
    public string PackingUser = string.Empty;
    public decimal OverheadRate = 0;
    public int Checked = 0;
    public string Description = string.Empty;
    public string ReceivedNumber = string.Empty;
    public int? OrderType = 0;

    public string ModifiedId = string.Empty;
    public string UsingID = string.Empty;
    public DateTime? CreatedAtTime = DateTime.Now;
    public string VendorApprovalBy = string.Empty;
    public string PurchaserApprovalBy = string.Empty;
    public int DocNoOccurrence = 0;
    public bool Blocked = false;
}
