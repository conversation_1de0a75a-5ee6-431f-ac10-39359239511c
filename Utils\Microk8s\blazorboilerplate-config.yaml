apiVersion: v1
data:
  ASPNETCORE_ENVIRONMENT: Development
  ASPNETCORE_Kestrel__Certificates__Default__Password: Admin123
  ASPNETCORE_Kestrel__Certificates__Default__Path: aspnetapp.pfx
  ASPNETCORE_URLS: https://+:443;http://+80
  PurchaseManager__ApplicationUrl: purchasemanager
  PurchaseManager__CertificatePassword: Admin123
  PurchaseManager__IS4ApplicationUrl: purchasemanager
  PurchaseManager__UseSqlServer: "true"
  ConnectionStrings__DefaultConnection: Server=sqlserver;Database=blazor_boilerplate;Trusted_Connection=True;MultipleActiveResultSets=true;User=sa;Password=yourVeryStrong(!)Password;Integrated Security=false
  Serilog__MinimumLevel__Default: Debug
kind: ConfigMap
metadata:
  name: purchasemanager-config
  namespace: default