@inherits VendorPageBase
@page "/vendors"
@attribute [Authorize]

<PageTitle>Vendor</PageTitle>

<MudCard Class="" Elevation="0">
    <MudCardHeader>
        <CardHeaderContent>
            <MudText Typo="Typo.h5">Vendor</MudText>
            <MudText Typo="Typo.caption">List Vendor</MudText>
        </CardHeaderContent>
        <CardHeaderActions>
            <MudButton Color="Color.Primary" Size="Size.Small" Variant="Variant.Filled">@L["Add"]</MudButton>
            <MudIconButton Icon="@Icons.Material.Filled.Refresh" OnClick="@(() => Reload())" Size="Size.Small"
                Class="ma-2" />
            <MudIconButton Icon="@Icons.Material.Filled.Settings" Size="Size.Small" Color="Color.Default" />
        </CardHeaderActions>
    </MudCardHeader>
    <MudCardContent>
        <MudTable ServerData="@(new Func<TableState, CancellationToken, Task<TableData<Vendor>>>(ServerReload))"
            Striped="false" Bordered="false" Dense="true" Hover="true" Elevation="0" Outlined="true" FixedHeader="true"
            FixedFooter="true" Height="500px" LoadingProgressColor="Color.Primary" @ref="table">
            <ToolBarContent>
                <MudTextField T="String" ValueChanged="@(s => OnSearchByVendorName(s))" Label="Search by vendor name"
                    Variant="Variant.Text" @ref="SearchByVendorNameRef"
                    OnAdornmentClick="@(async () => { await SearchByVendorNameRef.Clear(); })" Adornment="Adornment.End"
                    AdornmentIcon="@Icons.Material.Filled.Clear" IconSize="Size.Small" AdornmentColor="Color.Error"
                    Class="mt-0 ml-4">
                </MudTextField>
                <MudTextField T="String" ValueChanged="@(s => OnSearchByVendorPhone(s))" Label="Search by phone"
                    Variant="Variant.Text" @ref="SearchByVendorPhoneRef"
                    OnAdornmentClick="@(async () => { await SearchByVendorPhoneRef.Clear(); })"
                    Adornment="Adornment.End" AdornmentIcon="@Icons.Material.Filled.Clear" IconSize="Size.Small"
                    AdornmentColor="Color.Error" Class="mt-0 ml-4">
                </MudTextField>
                <MudTextField T="String" ValueChanged="@(s => OnSearchByVendorNumber(s))" Label="Search by item number"
                    Variant="Variant.Text" @ref="SearchByItemNumberRef"
                    OnAdornmentClick="@(async () => { await SearchByItemNumberRef.Clear(); })" Adornment="Adornment.End"
                    AdornmentIcon="@Icons.Material.Filled.Clear" IconSize="Size.Small" AdornmentColor="Color.Error"
                    Class="mt-0 ml-4">
                </MudTextField>
            </ToolBarContent>
            <HeaderContent>
                <MudTh>
                    <MudTableSortLabel T="Vendor">@L["Name"]</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel T="Vendor">@L["Short Name"]</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel T="Vendor">@L["VAT Regis_ No"]</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel T="Vendor">@L["Phone"]</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel T="Vendor">@L["Address"]</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel T="Vendor">@L["Block"]</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel T="Vendor">@L["Contacts"]</MudTableSortLabel>
                </MudTh>
            </HeaderContent>
            <RowTemplate Context="row">
                <MudTd DataLabel="@L["Name"]" Style="min-width: 450px">
                    <MudText Typo="Typo.body1"></MudText>
                    <MudLink OnClick="@(e => { NavigationManager.NavigateTo("/vendors/" + row.Number + "/detail"); })">
                        @row.Name </MudLink>
                    <MudText Typo="Typo.subtitle2" Style="color: var(--mud-palette-text-secondary); "> Number:
                        @row.Number </MudText>
                </MudTd>
                <MudTd DataLabel="@L["VAT Registration No"]">@row.SearchName</MudTd>
                <MudTd DataLabel="@L["VAT Registration No"]">@row.VatregistrationNumber</MudTd>
                <MudTd DataLabel="@L["Phone"]">@row.Phone</MudTd>
                <MudTd DataLabel="@L["Address"]" Style="min-width: 300px;">@row.Address</MudTd>
                <MudTd DataLabel="@L["Blocked"]">
                    <MudChip T="VendorBlockedType" Value="@row.Blocked"
                        Color="@(row.Blocked == VendorBlockedType.Blocked ? Color.Error : Color.Primary)"></MudChip>
                </MudTd>
                <MudTd DataLabel="@L["Contacts"]">
                    <MudIconButton OnClick="@(() => OnNavigationToVendorContacts(row.Number))"
                        Icon="@Icons.Material.Filled.Info" Color="Color.Primary" />
                </MudTd>
            </RowTemplate>
            <PagerContent>
                <MudTablePager RowsPerPageString=@L["Rows per page"] />
            </PagerContent>
        </MudTable>
    </MudCardContent>
</MudCard>
