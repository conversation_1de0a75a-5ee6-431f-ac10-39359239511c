﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Aop;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Item;
using PurchaseManager.Storage;
using static PurchaseManager.Shared.Helpers.POHandlingShare;

namespace PurchaseManager.Server.Managers;

[ApiResponseException]
public class POLineManager : IPOLineManager
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;
    private readonly IItemManager _itemManager;
    private readonly IStringLocalizer<Global> _i18N;
    private readonly IAdminManager _adminManager;

    public POLineManager(ApplicationDbContext context, IMapper mapper, IItemManager itemManager, IStringLocalizer<Global> i18N,
        IAdminManager adminManager)
    {
        _context = context;
        _mapper = mapper;
        _itemManager = itemManager;
        _i18N = i18N;
        _adminManager = adminManager;
    }
    public bool EXDateValid(DateOnly exDate)
    {
        var dateValue = exDate.DayNumber - DateOnly.FromDateTime(DateTime.Today).DayNumber;
        return dateValue > 0;
    }


    /// <summary>
    /// Get all data in line
    /// </summary>
    /// <param name="code"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task<ApiResponse> GetLinesAsync(string code)
    {
        try
        {
            var poLine = await _context.PurchaseOrderLines
                .Where(x => x.DocumentNumber == code)
                .ToListAsync();
            if (poLine.Count == 0)
            {
                return ApiResponse.S404("Document no detail");
            }

            var poLineMapper = _mapper.Map<List<POLineGetDto>>(poLine);
            return ApiResponse.S200(result: poLineMapper);
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.GetBaseException().Message);
        }
    }

    /// <summary>
    /// Get po line by LineNumber
    /// </summary>
    /// <param name="documentNumber"></param>
    /// <param name="lineNUmber"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    public async Task<ApiResponse> GetLineAsync(string documentNumber, int lineNUmber)
    {
        var poLine = await _context.PurchaseOrderLines
            .FirstOrDefaultAsync(x => x.DocumentNumber == documentNumber && x.LineNumber == lineNUmber);
        if (poLine == null)
        {
            return ApiResponse.S404($"DocumentNumber {documentNumber} and lineNumber {lineNUmber} does not exist");
        }
        var poLineMapper = _mapper.Map<POLineGetDto>(poLine);

        return ApiResponse.S200(result: poLineMapper);
    }

    public async Task<ApiResponse> DuplicateValid(POLineAddOrUpdate poLine)
    {
        if (poLine is null)
        {
            return ApiResponse.S404("PO Line is null");
        }

        var documentNumber = RemoveSpecialChar(poLine.DocumentNumber);
        var itemNumber = RemoveSpecialChar(poLine.ItemNumber);
        var documentType = poLine.DocumentType;
        var unitOfMeasure = RemoveSpecialChar(poLine.UnitOfMeasure);
        var rowId = poLine.RowID;

        var countLine = await _context.PurchaseOrderLines
            .Where(h => h.DocumentNumber == documentNumber
                        && h.ItemNumber == itemNumber
                        && h.UnitOfMeasure == unitOfMeasure
                        && h.DocumentType == documentType
                        && h.RowId != rowId).CountAsync();

        return countLine > 0 ? ApiResponse.S200("Line is duplicate", true) : ApiResponse.S404("Check line Ok");
    }

    public async Task<ApiResponse> CreateLineAsync(POLineAddOrUpdate poLineAdd)
    {
        try
        {
            var (price, qtyPer, message) = GetPriceAndQtyFromUnitOfMeasure(poLineAdd);

            if (qtyPer == 0 || price == 0)
            {
                return ApiResponse.S404(message);
            }
            
            var itemName = GetItemName(poLineAdd.ItemNumber);
            if (string.IsNullOrEmpty(itemName))
            {
                return ApiResponse.S404("Item number does not exist");
            }

            var lineNumber = await _context.PurchaseOrderLines
                .Where(x => x.DocumentNumber == poLineAdd.DocumentNumber)
                .OrderByDescending(x => x.LineNumber)
                .Select(x => x.LineNumber)
                .FirstOrDefaultAsync();

            var poLine = _mapper.Map<PurchaseOrderLine>(poLineAdd);
            poLine.LineNumber = lineNumber + 1;
            poLine.Description = poLineAdd.Desc;
            poLine.ReceiptNumber = string.Empty;
            poLine.Type = 0;
            poLine.LocationCode = "TSAL";
            poLine.PostingGroup = string.Empty;
            poLine.ExpectedReceiptDate = DateTime.Now;
            poLine.Description2 = string.Empty;
            poLine.AllowInvoiceDiscount = 0;
            poLine.QuantityReceived = 0;
            poLine.QuantityInvoiced = 0;
            poLine.ReceiptLineNumber = 0;
            poLine.Status = 0;
            poLine.LineAmount = 0;
            poLine.UnitOfMeasureCode = string.Empty;
            poLine.QtyPerUnitOfMeasure = qtyPer;
            poLine.CrossReferenceNumber = string.Empty;
            poLine.CategoryCode = string.Empty;
            poLine.ItemName = itemName;
            poLine.UnitPrice = price;

            _context.PurchaseOrderLines.Add(poLine);
            await _context.SaveChangesAsync();
            var lines = await GetLineAsync(poLineAdd.DocumentNumber, poLine.LineNumber);

            return lines;
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> CreateMultipleLinesAsync(List<POLineAddOrUpdate> poLineAddList)
    {
        if (poLineAddList == null || poLineAddList.Count == 0)
        {
            return ApiResponse.S400("No line items provided.");
        }

        try
        {
            var documentNumber = poLineAddList.First().DocumentNumber;

            var currentLineNumber = await _context.PurchaseOrderLines
                .Where(x => x.DocumentNumber == documentNumber)
                .OrderByDescending(x => x.LineNumber)
                .Select(x => x.LineNumber)
                .FirstOrDefaultAsync();

            var newLines = new List<PurchaseOrderLine>();

            foreach (var poLineAdd in poLineAddList)
            {
                var (price, qtyPer, message) = GetPriceAndQtyFromUnitOfMeasure(poLineAdd);

                if (qtyPer == 0 || price == 0)
                {
                    return ApiResponse.S404($"Invalid price/qty for item {poLineAdd.ItemNumber}: {message}");
                }

                var itemName = GetItemName(poLineAdd.ItemNumber);
                if (string.IsNullOrEmpty(itemName))
                {
                    return ApiResponse.S404($"Item number '{poLineAdd.ItemNumber}' does not exist.");
                }

                currentLineNumber++;

                var poLine = BuildPurchaseOrderLine(poLineAdd, price, qtyPer, itemName, currentLineNumber);
                newLines.Add(poLine);
            }

            await _context.PurchaseOrderLines.AddRangeAsync(newLines);
            await _context.SaveChangesAsync();

            var lines = await GetLinesAsync(documentNumber);

            return lines;
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> UpdateLineAsync(POLineAddOrUpdate poLineUpdate)
    {
        try
        {
            var (price, qtyPer, message) = GetPriceAndQtyFromUnitOfMeasure(poLineUpdate);

            if (qtyPer == 0 || price == 0)
            {
                return ApiResponse.S404(message);
            }
            
            var itemName = GetItemName(poLineUpdate.ItemNumber);
            if (string.IsNullOrEmpty(itemName))
            {
                return ApiResponse.S404("Item number does not exist");
            }
            var poLine = await _context.PurchaseOrderLines
                .FirstOrDefaultAsync(x => x.DocumentNumber == poLineUpdate.DocumentNumber
                                          && x.RowId == poLineUpdate.RowID);

            _mapper.Map(poLineUpdate, poLine);
            poLine.UnitPrice = price;
            poLine.QtyPerUnitOfMeasure = qtyPer;
            poLine.ItemName = itemName;

            await _context.SaveChangesAsync();
            return ApiResponse.S200();
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.GetBaseException().Message);

        }
    }
    public async Task<ApiResponse> UpdatePurchaseOrder(UpdatePoDto updatePoDto)
    {
        try
        {
            var foundPo = await _context.PurchaseOrders.SingleOrDefaultAsync(
            p => p.VendorNumber == updatePoDto.VendorNumber
                 && p.ItemNumber == updatePoDto.ItemNumber
                 && p.CreateDate == updatePoDto.CreateDate
            );
            if (foundPo == null)
            {
                return ApiResponse.S404(_i18N["Not found"]);
            }
            var loginId = _adminManager.GetUserLogin();
            foundPo.Quantity = updatePoDto.Quantity;
            foundPo.LastUserModified = loginId;
            foundPo.LastDateModified = DateTime.Now;
            var rs = await _context.SaveChangesAsync();
            return rs != 0
                ? ApiResponse.S200(_i18N["Operation Successful"])
                : ApiResponse.S404(_i18N["Operation Failed"]);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> UpdatePurchaseOrderAfterCreatePO(UpdatePoDto updatePoDto)
    {
        try
        {
            var foundPo = await _context.PurchaseOrders.SingleOrDefaultAsync(
            p => p.VendorNumber == updatePoDto.VendorNumber
                 && p.ItemNumber == updatePoDto.ItemNumber
                 && p.CreateDate == updatePoDto.CreateDate
            );
            if (foundPo == null)
            {
                return ApiResponse.S404(_i18N["Not found"]);
            }
            var loginId = _adminManager.GetUserLogin();
            foundPo.TotalOrder += updatePoDto.Quantity;
            foundPo.Quantity = 0;
            foundPo.LastUserModified = loginId;
            foundPo.LastDateModified = DateTime.Now;
            var rs = await _context.SaveChangesAsync();
            return rs != 0
                ? ApiResponse.S200(_i18N["Operation Successful"])
                : ApiResponse.S404(_i18N["Operation Failed"]);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> DeleteLine(string documentNumber, string itemNumber, int rowId)
    {
        try
        {
            var poLine = await _context.PurchaseOrderLines
                .FirstOrDefaultAsync(h => h.DocumentNumber == documentNumber
                                          && h.ItemNumber == itemNumber
                                          && h.RowId == rowId);
            if (poLine == null)
            {
                return ApiResponse.S404("Could not find po line");
            }

            _context.PurchaseOrderLines.Remove(poLine);
            await _context.SaveChangesAsync();

            return ApiResponse.S200();
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.GetBaseException().Message);
        }
    }

    private (decimal price, decimal qtyPer, string message) GetPriceAndQtyFromUnitOfMeasure(POLineAddOrUpdate poLineAdd)
    {
        try
        {
            var unitPrice = 0m;
            var qtyPerUnitOfMeasure = 0m;
            if (poLineAdd is null)
            {
                return (0, 0, "PO Line is null");
            }
            var filter = new ItemFilter
            {
                Number = poLineAdd.ItemNumber
            };
            var itemData = _itemManager.GetAllItems(filter, "").FirstOrDefault();
            if (itemData == null)
            {
                return (unitPrice, qtyPerUnitOfMeasure, "Item not found");
            }
            if (itemData.SalesPrices == null)
            {
                unitPrice = 0m;
                return (unitPrice, qtyPerUnitOfMeasure, "Unit price cannot be zero, Please sync data in OMS");
            }
            if (itemData.ItemUnitOfMeasures == null)
            {
                qtyPerUnitOfMeasure = 0m;
                return (unitPrice, qtyPerUnitOfMeasure, "Quantity per unit of measure cannot be zero, Please sync data in OMS");
            }

            var salesPrice = itemData.SalesPrices.FirstOrDefault(x
                => x.ItemNumber == itemData.Number &&
                   x.UnitOfMeasureCode != null &&
                   x.UnitOfMeasureCode.Equals(poLineAdd.UnitOfMeasure,
                   StringComparison.CurrentCultureIgnoreCase));

            // Giá bán == 0 là sản phẩm KM, tặng kèm. Gán = 1 để tránh lỗi chia cho 0
            if (salesPrice != null)
            {
                unitPrice = salesPrice.UnitPrice == 0 ? 1 : salesPrice.UnitPrice;
            }

            var unitOfMeasure = itemData.ItemUnitOfMeasures.FirstOrDefault(x
                => x.Code.Equals(poLineAdd.UnitOfMeasure, StringComparison.CurrentCultureIgnoreCase));

            if (unitOfMeasure != null)
            {
                qtyPerUnitOfMeasure = unitOfMeasure.QuantityPerUnitOfMeasure;
            }

            return (unitPrice, qtyPerUnitOfMeasure, "Success");
        }
        catch (Exception e)
        {
            return (0, 0, e.GetBaseException().Message);
        }
    }

    private string GetItemName(string itemNumber)
    {
        var filter = new ItemFilter
        {
            Number = itemNumber
        };
        var itemData = _itemManager.GetAllItems(filter, "").FirstOrDefault();
        return itemData != null ? itemData.Name : string.Empty;
    }

    private PurchaseOrderLine BuildPurchaseOrderLine(
        POLineAddOrUpdate poLineAdd, decimal price, decimal qtyPer, string itemName, int lineNumber)
    {
        var poLine = _mapper.Map<PurchaseOrderLine>(poLineAdd);
        poLine.LineNumber = lineNumber;
        poLine.Description = poLineAdd.Desc;
        poLine.ReceiptNumber = string.Empty;
        poLine.Type = 0;
        poLine.LocationCode = "TSAL";
        poLine.PostingGroup = string.Empty;
        poLine.ExpectedReceiptDate = DateTime.Now;
        poLine.Description2 = string.Empty;
        poLine.AllowInvoiceDiscount = 0;
        poLine.QuantityReceived = 0;
        poLine.OutstandingQuantity = poLineAdd.Quantity;
        poLine.QuantityToReceive = poLineAdd.Quantity;
        poLine.QuantityInvoiced = poLineAdd.Quantity;
        poLine.ReceiptLineNumber = 0;
        poLine.Status = 0;
        poLine.LineAmount = 0;
        poLine.UnitOfMeasureCode = string.Empty;
        poLine.QtyPerUnitOfMeasure = qtyPer;
        poLine.CrossReferenceNumber = string.Empty;
        poLine.CategoryCode = string.Empty;
        poLine.ItemName = itemName;
        poLine.UnitPrice = price;
        return poLine;
    }

}
