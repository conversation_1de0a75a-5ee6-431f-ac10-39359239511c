using System.Diagnostics;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Extensions;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using TS.GRPC;
using static Microsoft.AspNetCore.Http.StatusCodes;
namespace PurchaseManager.Server.Managers;
public class GrpcManager : IGrpcManager
{

    private string _clientName = "Purchasing Order";
    private readonly ILogger<GrpcManager> _logger;
    private readonly IVendorManager _vendorManager;
    private readonly IItemManager _itemManager;

    private readonly IStringLocalizer<Global> L;
    public GrpcManager(ILogger<GrpcManager> logger, IStringLocalizer<Global> l, IVendorManager vendorManager, IItemManager itemManager)
    {
        _logger = logger;
        L = l;
        _vendorManager = vendorManager;
        _itemManager = itemManager;

    }
    public async Task<ApiResponse> SyncItem(SyncRequest syncRequest, CancellationToken cancellationToken)
    {
        try
        {
            var channel = HttpExtension.GetGrpcChannel(syncRequest.Domain, syncRequest.Token);
            var client = new ItemService.ItemServiceClient(channel);
            var request = new SyncReq { ClientName = _clientName };
            var stopwatch = Stopwatch.StartNew();
            var caller = client.POSyncItem(request);
            //Read response from gRPC Server
            while (await caller.ResponseStream.MoveNext(cancellationToken))
            {
                var user = caller.ResponseStream.Current;
                // TODO: update Items found here 
                Console.WriteLine($"{user.ListItem.Count}");
            }
            return new ApiResponse(Status200OK, L["Sync Item successfully"], true);
        }
        catch (Exception ex)
        {
            var errorMsg = ex.GetBaseException().Message;
            _logger.LogError("Sync Item has error(s): " + errorMsg);
            return new ApiResponse(Status500InternalServerError, errorMsg, false);
        }
    }

    public async Task<ApiResponse> SyncIUOM(SyncRequest syncRequest, CancellationToken cancellationToken)
    {
        var internalResp = new ApiResponse(Status200OK, L["Sync Item successfully"], true);
        try
        {
            var channel = HttpExtension.GetGrpcChannel(syncRequest.Domain, syncRequest.Token);
            var client = new IUOMService.IUOMServiceClient(channel);
            var request = new SyncReq { ClientName = _clientName };
            var stopwatch = Stopwatch.StartNew();
            var caller = client.POSyncItemUnitOfMeasure(request);
            //Read response from gRPC Server
            while (await caller.ResponseStream.MoveNext(cancellationToken))
            {
                var resp = caller.ResponseStream.Current;
                internalResp = await _itemManager.SyncIUOMAsync(resp.ListIUOM.ToList());

            }
            return internalResp;
        }
        catch (Exception ex)
        {
            var errorMsg = ex.GetBaseException().Message;
            _logger.LogError("Sync IUOM has error(s): " + errorMsg);
            return new ApiResponse(Status500InternalServerError, errorMsg, false);
        }
    }

    public async Task<ApiResponse> SyncUOM(SyncRequest syncRequest, CancellationToken cancellationToken)
    {
        var internalResp = new ApiResponse(Status200OK, L["Sync Item successfully"], true);

        try
        {
            var channel = HttpExtension.GetGrpcChannel(syncRequest.Domain, syncRequest.Token);
            var client = new UOMService.UOMServiceClient(channel);
            var request = new SyncReq { ClientName = _clientName };
            var stopwatch = Stopwatch.StartNew();
            var caller = client.POSyncUnitOfMeasure(request);
            //Read response from gRPC Server
            while (await caller.ResponseStream.MoveNext(cancellationToken))
            {
                var resp = caller.ResponseStream.Current;
                internalResp = await _itemManager.SyncUnitOfMeasureAsync(resp.ListUOM.ToList());

            }
            return internalResp;
        }
        catch (Exception ex)
        {
            var errorMsg = ex.GetBaseException().Message;
            _logger.LogError("Sync UOM has error(s): " + errorMsg);
            return new ApiResponse(Status500InternalServerError, errorMsg, false);
        }
    }


    public async Task<ApiResponse> SyncVendor(SyncRequest syncRequest, CancellationToken cancellationToken)
    {
        var internalResp = new ApiResponse(Status200OK, L["Sync Item successfully"], true);
        try
        {
            var channel = HttpExtension.GetGrpcChannel(syncRequest.Domain, syncRequest.Token);
            var client = new VendorService.VendorServiceClient(channel);
            var request = new SyncReq { ClientName = _clientName };
            var caller = client.POSyncVendor(request);
            //Read response from gRPC Server
            while (await caller.ResponseStream.MoveNext(cancellationToken))
            {
                var resp = caller.ResponseStream.Current;
                internalResp = await _vendorManager.SyncVendor(resp.ListVendor.ToList());
            }
            return internalResp;
        }
        catch (Grpc.Core.RpcException ex)
        {
            var errorMsg = ex.GetBaseException().Message;
            _logger.LogError("Sync Item has error(s): " + errorMsg);
            return new ApiResponse(Status500InternalServerError, errorMsg, false);
        }
        catch (Exception ex)
        {
            var errorMsg = ex.GetBaseException().Message;
            _logger.LogError("Sync Item has error(s): " + errorMsg);
            return new ApiResponse(Status500InternalServerError, errorMsg, false);
        }


    }
}
