﻿using AutoMapper;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Models.Account;
using Colors = PurchaseManager.Infrastructure.Storage.DataModels.Colors;
using Item = PurchaseManager.Infrastructure.Storage.DataModels.Item;
using ItemColors = PurchaseManager.Infrastructure.Storage.DataModels.ItemColors;
using ItemUnitOfMeasure = PurchaseManager.Infrastructure.Storage.DataModels.ItemUnitOfMeasure;
using SalesPrice = PurchaseManager.Infrastructure.Storage.DataModels.SalesPrice;
using UnitOfMeasure = PurchaseManager.Infrastructure.Storage.DataModels.UnitOfMeasure;
using VendorItem = PurchaseManager.Infrastructure.Storage.DataModels.VendorItem;

namespace PurchaseManager.Storage.Mapping;

public class ItemMappingProfile : Profile
{
    public ItemMappingProfile()
    {
        CreateMap<Item, CreateItemDto>().ReverseMap().ForMember(destinationMember: dest => dest.RowId, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.ItemUnitOfMeasures, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: dest => dest.SalesPrices, memberOptions: opt => opt.Ignore());// Ignore or configure this properly
        CreateMap<Item, UpdateItemDto>().ReverseMap();
        CreateMap<UnitOfMeasure, CreateUnitOfMeasureDto>().ReverseMap();
        CreateMap<UnitOfMeasure, DetailUnitOfMeasureDto>().ReverseMap();
        CreateMap<UnitOfMeasure, UpdateUnitOfMeasureDto>().ReverseMap();
        CreateMap<UserViewModel, GetUserDto>().ReverseMap();
        CreateMap<ItemUnitOfMeasure, CreateItemUnitOfMeasureDto>();
        CreateMap<ItemUnitOfMeasure, UpdateItemUnitOfMeasureDto>().ReverseMap();
        CreateMap<SalesPrice, UpdateSalesPriceDto>().ReverseMap().ForMember(destinationMember: sp => sp.ItemNumber, memberOptions: opt => opt.Ignore())
            .ForMember(destinationMember: sp => sp.StartingDate, memberOptions: opt => opt.Ignore()).ForMember(destinationMember: sp => sp.UnitOfMeasureCode, memberOptions: opt => opt.Ignore());

        // Item mapping

        CreateMap<Item, DetailItemDto>().ForMember(destinationMember: dest => dest.Number, memberOptions: opt => opt.MapFrom(src => src.Number))
            .ForMember(destinationMember: dest => dest.Name, memberOptions: opt => opt.MapFrom(src => src.Name))
            .ForMember(destinationMember: dest => dest.Description, memberOptions: opt => opt.MapFrom(src => src.Description));
        CreateMap<SalesPrice, DetailSalesPriceDto>();
        CreateMap<ItemUnitOfMeasure, DetailItemUnitOfMeasureDto>();
        CreateMap<UpdateItemDto, DetailItemDto>().ReverseMap();
        CreateMap<UnitOfMeasure, UnitOfMeasureDto>();
        CreateMap<CreateItemUnitOfMeasureDto, UnitOfMeasureDto>();
        CreateMap<UnitOfMeasureDto, CreateItemUnitOfMeasureDto>();
        CreateMap<UpdateItemUnitOfMeasureDto, DetailItemUnitOfMeasureDto>();
        CreateMap<DetailItemUnitOfMeasureDto, UpdateItemUnitOfMeasureDto>();
        CreateMap<UpdateSalesPriceDto, DetailSalesPriceDto>();
        CreateMap<DetailSalesPriceDto, UpdateSalesPriceDto>();
        CreateMap<CreateItemUnitOfMeasureDto, DetailSalesPriceDto>();
        CreateMap<DetailSalesPriceDto, CreateItemUnitOfMeasureDto>();
        CreateMap<CreateSalesPriceDto, DetailSalesPriceDto>();
        CreateMap<DetailSalesPriceDto, CreateSalesPriceDto>();

        CreateMap<CreateUnitOfMeasureDto, UnitOfMeasureDto>();
        CreateMap<UnitOfMeasureDto, CreateUnitOfMeasureDto>();

        CreateMap<UpdateUnitOfMeasureDto, UnitOfMeasureDto>();
        CreateMap<UnitOfMeasureDto, UpdateUnitOfMeasureDto>();

        CreateMap<ItemColors, ItemColorDto>();
        CreateMap<Colors, ColorDto>();
        CreateMap<ItemColorDto, InsertOrUpdateItemColorsDto>();
        CreateMap<VendorItem, DetailVendorItemDto>();
        CreateMap<UpdateVendorItemDto, VendorItem>();
        CreateMap<CreateVendorItemDto, VendorItem>();
        CreateMap<DetailVendorItemDto, UpdateVendorItemDto>();
        CreateMap<Item, VendorItem>();
        CreateMap<DetailItemDto, VendorItem>().ReverseMap();
    }
}
