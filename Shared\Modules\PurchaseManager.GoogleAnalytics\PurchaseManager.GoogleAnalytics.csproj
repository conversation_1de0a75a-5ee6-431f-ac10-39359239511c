<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Razor" Version="2.2.0"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\PurchaseManager.Shared\PurchaseManager.Shared.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Compile Update="ModuleStrings.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>ModuleStrings.resx</DependentUpon>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Update="ModuleStrings.resx">
            <Generator>PublicResXFileCodeGenerator</Generator>
            <LastGenOutput>ModuleStrings.Designer.cs</LastGenOutput>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
      <Folder Include="obj\Debug\net8.0\" />
    </ItemGroup>

    <Target Name="PostBuild" AfterTargets="PostBuildEvent">
        <Copy SourceFiles="$(TargetPath)" DestinationFolder="$(SolutionDir)Server\PurchaseManager.Server\Modules\"/>
    </Target>

</Project>
