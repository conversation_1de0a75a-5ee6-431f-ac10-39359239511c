namespace PurchaseManager.Infrastructure.Storage.DataModels;

public partial class PurchaseSuggestedPaymentLines
{
    public int DocumentType { get; set; }

    public string DocumentNumber { get; set; } = string.Empty;

    public int LineNumber { get; set; }

    public string BuyFromVendorNumber { get; set; } = string.Empty;

    public int Type { get; set; }

    public string Number { get; set; } = string.Empty;

    public string LocationCode { get; set; } = string.Empty;

    public DateTime ExpectedReceiptDate { get; set; }

    public string Description { get; set; } = string.Empty;

    public string UnitOfMeasure { get; set; } = string.Empty;

    public decimal Quantity { get; set; }

    public decimal OutstandingQuantity { get; set; }

    public decimal QtyToInvoice { get; set; }

    public decimal QtyToReceive { get; set; }

    public decimal Vat { get; set; }

    public decimal LineDiscount { get; set; }

    public decimal LineDiscountAmount { get; set; }

    public decimal Amount { get; set; }

    public decimal GrossWeight { get; set; }

    public decimal NetWeight { get; set; }

    public decimal UnitsPerParcel { get; set; }

    public decimal UnitVolume { get; set; }

    public string JobNumber { get; set; } = string.Empty;

    public string PhaseCode { get; set; } = string.Empty;

    public string TaskCode { get; set; } = string.Empty;

    public string StepCode { get; set; } = string.Empty;

    public decimal OutstandingAmount { get; set; }

    public decimal QuantityReceived { get; set; }

    public decimal QuantityInvoiced { get; set; }

    public string ReceiptNumber { get; set; } = string.Empty;

    public decimal Profit { get; set; }

    public string PayToVendorNumber { get; set; } = string.Empty;

    public string TransactionType { get; set; } = string.Empty;

    public string TransportMethod { get; set; } = string.Empty;

    public string EntryPoint { get; set; } = string.Empty;

    public string Area { get; set; } = string.Empty;

    public string TransactionSpecification { get; set; } = string.Empty;

    public string VatbusPostingGroup { get; set; } = string.Empty;

    public string VatprodPostingGroup { get; set; } = string.Empty;

    public string CurrencyCode { get; set; } = string.Empty;

    public decimal Vatamount { get; set; }

    public decimal UnitCost { get; set; }

    public int Status { get; set; }

    public decimal LineAmount { get; set; }

    public decimal VatbaseAmount { get; set; }

    public string ProdOrderNumber { get; set; } = string.Empty;

    public string VariantCode { get; set; } = string.Empty;

    public decimal QtyPerUnitOfMeasure { get; set; }

    public string CrossReferenceNumber { get; set; } = string.Empty;

    public string PurchasingCode { get; set; } = string.Empty;

    public DateTime RequestedReceiptDate { get; set; }

    public DateTime PromisedReceiptDate { get; set; }

    public string LeadTimeCalculation { get; set; } = string.Empty;

    public string InboundWahouesHandlingTime { get; set; } = string.Empty;

    public DateTime PlannedReceiptDate { get; set; }

    public DateTime OrderDate { get; set; }

    public string ReturnReasonCode { get; set; } = string.Empty;

    public string RoutingNumbre { get; set; } = string.Empty;

    public string OperationNo { get; set; } = string.Empty;

    public string WorkCenterNumber { get; set; } = string.Empty;

    public decimal OverheadRate { get; set; }

    public int PlanningFlexibility { get; set; }

    public string SafetyLeadTime { get; set; } = string.Empty;

    public int RoutingReferenceNumber { get; set; }

    public string OriginalCountry { get; set; } = string.Empty;

    public int ApplyToItemEntry { get; set; }

    public string VatregistrationNumber { get; set; } = string.Empty;

    public string Vatdescription { get; set; } = string.Empty;

    public string ExternalDocumentNo { get; set; } = string.Empty;

    public string ReasonCode { get; set; } = string.Empty;

    public string CertificateNumber { get; set; } = string.Empty;

    public DateTime IssueDate { get; set; }

    public string PlaceOfIssue { get; set; } = string.Empty;

    public string BankAddress { get; set; } = string.Empty;

    public string BankCode { get; set; } = string.Empty;

    public string BankName { get; set; } = string.Empty;

    public string PersonIncharge { get; set; } = string.Empty;

    public string SalespersonCode { get; set; } = string.Empty;

    public string LotNumber { get; set; } = string.Empty;

    public DateOnly? ExpirationDate { get; set; }

    public string ExperimentNumber { get; set; } = string.Empty;

    public decimal? Usage { get; set; }

    public string PostingGroup { get; set; } = string.Empty;

    public string ItemClasses { get; set; } = string.Empty;

    public int? PurchaseType { get; set; }

    public decimal? QuantityShipped { get; set; }

    public int? CreditType { get; set; }
    public int RowId { get; set; }

}
