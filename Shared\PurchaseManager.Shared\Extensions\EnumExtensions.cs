﻿using System.ComponentModel.DataAnnotations;
using System.Reflection;
using PurchaseManager.Shared.Models;

namespace PurchaseManager.Shared.Extensions;

public static class EnumExtensions
{
    public static string GetDisplayName(this Enum enumValue)
    {
        string displayName;
        displayName = enumValue.GetType()
            .GetMember(enumValue.ToString())
            .FirstOrDefault()
            .GetCustomAttribute<DisplayAttribute>()?
            .GetName();

        if (string.IsNullOrEmpty(displayName))
        {
            displayName = enumValue.ToString();
        }

        return displayName;
    }


    public static List<SelectItem<int>> ToSelectItemList(this Type enumType)
    {
        if (!enumType.IsEnum)
            throw new ArgumentException("Type must be an enum");

        return Enum.GetValues(enumType)
            .Cast<Enum>()
            .Select(e => new SelectItem<int>
            {
                Id = Convert.ToInt32(e),
                DisplayValue = e.GetDisplayName(),
                Selected = false
            })
            .ToList();
    }
}
