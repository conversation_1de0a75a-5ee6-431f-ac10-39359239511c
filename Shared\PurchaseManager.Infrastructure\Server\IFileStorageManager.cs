﻿using Microsoft.AspNetCore.Http;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.FileStorage;
using PurchaseManager.Shared.Models.FileStorage;
namespace PurchaseManager.Infrastructure.Server;

public interface IFileStorageManager
{
    Task<ApiResponse> SaveFilesAsync(List<IFormFile> files, string documentNo, string prefix);
    Task<ApiResponse> UpdateFileAsync(String fileId, IFormFile file, String description);
    IQueryable<DetailFileDto> GetFilesAsync(FileStorageFilter filter, String queryString);
    Task<ApiResponse> DeleteFileAsync(string fileId);
}
