using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Table("QueueGenPurchaseOrder")]
[PrimaryKey("VendorNumber", "ItemNumber", "BrandCode", "DocumentType")]
public class QueueGenPurchaseOrder
{
    [Key]
    [Column(Order = 0)]
    [StringLength(100)]
    public string VendorNumber { get; set; }
    [Key]
    [Column(Order = 1)]
    [StringLength(100)]
    public string ItemNumber { get; set; }
    [Key]
    [Column(Order = 2)]
    [StringLength(100)]
    public string BrandCode { get; set; }
    [Required]
    [StringLength(100)]
    public string ItemName { get; set; }
    [Required]
    [StringLength(50)]
    public string PurchaseUnitOfMeasure { get; set; }
    public int Quantity { get; set; }
    [Required]
    public DateTime CreateDate { get; set; }
    public bool IsGenerated { get; set; }
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }
    public int QuantityPerUnitOfMeasure { get; set; }
    public string? Description { get; set; } = string.Empty;
    public decimal PriceB4VAT { get; set; }
    public decimal VAT { get; set; }
    [Key]
    public int DocumentType { get; set; }
    [StringLength(100)]
    public string BaseUnit { get; set; } = null!;
}
