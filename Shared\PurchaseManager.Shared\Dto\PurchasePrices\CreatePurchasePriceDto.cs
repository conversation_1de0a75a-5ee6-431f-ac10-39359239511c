﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PurchaseManager.Shared.Dto.PurchasePrices;

public class CreatePurchasePriceDto
{
    public string RowId { get; set; }
    public string VendorNumber { get; set; }
    public string ItemNumber { get; set; }
    [Required]
    public string PurchasingUnit { get; set; }
    [Required]
    [Range(1000, double.MaxValue)]
    public decimal Price { get; set; }
    public int VAT { get; set; }
    public string FoCPromotion { get; set; } = string.Empty;
    public int DiscountBySkus { get; set; }
    public string GroupProduct { get; set; } = string.Empty;
    [NotMapped]
    public List<string> ListUnitToChoose { get; set; } = [];
}
