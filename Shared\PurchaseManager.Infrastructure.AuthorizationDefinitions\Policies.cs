﻿namespace PurchaseManager.Infrastructure.AuthorizationDefinitions;

public static class Policies
{
    public const string IsAdmin = "IsAdministrator";
    public const string IsUser = "IsUser";
    public const string IsMyEmailDomain = "IsMyEmailDomain";
    public const string TwoFactorEnabled = "TwoFactorEnabled";
    public const string IsVendor = "IsVendor";
    public const string IsPurchaseManager = "IsPurchaseManager";
    public const string IsMKT = "IsMKT";
    public const string IsPurchaseUser = "IsPurchaseUser";
    public const string IsWarehouseManager = "IsWarehouseManager";
    public const string IsWarehouseUser = "IsWarehouseUser";
    public const string IsTSUser = "IsTSUser";
    public const string IsVendorAndPurchaseUser = "IsVendorAndPurchaseUser";
    public const string IsVendorContact = "IsVendorContact";
}
