﻿namespace PurchaseManager.Shared.Dto.StockOrder;

public class GetStockOrderDto
{
    public int Index { get; set; }// stt

    public string Number { get; set; } = null!;
    public string HeaderNumber { get; set; } = null!;
    public int LineNumber { get; set; }
    public int DocumentType { get; set; }
    public string ItemNumber { get; set; } = null!;
    public string LotNo { get; set; } = null!;
    public DateTime ExpirationDate { get; set; }
    public string ExpirationDateText
    {
        get => ExpirationDate.ToString("dd/MM/yyyy");
        set => _ = value; // không gán trực tiếp để tránh lỗi, xử lý trong OnDateChanged
    }
    public int TotalQuantity { get; set; }
    public bool IsAdding { get; set; }
    public int QuantityReceived { get; set; }
    public string ItemName { get; set; } = null!;
    public string Note { get; set; } = null!;
    public string CreateBy { get; set; } = null!;
    public int Status { get; set; } = 1; // 1: Draft, 2: Saved
    public bool HasError { get; set; }
    public string ErrorText { get; set; }
    public bool IsCreateSO { get; set; }

}
