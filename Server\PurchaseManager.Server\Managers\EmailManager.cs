﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using MimeKit;
using NetTopologySuite.Utilities;
using Newtonsoft.Json;
using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Aop;
using PurchaseManager.Shared.Dto.Email;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Email;
using PurchaseManager.Storage;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Managers;

[ApiResponseException]
public class EmailManager : IEmailManager
{
    private readonly EmailConfiguration _emailConfiguration;
    private readonly IEmailFactory _emailFactory;
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<EmailManager> _logger;
    private readonly ApplicationPersistenceManager _persistenceManager;
    private readonly IMapper _mapper;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IStringLocalizer<Global> _i18N;
    private readonly IAdminManager _adminManager;
    private readonly IPOHeaderManager _poHeaderManager;

    public static SemaphoreSlim QueueSync { get; } = new SemaphoreSlim(0, 1);

    public EmailManager(
        ITenantSettings<EmailConfiguration> emailConfiguration,
        IEmailFactory emailFactory,
        ApplicationDbContext dbContext,
        ILogger<EmailManager> logger, ApplicationPersistenceManager persistenceManager, IMapper mapper,
        IHttpContextAccessor httpContextAccessor, IAdminManager adminManager,
        IPOHeaderManager poHeaderManager, IStringLocalizer<Global> i18N)
    {
        _emailConfiguration = emailConfiguration.Value;
        _emailFactory = emailFactory;
        _dbContext = dbContext;
        _logger = logger;
        _persistenceManager = persistenceManager;
        _mapper = mapper;
        _httpContextAccessor = httpContextAccessor;
        _adminManager = adminManager;
        _poHeaderManager = poHeaderManager;
        _i18N = i18N;
    }

    //Used by API
    public async Task<ApiResponse> SendTestEmail(EmailRequestDto emailRequest)
    {
        var email = _emailFactory.BuildEmail(emailRequest);
        email.ToAddresses.AddRange(emailRequest.ToAddresses);
        email.TemplateId = emailRequest.TemplateId;
        if (emailRequest.CcAddresses != null)
        {
            email.CcAddresses.AddRange(emailRequest.CcAddresses);
        }
        if (emailRequest.BccAddresses != null)
        {
            email.BccAddresses.AddRange(emailRequest.BccAddresses);
        }
        // email.BccAddresses.Add(new EmailAddressDto(emailRequest.FromName, emailRequest.FromAddress));
        return await QueueEmail(email, EmailType.Test);
    }

    public IQueryable<DetailEmailDto> GetEmails(EmailFilter filter)
    {
        var emails = _persistenceManager.GetEntities<QueuedEmail>().AsNoTracking()
            .Include(h => h.EmailsNavigation)
            .Where(h => (filter.Query == null || h.Email.Contains(filter.Query)) &&
                        (filter.Subject == null || h.Email.Contains(filter.Subject)) &&
                        (filter.ToEmail == null || h.Email.Contains(filter.ToEmail))
            )
            .OrderByDescending(h => h.Id);

        //convert to dto



        var result = _mapper.ProjectTo<DetailEmailDto>(emails);
        return result;
    }
    public IQueryable<DetailEmailTemplate> GetEmailTemplates(EmailTemplateFilter filter)
    {
        var emails = _persistenceManager.GetEntities<EmailTemplates>().AsNoTracking()
            .Include(h => h.QueuedEmail)
            .Where(h => (filter.Query == null || h.TemplateName.Contains(filter.Query)) &&
                        (filter.TemplateName == null || h.TemplateName.Contains(filter.TemplateName)) &&
                        (filter.EmailTemplatesId == null || h.EmailTemplatesId == filter.EmailTemplatesId)
            )
            .OrderByDescending(h => h.EmailTemplatesId);
        return _mapper.ProjectTo<DetailEmailTemplate>(emails);
    }
    public async Task<ApiResponse> CreateEmailTemplate(CreateEmailTemplateDto createEmailTemplateDto)
    {
        try
        {
            var emailTemplate = _mapper.Map<EmailTemplates>(createEmailTemplateDto);
            emailTemplate.LoginId = GetUserName();
            emailTemplate.Hidden = false;
            _dbContext.EmailTemplates.Add(emailTemplate);
            await _dbContext.SaveChangesAsync();
            return new ApiResponse(Status201Created, _i18N["Success"]);
        }
        catch (Exception e)
        {
            return new ApiResponse(Status500InternalServerError, e.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> UpdateEmailTemplate(int emailTemplateId, UpdateEmailTemplateDto updateEmailTemplateDto)
    {
        try
        {
            var emailTemplate = await _dbContext.EmailTemplates.SingleOrDefaultAsync(s => s.EmailTemplatesId == emailTemplateId);
            if (emailTemplate == null)
            {
                return new ApiResponse(Status404NotFound, _i18N["Not found"]);
            }

            _mapper.Map(updateEmailTemplateDto, emailTemplate);
            emailTemplate.LastDateModified = DateTime.UtcNow.AddHours(7);
            //_dbContext.EmailTemplates.Update(emailTemplate);
            _dbContext.SaveChanges();
            return new ApiResponse(Status200OK, _i18N["Success"]);
        }
        catch (Exception e)
        {
            return new ApiResponse(Status500InternalServerError, e.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> QueueEmail(EmailMessageDto emailMessage, EmailType emailType)
    {
        try
        {
            _dbContext.QueuedEmails.Add(new QueuedEmail
            {
                Email = JsonConvert.SerializeObject(emailMessage),
                EmailType = emailType,
                EmailTemplatesId = emailMessage.TemplateId,
                CreatedOn = DateTime.UtcNow.AddHours(7),
                PoNumber = emailMessage.PoNumber,
                VendorNumber = emailMessage.VendorNumber,
            });
            await _dbContext.SaveChangesAsync();

            QueueSync.Release();

            var msg = $"Email to {string.Join(" - ", emailMessage.ToAddresses.Select(i => i.Address))} queued";

            _logger.LogInformation(msg);

            return new ApiResponse(Status200OK, msg);
        }
        catch (Exception ex)
        {
            _logger.LogError($"QueueEmail failed {ex.GetBaseException().Message} {ex.StackTrace}");

            return new ApiResponse(Status500InternalServerError, ex.GetBaseException().Message);
        }
    }

    public async Task<ApiResponse> SendEmail(EmailMessageDto emailMessage, EmailDataDto emailDataDto = null, EmailType emailType = EmailType.Common)
    {
        try
        {
            var message = new MimeMessage();

            if (emailMessage.FromAddresses.Count == 0)
            {
                emailMessage.FromAddresses.Add(new EmailAddressDto(_emailConfiguration.FromName, _emailConfiguration.FromAddress));
            }

            message.To.AddRange(emailMessage.ToAddresses.Select(x => new MailboxAddress(x.Name, x.Address)));
            message.From.AddRange(emailMessage.FromAddresses.Select(x => new MailboxAddress(x.Name, x.Address)));
            message.Cc.AddRange(emailMessage.CcAddresses.Select(x => new MailboxAddress(x.Name, x.Address)));
            message.Bcc.AddRange(emailMessage.BccAddresses.Select(x => new MailboxAddress(x.Name, x.Address)));

            var bodyBuilder = new BodyBuilder();
            SentEmail sentEmail = null;
            if (emailDataDto?.Attachments != null)
                // add a file into email
            {
                foreach (var att in emailDataDto.Attachments)
                {
                    bodyBuilder.Attachments.Add(att.Key, att.Value);
                }
            }

            message.Sender = new MailboxAddress(_emailConfiguration.FromName, _emailConfiguration.FromAddress);
            message.Subject = emailMessage.Subject;

            bodyBuilder.HtmlBody = emailMessage.Body;
            message.Body = emailMessage.IsHtml ? bodyBuilder.ToMessageBody() : new TextPart("plain") { Text = emailMessage.Body };

            //Be careful that the SmtpClient class is the one from Mailkit not the framework!
            using var emailClient = new MailKit.Net.Smtp.SmtpClient();

            if (_emailConfiguration.SmtpUseSSL)
            {
                emailClient.SslProtocols = _emailConfiguration.SmtpSslProtocol;
            }
            else
            {
                emailClient.ServerCertificateValidationCallback = (_, _, _, _) => true;
            }


            await emailClient.ConnectAsync(_emailConfiguration.SmtpServer, _emailConfiguration.SmtpPort);

            //Remove any OAuth functionality as we won't be using it.
            emailClient.AuthenticationMechanisms.Remove("XOAUTH2");

            if (!string.IsNullOrWhiteSpace(_emailConfiguration.SmtpUsername))
            {
                await emailClient.AuthenticateAsync(_emailConfiguration.SmtpUsername, _emailConfiguration.SmtpPassword);
            }

            await emailClient.SendAsync(message);

            await emailClient.DisconnectAsync(true);

            var msg = $"Email successfully sent to {string.Join(" - ", message.To.Select(i => i.Name))}";

            if (emailDataDto != null)
            {
                sentEmail = new SentEmail
                {
                    Type = (int)emailType,
                    Body = emailMessage.Body,
                    PoNumber = emailDataDto.PONumber,
                    SentBy = _adminManager.GetUserLogin(),
                    SentAt = DateTime.UtcNow.AddHours(7),
                    VendorNumber = emailDataDto.VendorNumber
                };
            }

            if (sentEmail != null)
            {
                _dbContext.SentEmails.Add(sentEmail);
            }
            await _dbContext.SaveChangesAsync();
            return new ApiResponse(Status200OK, msg);
        }
        catch (Exception ex)
        {
            return new ApiResponse(Status500InternalServerError, ex.GetBaseException().Message);
        }
    }
    private string GetUserName()
    {
        return _httpContextAccessor.HttpContext!.User.Identity!.Name!.ToUpper();
    }

    public DetailEmailTemplate GetEmailTemplateById(int emailTemplateId)
    {
        var emails = _persistenceManager.GetEntities<EmailTemplates>().AsNoTracking()
      .Include(h => h.QueuedEmail)
      .Where(h => h.EmailTemplatesId == emailTemplateId)
      .OrderByDescending(h => h.EmailTemplatesId);

        if (!emails.Any()) return null;
        return _mapper.ProjectTo<DetailEmailTemplate>(emails).FirstOrDefault();
    }

    public async Task<ApiResponse> SendEmailDetailPOToVendor(EmailInfoInPODetailDto emailInfoInPODetailDto)
    {
        try
        {
            //get contact info or vendor info
            var vendorNumber = emailInfoInPODetailDto.VendorNumber;

            var contacts = _dbContext.Contacts.AsNoTracking().Where(h => h.VendorNumber.Trim() == vendorNumber.Trim());
            string name = "";
            string email = "";

            if (!contacts.Any())
            {
                var foundVendor = _dbContext.Vendors.AsNoTracking()
                    .FirstOrDefault(x => x.Number.Trim() == vendorNumber.Trim());
                if (foundVendor == null)
                {
                    return new ApiResponse(Status404NotFound, "Vendor info not found. Try again");
                }
                name = foundVendor.Name;
                email = foundVendor.Email;
            }
            else
            {
                var foundContact = contacts.FirstOrDefault();
                if (foundContact != null)
                {
                    name = foundContact.Name;
                    email = foundContact.Email;
                }
            }
            // prepare req  
            if (string.IsNullOrEmpty(name) || string.IsNullOrEmpty(email))
            {
                return new ApiResponse(Status404NotFound,
                _i18N["Vendor or Contact has not email or name set yet. Let's check it again"]);
            }
            var toEmail = new EmailAddressDto(name, email);
            EmailMessageDto req = new EmailMessageDto
            {
                IsHtml = true,
                Body = emailInfoInPODetailDto.Body,
                Subject = emailInfoInPODetailDto.Subject,
                ToAddresses = new List<EmailAddressDto> { toEmail },
                TemplateId = emailInfoInPODetailDto.EmailTemplatesId
            };
            // get file to attach to email 
            var attachment = await _poHeaderManager.ViewPDF(emailInfoInPODetailDto.PONumber);

            if (attachment is null)
            {
                return new ApiResponse(Status500InternalServerError, _i18N["Cannot add attachment at this time. Please try again!"]);
            }

            var attachmentParams = new Dictionary<string, byte[]>
            {
                {"TS_" +  emailInfoInPODetailDto.PONumber + ".pdf", attachment }
            };
            // send email and return resp
            var data = new EmailDataDto
            {
                Attachments = attachmentParams,
                PONumber = emailInfoInPODetailDto.PONumber,
                VendorNumber = emailInfoInPODetailDto.VendorNumber.Trim(),
            };
            return await SendEmail(req, data, EmailType.DetailPO);
        }
        catch (Exception ex)
        {
            return new ApiResponse(Status500InternalServerError, ex.GetBaseException().Message);
        }

    }
    public IQueryable<SentEmailDto> GetAllSentEmail(SentEmailFilter filter)
    {
        var queryData = _dbContext.SentEmails.AsNoTracking().Where(c =>
                (filter.SentAt == null || c.SentAt.Month == filter.SentAt.Value.Month && c.SentAt.Year == filter.SentAt.Value.Year)
                && (filter.SentBy == null || c.SentBy.Contains(filter.SentBy))
                && (filter.PoNumber == null || c.PoNumber.Contains(filter.PoNumber))
                && (filter.VendorNumber == null || c.VendorNumber.Contains(filter.VendorNumber))
                && (filter.Type == null || c.Type == (int)filter.Type)
                );
        var respData = _mapper.ProjectTo<SentEmailDto>(queryData);
        return respData;
    }
}
