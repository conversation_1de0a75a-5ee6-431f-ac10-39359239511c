﻿@using PurchaseManager.Shared.Models.Account
@using Microsoft.AspNetCore.Components
@inject AuthenticationStateProvider authStateProvider
<AuthorizeView>
    <Authorized>
        <MudMenu AnchorOrigin="Origin.BottomRight" Dense=true Variant="Variant.Text" Class="mx-1 px-3" Size="Size.Small"
            Label="Account" LockScroll="true" Color="Color.Inherit" StartIcon="@Icons.Material.Outlined.Person"
            EndIcon="@Icons.Material.Outlined.KeyboardArrowDown" TransformOrigin="Origin.TopRight">
            <ActivatorContent>

                    <MudAvatar Color="Color.Success" Size="Size.Medium" Variant="Variant.Outlined">
                @if (!string.IsNullOrEmpty(userViewModel?.UserName))
                {
                        @userViewModel.UserName.Substring(0, 2).ToUpper()
                }
                else
                {
                    <span>A</span>
                }
                    </MudAvatar>
            </ActivatorContent>
            <ChildContent>
                @if (userViewModel != null)
                {
                    <div class="py-4" style="min-width: 220px">
                        <MudText Class="px-6 pb-2" Typo="Typo.body2">
                            <b>@userViewModel.FirstName @userViewModel.LastName </b>
                        </MudText>
                        <MudText Class="px-6 pb-2" Typo="Typo.body2">
                            @userViewModel.Email
                        </MudText>
                        <MudText Class="px-6 pb-2" Typo="Typo.caption">
                            Xin chào: @userViewModel.UserName
                        </MudText>
                        <MudDivider Class="my-2" />
                        <MudMenuItem Href="/account/profile">
                            <div class="d-flex">
                                <MudIcon Class="mx-2" Icon="@Icons.Material.Filled.Person" />
                                <MudText>Profile</MudText>
                            </div>
                        </MudMenuItem>
                        <MudMenuItem>
                            <div class="d-flex">
                                <MudIcon Class="mx-2" Icon="@Icons.Material.Filled.Settings" />
                                <MudText>Settings</MudText>
                            </div>
                        </MudMenuItem>
                        @if (userViewModel.Roles.Contains("IsAdministrator"))
                        {
                            <MudMenuItem Href="/admin">
                                <MudStack Row="true" Class="mx-2">
                                    <MudIcon Icon="@Icons.Material.Filled.Settings" />
                                    <MudText>Admin Manager</MudText>
                                </MudStack>
                            </MudMenuItem>
                        }
                        <div class="mt-4 mx-4">
                            <MudButton Color="Color.Default" FullWidth="true" OnClick="LogoutClick"
                                StartIcon="@Icons.Material.Filled.Logout" Variant="Variant.Outlined">
                                Logout
                            </MudButton>
                        </div>
                    </div>
                }
            </ChildContent>
        </MudMenu>
    </Authorized>
    <NotAuthorized>
        <div class="drawer-profile">
            <Login ShowLogInLabel="true" />
        </div>
    </NotAuthorized>
</AuthorizeView>
@code {
    [CascadingParameter]
    Task<AuthenticationState> authenticationStateTask { get; set; }
    [Inject] AppState appState { get; set; }
    UserViewModel userViewModel;

    protected async  override Task OnInitializedAsync()
    {
        userViewModel = null;
        var user = (await authenticationStateTask).User;
        if (user.Identity.IsAuthenticated)
            userViewModel = await ((IdentityAuthenticationStateProvider)authStateProvider).GetUserViewModel();

            await base.OnInitializedAsync();
    }
    async Task LogoutClick()
    {
        appState.ClearUserProfile();
        await ((IdentityAuthenticationStateProvider)authStateProvider).Logout();
    }
}