using System.Globalization;
using System.Net.Http.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Http;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Interfaces;

namespace PurchaseManager.Theme.Material.Demo.Pages;

public partial class Index : ComponentBase
{
    [Inject]
    private HttpClient HttpClient { get; set; }
    [Inject]
    private NavigationManager NavigationManager { get; set; }

    protected int countOrdersFinish;
    private List<PurchaseOrderHeader> ListDemand { get; set; } = [];
    private CountOrderTypeDto CountReplenishmentOrders { get; set; } = new CountOrderTypeDto();
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    protected PoDocNoOccurrenceDto OrderByType { get; set; } = new PoDocNoOccurrenceDto();
    protected DateTime Date { get; set; } = DateTime.Today;

    protected override async Task OnInitializedAsync()
    {
        await CountOrderDocNoOccurrent();
        await GetLeadTime();
        await FunctionCountReplenishmentOrders();
        await CountOrderFinished();
        await base.OnInitializedAsync();
    }

    protected async Task FunctionCountReplenishmentOrders()
    {
        var apiResponse = await HttpClient.GetFromJsonAsync<ApiResponseDto<CountOrderTypeDto>>($"/api/Dashboard/CountOrderTypes");
        if (apiResponse.IsSuccessStatusCode)
        {
        }
    }
    protected async Task GetLeadTime()
    {
        try
        {
            var query = new QueryString();
            query.Add("dateTime", Date.ToString(CultureInfo.InvariantCulture));
            var dateString = Date.ToString("yyyy-MM-dd");
            var apiResponse =
                await HttpClient.GetFromJsonAsync<ApiResponseDto<List<PurchaseOrderHeader>>>(
                $"api/Dashboard/GetsDocumentApprove?dateTime={dateString}");
            if (apiResponse.IsSuccessStatusCode)
            {
                ListDemand = apiResponse.Result;
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
    }

    protected async Task CountOrderFinished()
    {
        var apiResponse = await HttpClient.GetFromJsonAsync<ApiResponseDto<int>>($"/api/Dashboard/CountPoCompleted");
        if (apiResponse.IsSuccessStatusCode)
        {
            countOrdersFinish = apiResponse.Result;
        }
    }

    protected async Task CountOrderDocNoOccurrent()
    {
        try
        {
            var apiResponse =
                await HttpClient.GetFromJsonAsync<ApiResponseDto<PoDocNoOccurrenceDto>>($"api/Dashboard/CountPoDocNoOccurrence");
            if (apiResponse.IsSuccessStatusCode)
            {
                OrderByType = apiResponse.Result;
            }

        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.ToString(), ViewNotifierType.Error);
        }
    }

    protected async Task ChangeDateViewLeadTime(DateTime? date)
    {
        if (date != null)
        {
            Date = (DateTime)date;
        }
        await ReloadViewLeadTime();
    }

    protected async Task ReloadViewLeadTime() => await GetLeadTime();
    protected async Task ReloadViewReplenishment() => await FunctionCountReplenishmentOrders();
    protected async Task ReloadViewFinished() => await CountOrderFinished();
    protected async Task ReloadViewOrders() => await CountOrderDocNoOccurrent();
}
