<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
      <validation validateIntegratedModeConfiguration="false" />
      <handlers>
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath=".\PurchaseManager.Server.exe" stdoutLogEnabled="true" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
      <httpProtocol  allowKeepAlive="true">
			  <customHeaders>
				  <remove name="X-Powered-By" />
			  </customHeaders>
		  </httpProtocol>
      <security>
        <requestFiltering removeServerHeader="true">
          <!-- Handle requests up to 1 GB -->
          <requestLimits maxAllowedContentLength="1073741824" />
        </requestFiltering>
      </security>
    </system.webServer>
  </location>
</configuration>
