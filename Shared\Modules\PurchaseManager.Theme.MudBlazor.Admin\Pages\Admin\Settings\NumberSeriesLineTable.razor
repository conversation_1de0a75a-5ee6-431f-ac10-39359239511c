@page "/admin/settings/numberseriesline"
@using Breeze.Sharp
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using PurchaseManager.Shared.Models
@using System.ComponentModel
@layout AdminLayout

@inject IApiClient apiClient
@inject IViewNotifier viewNotifier
@inject IStringLocalizer<Global> L

<div style="margin: 20px; font-weight: bold; font-size: 18px">NumberSeries</div>

<MudGrid Spacing="0" Class="my-4">
    <MudItem>
        <MudAutocomplete T="string" Label="SeriesCode" @bind-Value="value" SEL SearchFunc="@Search" Variant="Variant.Outlined" />
    </MudItem>
</MudGrid>

<div style="margin: 20px; font-weight: bold; font-size: 18px">NumberSeriesLine</div>

<MudGrid Style="font-weight:bold;background-color:#ddd;margin:0" Class="align-center">
    <MudItem>
        <MudButton StartIcon="@Icons.Material.Filled.PlaylistAdd" OnClick="@((e) => OpenDialog())" Variant="Variant.Filled" Color="Color.Primary">New</MudButton>
    </MudItem>
    <MudItem xs="1" >SeriesCode</MudItem>
    <MudItem xs="1" >Code</MudItem>
    <MudItem xs="1" >Starting</MudItem>
    <MudItem xs="1" >Ending</MudItem>
    <MudItem xs="1" >Warning</MudItem>
    <MudItem xs="1" >LastNumber</MudItem>
    <MudItem xs="1" >Open</MudItem>
    <MudItem xs="1" >LastDate</MudItem>
    <MudItem xs="1" >SourceCode</MudItem>
    <MudItem xs="1" >Description</MudItem>
    <MudItem xs="1" >UserId</MudItem>
</MudGrid>
<div style="height:360px;overflow-y:auto;overflow-x:hidden;width:calc(100% + 24px);">
    <Virtualize @ref="asyncVirtualize" Context="item" ItemsProvider="LoadNumberSeriesLine" ItemSize="72" OverscanCount="20">
        <ItemContent>
            <MudGrid Class="align-center" @key="item.Code" Style="border-bottom: 1px solid #eee;">
                <MudItem xs="1" ><MudIconButton Icon="@Icons.Material.Filled.Delete" OnClick="@(() => OpenDeleteDialog(item))"></MudIconButton></MudItem>
                <MudItem xs="1" >@item.NumberSeriesCode</MudItem>
                <MudItem xs="1" >@item.Code</MudItem>
                <MudItem xs="1" >@item.StartingNumber</MudItem>
                <MudItem xs="1" >@item.EndingNumber</MudItem>
                <MudItem xs="1" >@item.WarningNumber</MudItem>
                <MudItem xs="1" >@item.LastNumberUsed</MudItem>
                <MudItem xs="1" >@item.Open</MudItem>
                <MudItem xs="1" >@item.LastDateUsed</MudItem>
                <MudItem xs="1" >@item.SourceCode</MudItem>
                <MudItem xs="1" >@item.Description</MudItem>
                <MudItem xs="1" >@item.UserId</MudItem>
            </MudGrid>
        </ItemContent>
        <Placeholder>
            <div style="border-bottom: 1px solid #eee;">
                <MudProgressCircular Color="Color.Default" Indeterminate="true" />
            </div>
        </Placeholder>
    </Virtualize>
</div>

@if (currentNumberSeriesLine != null)
{
    <MudDialog @bind-Visible="@dialogIsOpen" Options="@dialogOptions"  >
        <TitleContent>
            <MudText Typo="Typo.h6">
                <MudIcon Icon="@Icons.Material.Filled.Add" Class="mr-3 mb-n1" />
                Create
            </MudText>
        </TitleContent>
        <DialogContent>
            <EditForm id="newNumberSeriesLine" Model="@currentNumberSeriesLine" OnValidSubmit="@NewEntity">
                <MudTextField @bind-Value="@currentNumberSeriesLine.NumberSeriesCode" Label="NumberSeriesCode" Required="true" RequiredError=@L["Required"]></MudTextField>
                <MudTextField @bind-Value="@currentNumberSeriesLine.Code" Label="Code" Required="true" RequiredError=@L["Required"]></MudTextField>
                <MudTextField @bind-Value="@currentNumberSeriesLine.StartingNumber" Label="StartingNumber" Required="true" RequiredError=@L["Required"]></MudTextField>
                <MudTextField @bind-Value="@currentNumberSeriesLine.EndingNumber" Label="EndingNumber" Required="true" RequiredError=@L["Required"]></MudTextField>
                <MudTextField @bind-Value="@currentNumberSeriesLine.ProposalNumber" Label="ProposalNumber" Required="true" RequiredError=@L["Required"]></MudTextField>
                <MudTextField @bind-Value="@currentNumberSeriesLine.SourceCode" Label="SourceCode" Required="true" RequiredError=@L["Required"]></MudTextField>
                <MudTextField @bind-Value="@currentNumberSeriesLine.Description" Label="Description" Required="true" RequiredError=@L["Required"]></MudTextField>
            </EditForm>
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="@(e => { dialogIsOpen = false; })">@L["Cancel"]</MudButton>
            <MudButton ButtonType="ButtonType.Submit" form="newNumberSeriesLine" Variant="Variant.Filled" Color="Color.Primary">Create</MudButton>
        </DialogActions>
    </MudDialog>

    <MudDialog @bind-Visible="@deleteDialogOpen" Style="z-index:100">
        <TitleContent>
            <MudText Typo="Typo.h6">
                <MudIcon Icon="@Icons.Material.Filled.DeleteForever" Class="mr-3 mb-n1" />
                @L["Confirm Delete"]
            </MudText>
        </TitleContent>
        <DialogContent>
            @L["Are you sure you want to delete {0}?", currentNumberSeriesLine.Code]
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="@(e => { deleteDialogOpen = false; })">@L["Cancel"]</MudButton>
            <MudButton OnClick="@Delete" Variant="Variant.Filled" Color="Color.Error">@L["Delete"]</MudButton>
        </DialogActions>
    </MudDialog>
}

@code {
    private Virtualize<NumberSeriesLine> asyncVirtualize;
    private NumberSeriesLine currentNumberSeriesLine = new();

    private DateTime today = DateTime.Today;
    private int startIndex;
    private int pageSize;
    private int itemCount;
    private bool deleteDialogOpen = false;
    private bool dialogIsOpen = false;
    private DialogOptions dialogOptions { get; set; }

    private string value;
    private List<string> states;

    protected override async Task OnInitializedAsync()
    {
        base.OnInitialized();
        var items = await apiClient.GetNumberCode();
        states = (List<string>)items.Results;
    }

    private async Task<IEnumerable<string>> Search(string value, CancellationToken token)
    {
        // In real life use an asynchronous function for fetching data from an api.
        await Task.Delay(5);

        // if text is null or empty, show complete list
        return states;
    }

    private async ValueTask<ItemsProviderResult<NumberSeriesLine>> LoadNumberSeriesLine(ItemsProviderRequest request)
    {
        startIndex = request.StartIndex;
        pageSize = request.Count;

        var items = await apiClient.GetNumberSeriesLine(request.Count, request.StartIndex);

        itemCount = (int)items.InlineCount;

        if (request.StartIndex == 0)
            StateHasChanged();

        StateHasChanged();
        return new ItemsProviderResult<NumberSeriesLine>(items, itemCount);
    }

    public void OpenDialog()
    {
        dialogOptions = new DialogOptions()
        {
            MaxWidth = MaxWidth.Medium, FullWidth = true
        };
        currentNumberSeriesLine = new NumberSeriesLine()
        {
            NumberSeriesCode = value,
            WarningNumber = 0,
            IncrementByNumber = 0,
            LastNumberUsed = 1,
            Open = 0,
            LastDateUsed = DateTime.Today,
            SourceCode = "AL",
            UserId = "admin",
            FromDate = DateTime.Today,
            ToDate = DateTime.Today,
            Type = 0
        };
        dialogIsOpen = true;
    }

    public void OpenDeleteDialog(NumberSeriesLine number)
    {
        currentNumberSeriesLine = number;
        deleteDialogOpen = true;
    }

    public async Task NewEntity()
    {
        dialogIsOpen = false;

        try
        {
            apiClient.AddEntity(currentNumberSeriesLine);

            await apiClient.SaveChanges();

            apiClient.ClearEntitiesCache();
            await asyncVirtualize.RefreshDataAsync();

            StateHasChanged();
        }
        catch (Exception ex)
        {
            apiClient.CancelChanges();
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }

    public async Task Delete()
    {
        try
        {
            apiClient.RemoveEntity(currentNumberSeriesLine);
            await apiClient.SaveChanges();

            apiClient.ClearEntitiesCache();
            await asyncVirtualize.RefreshDataAsync();

            viewNotifier.Show($"{currentNumberSeriesLine.Code} deleted", ViewNotifierType.Success, L["Operation Successful"]);
        }
        catch (Exception ex)
        {
            apiClient.CancelChanges();
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }

        currentNumberSeriesLine = new NumberSeriesLine();

        deleteDialogOpen = false;
    }
}

