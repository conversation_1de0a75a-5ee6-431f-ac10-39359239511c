﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Table("VendorItems")]
public partial class VendorItem
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }

    [Required]
    [StringLength(100)]
    public string Name { get; set; }

    [StringLength(100)]
    [Unicode(false)]
    public string ItemNumber { get; set; }

    [Required]
    [StringLength(100)]
    [Unicode(false)]
    public string UnitOfMeasure { get; set; }

    [Column(TypeName = "decimal(38, 0)")]
    public decimal? Price { get; set; }

    /// <summary>
    /// sample: 2, 5, 8, 10,...
    /// </summary>
    public int? Vat { get; set; }

    [StringLength(100)]
    public string VendorNumber { get; set; }

    /// <summary>
    /// username
    /// </summary>
    [StringLength(100)]
    [Unicode(false)]
    public string CreatedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreatedAt { get; set; }

    [StringLength(100)]
    [Unicode(false)]
    public string LastUpdatedBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? LastUpdatedAt { get; set; }

    /// <summary>
    /// status of item
    /// </summary>
    public int? Blocked { get; set; }

    [StringLength(200)]
    public string Description { get; set; }

    [ForeignKey("VendorNumber")]
    [InverseProperty("VendorItems")]
    public virtual Vendor VendorNumberNavigation { get; set; }
}
