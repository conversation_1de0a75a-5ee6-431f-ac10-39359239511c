﻿using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Db;
using Breeze.Sharp;
using PurchaseManager.Shared.Models;

namespace PurchaseManager.Shared.Interfaces
{
    public interface IMasterDataApiClient : IBaseApiClient
    {
        Task<ApiResponseDto<GetVendorDto>> BuildVendorViewModel(string number);
        Task<ApiResponseDto> UpdateVendor(GetVendorDto vendorViewModel);
        Task<ApiResponseDto<bool>> ToggleVendorStatus(string vendorNumber);// block => active | active => block
        //Task<ApiResponseDto> Upload(MultipartFormDataContent content);

    }
}
