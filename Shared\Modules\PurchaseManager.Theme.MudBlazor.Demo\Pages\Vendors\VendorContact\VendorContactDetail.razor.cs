﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Http;
using Microsoft.Data.OData.Query.SemanticAst;
using Microsoft.Extensions.Localization;
using MudBlazor.Extensions;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto.Contact;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Models.Contact;
using PurchaseManager.Shared.Providers;
namespace PurchaseManager.Theme.Material.Demo.Pages.Vendors.VendorContact;
public partial class VendorContactDetail : ComponentBase
{
    #region  Parameters
    [Parameter]
    public string VendorNumber { get; set; } = string.Empty;
    [Parameter]
    public string ContactNumber { get; set; } = string.Empty;
    #endregion Parameters
    #region DIs
    [Inject] public IStringLocalizer<Global> L { get; set; }
    [Inject] protected IViewNotifier viewNotifier { get; set; }
    [Inject] protected NavigationManager navigationManager { get; set; }
    [Inject] public IApiClient apiClient { get; set; }
    [Inject] public IAccountApiClient accountApiClient { get; set; }
    [Inject] protected NavigationManager NavigationManager { get; set; }
    [Inject] protected IVendorApiClient vendorApiClient { get; set; }

    #endregion DIs
    #region Variables
    protected EditForm formRef { get; set; }
    protected GetContactDto currentContact { get; set; } = new();
    protected GetVendorDto currentVendor { get; set; } = new();
    protected ContactFilter contactFilter { get; set; } = new();
    protected bool isAdding { get; set; }
    protected bool isLoading { get; set; }
    protected int selectedTypeOfContact { get; set; }
    protected List<SelectItem<int>> listTypeOfContact { get; set; } = [];
    protected IdentityAuthenticationStateProvider identityAuthenticationStateProvider;

    #endregion Variables
    #region Inherited Methods
    protected override async Task OnInitializedAsync()
    {
        isLoading = true;
        GetAction();
        GetTypeOfContact();
        if (!isAdding && !string.IsNullOrEmpty(ContactNumber)) await GetContactDetailByVendorNumberAsync();
        else await GetVendorInfoAsync();
        await base.OnInitializedAsync();
        isLoading = false;
    }
    #endregion  Inherited Methods
    #region Api calls
    protected async Task GetContactDetailByVendorNumberAsync()
    {
        try
        {
            contactFilter.Number = ContactNumber;
            var resp = await apiClient.GetAllContacts(contactFilter);
            if (!resp.Results.Any() || resp.Results.FirstOrDefault() is null)
                viewNotifier.Show("No Data", ViewNotifierType.Error, L["Operation Failed!"]);
            else
            {
                currentContact = resp.Results.FirstOrDefault();
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }

    protected async Task GetVendorInfoAsync()
    {
        try
        {
            var resp = await vendorApiClient.GetVendorsByNumber(VendorNumber);
            if (!resp.IsSuccessStatusCode || resp.Result is null)
                viewNotifier.Show("No Data", ViewNotifierType.Error, L["Operation Failed!"]);
            else
            {
                currentVendor = resp.Result.FirstOrDefault();
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    protected async Task OnCreateNewAccountForContactAsync()
    {
        try
        {
            var resp = await accountApiClient.RegisterContactAccountAsync(new CreateAccountContactDto
            {
                ContactNumber = currentContact.Number,
                VendorNumber = currentContact.VendorNumber,
                Email = currentContact.Email?.Trim(),
                UserName = currentContact.Phone?.Trim(),
            });
            if (resp.IsSuccessStatusCode && resp.StatusCode == StatusCodes.Status201Created)
            {
                await GetContactDetailByVendorNumberAsync();
                viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation Successfully"]);
            }
            else viewNotifier.Show(resp.Result is not null ? resp.Result.ToString() : resp.Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }


    #endregion Api calls
    protected void GetAction()
    {
        isAdding = navigationManager.Uri.Contains("add");
    }

    protected async Task SubmitContactAsync()
    {
        if (isAdding)
        {
            await AddNewContactAsync();
        }
        else
        {
            await UpdateContactAsync();
        }
    }

    protected async Task AddNewContactAsync()
    {
        try
        {
            var createContact = new CreateContactDto()
            {
                Name = currentContact.Name?.Trim(),
                Address = currentContact.Address?.Trim(),
                Phone = currentContact.Phone?.Trim(),
                Tax = currentContact.Tax?.Trim(),
                Email = currentContact.Email?.Trim(),
                Tags = currentContact.Tags?.Trim(),
                Type = currentContact.Type,
                VendorNumber = VendorNumber,
            };
            var resp = await apiClient.CreateContact(createContact);
            if (resp.StatusCode == StatusCodes.Status201Created)
            {
                viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation Successfully"]);
                OnNavigateToListVendor();
            }
            else viewNotifier.Show(resp.Result is not null ? resp.Result.ToString() : resp.Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    protected async Task UpdateContactAsync()
    {
        try
        {
            var updateContact = new UpdateContactDto()
            {
                Number = currentContact.Number,
                Name = currentContact.Name?.Trim(),
                Address = currentContact.Address?.Trim(),
                Phone = currentContact.Phone?.Trim(),
                Tax = currentContact.Tax?.Trim(),
                Email = currentContact.Email?.Trim(),
                Tags = currentContact.Tags?.Trim(),
                HasAccount = currentContact.HasAccount,
                Type = currentContact.Type,
                VendorNumber = VendorNumber
            };
            var resp = await apiClient.UpdateContact(updateContact);
            if (resp.StatusCode == StatusCodes.Status200OK)
            {
                viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation Successfully"]);
            }
            else viewNotifier.Show(resp.Result.ToString(), ViewNotifierType.Error, L["Operation Failed"]);
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    protected void OnResetForm()
    {
        currentContact = new();
    }
    protected void OnNavigateToListVendor()
    {
        NavigationManager.NavigateTo($"/vendors/{VendorNumber}/contacts");
    }

    protected void GetTypeOfContact()
    {
        listTypeOfContact = typeof(ContactEnum).ToSelectItemList();
    }

}
