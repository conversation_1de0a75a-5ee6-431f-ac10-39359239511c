@page "/vendor/item/{vendorNumber}/{itemNumber}/detail"
@attribute [Authorize(Policy = Policies.IsVendor)]
@inherits EditVendorItemBase
@using System.ComponentModel.DataAnnotations
@if (currentVendorItemDto is null)
{
    <UserNotAuthorized />
}
else
{
    <MudPaper Elevation="0">
        <MudOverlay ZIndex="99999" Visible="isLoading" LightBackground Absolute>
            <MudProgressCircular Color="Color.Primary" Indeterminate />
        </MudOverlay>
        <EditForm Model="@currentVendorItemDto" OnValidSubmit="OnUpdateVendorItemSubmit">
            <DataAnnotationsValidator />
            <MudCard>
                <MudCardHeader>
                    <CardHeaderContent>
                        <MudToolBar Gutters="false" Class="" Dense="true">
                            <MudIconButton Icon="@Icons.Material.Filled.ArrowBack"
                                OnClick="@(_ => navigation.NavigateTo("/po/vendor/items"))" />
                            <MudText Inline Typo="Typo.h5" Align="Align.Start">@L["Item detail"]</MudText>
                            </MudToolBar> 
                    </CardHeaderContent>
                    <CardHeaderActions>
                        <MudStack Row>
                            <MudSwitch T="bool?" Value="@(currentVendorItemDto.Blocked == 0)"
                                ValueChanged="@(async value => await OnToggleVendorItemStatus(currentVendorItemDto.RowId))"
                                Label="@L["Blocked"]" Color="Color.Error" UncheckedColor="Color.Success" />
                            <MudButton ButtonType="ButtonType.Submit" Variant="Variant.Filled" Color="Color.Primary"
                                Class="ml-auto">
                                @L["Save"]
                            </MudButton>
                        </MudStack>
                    </CardHeaderActions>
                </MudCardHeader>
                <MudCardContent>
                    <MudTextField Label="Name" @bind-Value="currentVendorItemDto.Name"
                        For="@(() => currentVendorItemDto.Name)" />
                    <MudTextField ReadOnly Label="ItemNumber" @bind-Value="currentVendorItemDto.ItemNumber"
                        For="@(() => currentVendorItemDto.ItemNumber)" />
                    <MudNumericField Format="N2" Label="Price" @bind-Value="currentVendorItemDto.Price"
                        For="@(() => currentVendorItemDto.Price)" />
                    <MudNumericField Format="N2" Label="Vat" @bind-Value="currentVendorItemDto.Vat"
                        For="@(() => currentVendorItemDto.Vat)" />
                    <MudSelect T="string" @bind-Value="@currentVendorItemDto.UnitOfMeasure" Required
                        For="@(() => currentVendorItemDto.UnitOfMeasure)" Label="Unit Of Measure"
                        AnchorOrigin="Origin.BottomCenter" Clearable>
                        @foreach (var o in listUnitOfMeasureDto)
                        {
                            <MudSelectItem Value="@o.Code" />
                        }
                    </MudSelect>
                </MudCardContent>
            </MudCard>
        </EditForm>
    </MudPaper>
}
@code {
    @* RegisterAccountForm model = new RegisterAccountForm(); *@
    bool success;
    private void OnValidSubmit(EditContext context)
    {
        success = true;
        StateHasChanged();
    }
}