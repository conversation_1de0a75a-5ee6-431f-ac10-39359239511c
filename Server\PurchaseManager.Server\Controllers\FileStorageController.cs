using Breeze.AspNetCore;
using Microsoft.AspNetCore.Mvc;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.FileStorage;
using PurchaseManager.Shared.Models.FileStorage;
namespace PurchaseManager.Server.Controllers;

[Route("api/data/[action]")]
public class FileStorageController : Controller
{
    private readonly IFileStorageManager _fileStorageManager;

    public FileStorageController(IFileStorageManager fileStorageService)
    {
        _fileStorageManager = fileStorageService;
    }

    [HttpPost]
    [RequestSizeLimit(209715200)]
    [RequestFormLimits(MultipartBodyLengthLimit = 209715200)]
    public async Task<ApiResponse> Upload([FromForm] List<IFormFile> file, [FromForm] string documentNo, [FromForm] string prefix)
    {
        if (file == null || file.Count == 0)
        {
            return new ApiResponse(400, "No file uploaded");
        }
        if (documentNo is null || prefix is null)
        {
            return new ApiResponse(400, "DocumentNo or Prefix is invalid");
        }
        var result = await _fileStorageManager.SaveFilesAsync(file, documentNo, prefix);
        return result;
    }

    [HttpGet]
    [BreezeQueryFilter]
    public IQueryable<DetailFileDto> GetFiles([FromQuery] FileStorageFilter filter)
    {
        var queryString = HttpContext.Request.QueryString.ToString();
        var result = _fileStorageManager.GetFilesAsync(filter, queryString);
        return result;
    }
    [HttpDelete]
    public async Task<IActionResult> DeleteFile(string fileId)
    {
        var result = await _fileStorageManager.DeleteFileAsync(fileId);
        return Ok(result);
    }
}
