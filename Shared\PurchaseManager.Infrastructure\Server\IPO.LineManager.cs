﻿using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Infrastructure.Server;

public interface IPOLineManager
{
    bool EXDateValid(DateOnly exDate);
    Task<ApiResponse> GetLinesAsync(string code);
    Task<ApiResponse> GetLineAsync(string documentNumber, int lineNumber);
    /// <summary>
    /// Check PO Line Duplicate
    /// </summary>
    /// <param name="requestDto"></param>
    /// <returns></returns>
    Task<ApiResponse> DuplicateValid(POLineAddOrUpdate requestDto);
    Task<ApiResponse> CreateLineAsync(POLineAddOrUpdate requestDto);
    Task<ApiResponse> CreateMultipleLinesAsync(List<POLineAddOrUpdate> requestDtos);
    Task<ApiResponse> UpdateLineAsync(POLineAddOrUpdate requestDto);
    /// <summary>
    /// Update quantity in po
    /// </summary>
    /// <param name="updatePoDto"></param>
    /// <returns></returns>
    Task<ApiResponse> UpdatePurchaseOrder(UpdatePoDto updatePoDto);
    Task<ApiResponse> UpdatePurchaseOrderAfterCreatePO(UpdatePoDto updatePoDto);
    Task<ApiResponse> DeleteLine(string documentNumber, string itemNumber, int rowId);
}
