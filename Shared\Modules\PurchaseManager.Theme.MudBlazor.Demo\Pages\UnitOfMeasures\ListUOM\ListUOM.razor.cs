﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Models.Item;
using PurchaseManager.Theme.Material.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Pages.UnitOfMeasures.ListUOM;
public partial class UnitOfMeasurePage : ItemsTableBase<UnitOfMeasureDto>
{
    public UnitOfMeasureDto item = new UnitOfMeasureDto();
    public bool isLoading { get; set; } = true;
    protected MudTextField<string> SearchRef { get; set; }
    protected UnitFilter unitFilter = new UnitFilter();
    [Inject] public NavigationManager navigation { get; set; }
    protected override void OnInitialized()
    {
        from = "GetAllUnitOfMeasure";
        queryParameters = unitFilter;
        base.OnInitialized();
        isLoading = false;
    }
    protected override async Task OnSearch(string text)
    {
        unitFilter.Query = text;
        apiClient.ClearEntitiesCache();
        await Reload();
    }
}
