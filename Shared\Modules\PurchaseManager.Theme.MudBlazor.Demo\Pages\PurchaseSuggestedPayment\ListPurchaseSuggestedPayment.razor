﻿@page "/po/purchase-suggest-payment"
@inherits ListPSPBase
@if (isLoading)
{
    <MudProgressLinear Indeterminate="@isLoading" />
}
else
{
    <MudCard Class="" Elevation="0" Outlined="true">
        <MudCardHeader>
            <CardHeaderContent>
                <MudStack Row Spacing="6">
                    <div>
                        <MudText Typo="Typo.h5">@L["Purchase Suggested Payment"] </MudText>
                        <MudText Typo="Typo.caption">List</MudText>
                    </div>
                </MudStack>
            </CardHeaderContent>
            <CardHeaderActions>
                <MudIconButton Icon="@Icons.Material.Filled.Refresh" OnClick="@(() => Reload())" Size="Size.Medium" Class="ma-2" />
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent>
            <MudTable ServerData="@(new Func<TableState, CancellationToken, Task<TableData<PurchaseManager.Shared.Dto.PurchaseSuggestedPayment.DetailPurchaseSuggestedPaymentHeaderDto>>>(ServerReload))" Striped="false" Bordered="false"
                      Dense="true" Hover="true" Elevation="0" Outlined="true" FixedHeader="true"
                      LoadingProgressColor="Color.Primary" @ref="table">
                <HeaderContent>
                    <MudTh>
                        <MudTableSortLabel T="PurchaseOrderHeader">PSP No</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel T="PurchaseOrderHeader">Status</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel T="PurchaseOrderHeader">Order Day</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel T="PurchaseOrderHeader">Expected Receipt Date</MudTableSortLabel>
                    </MudTh>
                    <MudTh>
                        <MudTableSortLabel T="PurchaseOrderHeader">Due Date</MudTableSortLabel>
                    </MudTh>
                </HeaderContent>
                <RowTemplate Context="row">
                    <MudTd DataLabel="PO/No">
                        <MudLink Href="@($"/po/purchase-suggest-payment/{row.Number}/detail")"> @row.Number </MudLink>
                    </MudTd>
                    <MudTd DataLabel="Status">
                        <MudChipSet T="int" @bind-SelectedValue="@row.Status">
                            <MudChip T="Wrap" Text="@(DisplayStatus(row.Status))" Color="@(row.Status == 3 ? Color.Info : row.Status == 4 ? Color.Warning : row.Status == 5 ? Color.Success : Color.Tertiary)" Variant="Variant.Text" />
                        </MudChipSet>
                    </MudTd>
                    <MudTd DataLabel="Order Date">
                        @PurchaseManager.Theme.Material.Shared.Utils.DateTimeFormat.GetDateString(row.OrderDate)</MudTd>
                    <MudTd DataLabel="Expected Receipt Date">
                        @PurchaseManager.Theme.Material.Shared.Utils.DateTimeFormat.GetDateString(@row.ExpectedReceiptDate)
                    </MudTd>
                    <MudTd DataLabel="Due Date">
                        @PurchaseManager.Theme.Material.Shared.Utils.DateTimeFormat.GetDateString(@row.DueDate)
                    </MudTd>
                </RowTemplate>
                <PagerContent>
                    <MudTablePager RowsPerPageString=@L["Rows per page"] />
                </PagerContent>
                <NoRecordsContent>
                    No Data
                </NoRecordsContent>
            </MudTable>
        </MudCardContent>
    </MudCard>
}
