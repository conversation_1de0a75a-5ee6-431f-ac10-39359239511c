using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Report;
namespace PurchaseManager.Theme.Material.Demo.Pages.Report;
public partial class ReportIndexBase : ComponentBase
{
    protected List<ReportSelectItem> reportSelectItems { get; set; }

    [Inject] public IStringLocalizer<Global> L { get; set; }
    protected ReportSelectItem selectedModule { get; set; }
    protected override void OnInitialized()
    {
        reportSelectItems = new List<ReportSelectItem>(){
            new ReportSelectItem(){
                Key = "PO",
                Value ="Purchase Order"
            },
            new ReportSelectItem(){
                Key = "VD",
                Value ="Vendor"
            },
            new ReportSelectItem(){
                Key = "IT",
                Value ="Item"
            },
        };
        base.OnInitialized();
    }
}
