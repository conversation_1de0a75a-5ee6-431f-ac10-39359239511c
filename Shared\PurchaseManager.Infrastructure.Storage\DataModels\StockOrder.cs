namespace PurchaseManager.Infrastructure.Storage.DataModels;

public class StockOrder
{
    public string Number { get; set; }
    public string HeaderNumber { get; set; }
    public int LineNumber { get; set; }
    public int DocumentType { get; set; }
    public string ItemNumber { get; set; }
    public string LotNo { get; set; }
    public DateTime ExpirationDate { get; set; }
    public int TotalQuantity { get; set; }
    public int QuantityReceived { get; set; }
    public string ItemName { get; set; }
    public string Note { get; set; }
    public DateTime CreateAt { get; set; }
    public string CreateBy { get; set; }
    public int Status { get; set; } = 1;// 1: Draft, 2: Saved, 3: Completed (đã tạo phiếu nhập)
    public int RowId { get; set; }
    public bool IsCreateSO { get; set; }
    public PurchaseOrderHeader HeaderNumberNavigation { get; set; }
    public PurchaseOrderLine PurchaseOrderLine { get; set; }
}
