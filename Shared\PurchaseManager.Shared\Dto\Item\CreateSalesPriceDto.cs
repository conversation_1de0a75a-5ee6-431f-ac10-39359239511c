﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace PurchaseManager.Shared.Dto.Item;

public class CreateSalesPriceDto
{
    [StringLength(100)]
    public string? ItemNumber { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime StartingDate { get; set; }

    [StringLength(50)]
    public string UnitOfMeasureCode { get; set; } = null!;

    [Column(TypeName = "decimal(28, 10)")]
    public decimal UnitPrice { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime EndingDate { get; set; }

    [StringLength(50)] public string? SourceCode { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal? Quantity { get; set; }

    [Column(TypeName = "decimal(28, 10)")] 
    public decimal QuantityPerUnitOfMeasure { get; set; }

    [StringLength(250)]
    public string? Description { get; set; }
    public int Block { get; set; }
}
