﻿@using PurchaseManager.Shared.Dto.PO
@using PurchaseManager.Theme.Material.Demo.Shared.Components.PO.Services
@using PurchaseManager.Theme.Material.Shared.Utils
@using static PurchaseManager.Shared.Helpers.POHandlingShare;
<MudTable ServerData="@(new Func<TableState, CancellationToken, Task<TableData<POHeaderGetDto>>>(ServerReload))"
          Striped="false" Bordered="false" T="POHeaderGetDto" SelectOnRowClick="false" Dense Hover Elevation="0" Outlined
          FixedHeader Height="650px" HorizontalScrollbar="false" MultiSelection="IsPurchaseUser || IsAdmin || IsMKT"
    LoadingProgressColor="Color.Primary" @ref="Table">
    <ToolBarContent>
        <MudStack Row Justify="Justify.SpaceBetween" Style="width: 100%;">
            @if (Table.SelectedItems is { Count: > 0 } && (IsPurchaseUser || IsMKT))
            {
                <MudBadge Content="@Table.SelectedItems.Count" Color="Color.Error" Overlap>
                    <MudIconButton Size="Size.Small" Icon="@Icons.Material.Filled.Delete"
                        OnClick="@ShowConfirmDeleteDialogAsync" Variant="Variant.Outlined" Color="Color.Error">
                    </MudIconButton>
                </MudBadge>
            }
            else
            {
                <MudSpacer />
            }
            @* <MudButtonGroup Size="Size.Small" Color="Color.Primary" Variant="Variant.Outlined">
                <MudButton Disabled>Export</MudButton>
                <MudMenu Icon="@Icons.Material.Filled.ArrowDropDown" Style="align-self: auto;">
                    <MudMenuItem>Excel</MudMenuItem>
                    <MudMenuItem>CSV</MudMenuItem>
                    <MudMenuItem>...</MudMenuItem>
                </MudMenu>
            </MudButtonGroup> *@
            @if (IsEdited)
            {
                <MudButton Color="Color.Primary" OnClick="OnSaveChanged" Size="Size.Small" Variant="Variant.Filled">Save</MudButton>
                <MudButton Class="mx-4" OnClick="OnDiscardChanges" Color="Color.Error" Size="Size.Small" Variant="Variant.Filled">
                    Discard
                </MudButton>
            }
        </MudStack>
    </ToolBarContent>
    <HeaderContent>
        <MudTh>
            <MudTableSortLabel T="PurchaseOrderHeader">PO/No</MudTableSortLabel>
        </MudTh>
        <MudTd></MudTd>
        @if (!IsVendor && !IsVendorContact || IsPurchaseUser || IsAdmin || IsWarehouseUser || IsMKT)
        {
            <MudTh>
                <MudTableSortLabel T="PurchaseOrderHeader">Vendor</MudTableSortLabel>
            </MudTh>
        }
        <MudTh>
            <MudTableSortLabel T="PurchaseOrderHeader">Description</MudTableSortLabel>
        </MudTh>
        <MudTh>
            <MudTableSortLabel T="PurchaseOrderHeader">Amount</MudTableSortLabel>
        </MudTh>
        @* <MudTh>
            <MudTableSortLabel T="PurchaseOrderHeader">Type</MudTableSortLabel>
        </MudTh> *@
        <MudTh>
            <MudTableSortLabel T="PurchaseOrderHeader">Approval</MudTableSortLabel>
        </MudTh>
        <MudTh>
            <MudTableSortLabel T="PurchaseOrderHeader">Received/Ordered</MudTableSortLabel>
        </MudTh>
        <MudTh>
            <MudTableSortLabel T="PurchaseOrderHeader">Order Date</MudTableSortLabel>
        </MudTh>
        <MudTh>
            <MudTableSortLabel T="PurchaseOrderHeader">Expected Date</MudTableSortLabel>
        </MudTh>
        @if (IsPurchaseUser)
        {
            <MudTh>
                <MudTableSortLabel T="PurchaseOrderHeader">Modified Date</MudTableSortLabel>
            </MudTh>
        }
    </HeaderContent>
    <RowTemplate Context="row">
        <MudTd DataLabel="No">
            <MudStack Row AlignItems="AlignItems.Center">
                <MudStack>
                    <MudText>
                        <MudLink Href="@NavigateToDetails(row)">
                            @row.Number
                        </MudLink>
                    </MudText>
                </MudStack>
            </MudStack>
        </MudTd>
        <MudTd DataLabel="Status">
            <MudStack Spacing="0" Row AlignItems="AlignItems.Center">
                <MudTooltip Text="@POComponentExtension.GetDocNoOccurrenceText(row.DocNoOccurrence)" Color="Color.Success" Placement="Placement.Top">
                    <div>
                        @switch (row.DocNoOccurrence)
                        {
                            case (Int32)DocNoOccurrenceEnum.Order:
                                <MudIconButton Color="Color.Primary" Icon="@Icons.Material.Filled.LocalGroceryStore"/>
                                break;
                            case (Int32)DocNoOccurrenceEnum.Consigned:
                                <MudIconButton Color="Color.Warning" Icon="@Icons.Material.Filled.Draw"/>
                                break;
                            default:
                                <MudIconButton Color="Color.Error" Icon="@Icons.Material.Filled.CardGiftcard"/>
                                break;
                        }
                    </div>
                </MudTooltip>
                <MudTooltip Text="@(POComponentExtension.GetStatusText(row.Status))" Color="Color.Success" Placement="Placement.Top">
                    <MudChipSet T="Int32" @bind-SelectedValue="@row.Status">
                        <MudChip T="Wrap" Text="@(POComponentExtension.DisplayStatus(row.Status))" Color="@POComponentExtension.GetColorByPOStatus(row.Status)"
                                 Variant="Variant.Text"/>
                    </MudChipSet>
                </MudTooltip>
            </MudStack>
        </MudTd>
        @if (!IsVendor && !IsVendorContact || IsPurchaseUser || IsAdmin || IsMKT)
        {
            <MudTd DataLabel="Vendor" >
                <div>
                    <MudText Style="min-width: 200px; overflow: hidden;">
                        @row.BuyFromVendorName
                        @if (!string.IsNullOrEmpty(row.BuyFromContact)
                             && string.Equals(RemoveSpecialChar(row.BuyFromVendorNumber), row.BuyFromContact, StringComparison.CurrentCultureIgnoreCase))
                        {
                            <span> -</span>
                            <MudChip T="Wrap" Size="Size.Small" Color="Color.Success" Variant="Variant.Text"> @row.BuyFromContact </MudChip>
                        }
                    </MudText>
                    <b>Number:</b>
                    <MudLink Href="@NavigateToVendorDetails(row.BuyFromVendorNumber)">
                        <MudText Typo="Typo.caption">
                            @row.BuyFromVendorNumber
                        </MudText>
                    </MudLink>
                </div>
            </MudTd>
        }
        <MudTd DataLabel="No">
            <MudText Inline>
                @row.PostingDescription
            </MudText>
        </MudTd>
        <MudTd DataLabel="Total Amount"> @row.TotalAmount.ToString("N0")</MudTd>
        <MudTd DataLabel="Approval">
            <div>
                @{
                    var isPurchaserApproved = !string.IsNullOrEmpty(row.PurchaserApprovalBy);
                    var isVendorApproved = !string.IsNullOrEmpty(row.VendorApprovalBy);
                }
                <MudTooltip Text="@L["Approve By TS"]" Placement="Placement.Top">
                    <MudToggleIconButton @bind-Toggled="@isPurchaserApproved" Color="@Color.Default" Size="Size.Small"
                        ToggledSize="Size.Small" ToggledColor="@Color.Success" Icon="@Icons.Material.Filled.AssignmentInd"
                        ToggledIcon="@Icons.Material.Filled.AssignmentInd" />
                </MudTooltip>
                <MudTooltip Text="@L["Approve By Vendor"]" Placement="Placement.Top">
                    <MudToggleIconButton @bind-Toggled="@isVendorApproved" Color="@Color.Default" Size="Size.Small"
                        ToggledSize="Size.Small" ToggledColor="@Color.Success" Icon="@Icons.Material.Filled.Groups3"
                        ToggledIcon="@Icons.Material.Filled.Groups3" />
                </MudTooltip>
            </div>
        </MudTd>
        <MudTd DataLabel="Received/Order">
            <MudText>@((Int32)row.TotalQuantityReceived)/@((Int32)row.TotalQuantity)</MudText>
        </MudTd>
        <MudTd DataLabel="Order Date">
            @row.OrderDate.ToString("dd/MM/yyyy")
        </MudTd>
        <MudTd DataLabel="Expected Date">
            @if (IsWarehouseUser)
            {
                <MudDatePicker
                    AdornmentColor="@(ListDueDateOfPurchaseOrdersB4Edit.Find(x => x.Number == row.Number) is not null ? Color.Success : Color.Surface)"
                    Date="@((DateTime?)row.DueDate)" MinDate="DateTime.Today" ReadOnly="@(IsPurchaseUser || IsUpdating)"
                    DateChanged="@(date => OnDueDateChange(date, row.Number))" DateFormat="dd/MM/yyyy" />
            }
            else
            {
                @row.DueDate.ToString("dd/MM/yyyy")
            }
        </MudTd>
        @if (IsPurchaseUser)
        {
            <MudTd DataLabel="Modified date">
                @if (row.LastModifiedTime != null)
                {
                    @row.LastModifiedTime.Value.GetDateString()
                }
            </MudTd>
        }
    </RowTemplate>
    <PagerContent>
        <MudStack AlignItems="AlignItems.End">
            <MudPagination SelectedChanged="PageChanged"
                Count="@((Table.GetFilteredItemsCount() + Table.RowsPerPage - 1) / Table.RowsPerPage)" class="pa-2">
            </MudPagination>
        </MudStack>
    </PagerContent>
    <NoRecordsContent>
        No Data
    </NoRecordsContent>
</MudTable>
