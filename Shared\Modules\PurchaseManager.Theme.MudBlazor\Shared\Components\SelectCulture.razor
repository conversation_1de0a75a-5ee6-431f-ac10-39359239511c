﻿@inject NavigationManager navigationManager
@inject IJSRuntime js
@inject AppState appState

<MudMenu AnchorOrigin="Origin.BottomRight" Class="mx-3" Size="Size.Small" LockScroll="true"
    StartIcon="@Icons.Material.Outlined.Translate" Label="@CurrentCulture" TransformOrigin="Origin.TopRight">
    <ChildContent>
        @foreach (var item in Settings.SupportedCulturesWithName)
        {
            <MudMenuItem OnClick="@(() => OnCultureChanged(item.Item2))">@item.Item1</MudMenuItem>
        }
    </ChildContent>
</MudMenu>

@code {
    [CascadingParameter]
    private Task<AuthenticationState> authenticationStateTask { get; set; }

    private string CurrentCulture { get; set; }

    protected override async Task OnInitializedAsync()
    {
        CurrentCulture = CultureInfo.CurrentCulture.Name;

        await base.OnInitializedAsync();
    }

    public async void OnCultureChanged(string culture)
    {
        var uri = new Uri(navigationManager.Uri).GetComponents(UriComponents.PathAndQuery, UriFormat.Unescaped);

        if ((await authenticationStateTask).User.Identity.IsAuthenticated)
        {
            var profile = await appState.GetUserProfile();

            profile.Culture = culture;

            await appState.UpdateUserProfile();
        }

        navigationManager.NavigateTo($"/Culture/SetCulture?culture={Uri.EscapeDataString(culture)}&redirectUri={Uri.EscapeDataString(uri)}",
        forceLoad: true);
    }
}