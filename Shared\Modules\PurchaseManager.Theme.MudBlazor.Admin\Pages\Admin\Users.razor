﻿@inherits UsersPage
@page "/admin/users"
@attribute [Authorize(Policies.IsAdmin)]
@layout AdminLayout

<PageTitle>@L["Users"]</PageTitle>

@if (users == null)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    <MudTable FixedFooter FixedHeader Height="760px" ServerData="@(new Func<TableState, CancellationToken, Task<TableData<ApplicationUser>>>(ServerReload))" Striped="true" Bordered="true" Dense="true" Hover="true" Elevation="2" @ref="table">
        <ToolBarContent>
            <MudText Typo="Typo.h6">Users</MudText>
            <MudSpacer />
            <MudTextField T="string" Value="@userNameToSearch" Clearable Immediate  ValueChanged="@(async (value) => await OnSearchUser(value))" Placeholder="Search" Adornment="Adornment.Start" AdornmentIcon="@Icons.Material.Filled.Search" IconSize="Size.Medium" Class="mt-0"></MudTextField>
        </ToolBarContent>
        <HeaderContent>
            <MudTh><MudButton StartIcon="@Icons.Material.Filled.PersonAdd" OnClick="@(e => { createUserDialogOpen = true; })" Variant="Variant.Filled" Color="Color.Primary">@L["New User"]</MudButton></MudTh>
            <MudTh>@L["UserName"]</MudTh>
            <MudTh>@L["FullName"]</MudTh>
            <MudTh>@L["Email"]</MudTh>
            <MudTh>@L["Roles"]</MudTh>
        </HeaderContent>
        <RowTemplate Context="UserRow">
            <MudTd>
                <MudIconButton Icon="@Icons.Material.Filled.Edit" OnClick="@(() => OpenEditDialog(UserRow))"></MudIconButton>
                <MudIconButton Icon="@Icons.Material.Filled.RotateRight" OnClick="@(() => OpenResetPasswordDialog(UserRow))" Disabled="@(UserRow.UserName == DefaultUserNames.Administrator)"></MudIconButton>
                <MudTooltip Placement="Placement.Top" Text="@($"User {(UserRow.Block ? "was blocked" : "was active")}, click to {(UserRow.Block ? "active" : "block")}")">
                    <MudIconButton Color="@(UserRow.Block ? Color.Error : Color.Success)" Icon="@(UserRow.Block ? Icons.Material.Filled.Delete : Icons.Material.Filled.VerifiedUser)" OnClick="@(() => OpenDeleteDialog(UserRow))" Disabled="@(UserRow.UserName == DefaultUserNames.Administrator)"></MudIconButton>
                </MudTooltip>
                @if (!UserRow.EmailConfirmed)
                {
                    <MudTooltip Placement="Placement.Top" Text="@($"Verify email {UserRow.Email  } ")">
                        <MudIconButton Color="Color.Primary" OnClick="@(() => VerifyEmail(UserRow))" Icon="@Icons.Material.Filled.CheckCircle"></MudIconButton>
                    </MudTooltip>
                }
            </MudTd>
            <MudTd><div style="width:130px;">@UserRow.UserName</div></MudTd>
            <MudTd>@UserRow.FirstName @UserRow.LastName</MudTd>
            <MudTd style="white-space: nowrap">
                @UserRow.Email
                @if (UserRow.EmailConfirmed)
                {
                    <MudIcon Style="vertical-align: text-bottom" Color="Color.Success" Icon="@Icons.Material.Filled.CheckCircle"></MudIcon>
                }
            </MudTd>
            <MudTd>
                @if (@UserRow.UserRoles != null)
                {<MudChipSet T="string">
                        @foreach (var ur in @UserRow.UserRoles)
                        {
                            <MudChip T="string" Text="@ur.Role.Name"></MudChip>
                        }
                    </MudChipSet>}
            </MudTd>
        </RowTemplate>
        <PagerContent>
            <MudTablePager RowsPerPageString=@L["Rows per page"] />
        </PagerContent>
    </MudTable>
}

<MudDialog @bind-Visible="@createUserDialogOpen">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Add" Class="mr-3 mb-n1" />
            @L["New User"]
        </MudText>
    </TitleContent>
    <DialogContent>
        <EditForm id="createUserForm" Model="@newUserViewModel" OnValidSubmit="@CreateUserAsync">
            <FluentValidationValidator />
            <MudValidationSummary />
            <MudTextField @bind-Value="@newUserViewModel.UserName" Label=@L["UserName"] AdornmentIcon="@Icons.Material.Filled.Person" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>

            <MudTextField @bind-Value="@newUserViewModel.Email" Label=@L["Email"] AdornmentIcon="@Icons.Material.Outlined.Email" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"] Type="mail"></MudTextField>

            <MudTextField @bind-Value="@newUserViewModel.Password" Label=@L["Password"] AdornmentIcon="@Icons.Material.Outlined.Lock" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"] InputType="InputType.Password"></MudTextField>

            <MudTextField @bind-Value="@newUserViewModel.PasswordConfirm" Label=@L["Password Confirmation"] AdornmentIcon="@Icons.Material.Outlined.Lock" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"] InputType="InputType.Password"></MudTextField>
        </EditForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(e => { createUserDialogOpen = false; })">@L["Cancel"]</MudButton>
        <MudButton ButtonType="ButtonType.Submit" form="createUserForm" Disabled="@disableCreateUserButton" Variant="Variant.Filled" Color="Color.Primary">@L["Create"]</MudButton>
    </DialogActions>
</MudDialog>

<MudDialog @bind-Visible="@editDialogOpen">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-3 mb-n1" />
            @L["Edit {0}", currentUser.UserName]
        </MudText>
    </TitleContent>
    <DialogContent>
        <EditForm id="updateUserForm" Model="@currentUser" OnValidSubmit="@UpdateUserAsync">
            <FluentValidationValidator />
            <MudValidationSummary />
            <MudTextField @bind-Value="@currentUser.UserName" Label=@L["UserName"] AdornmentIcon="@Icons.Material.Filled.Person" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>

            <MudTextField @bind-Value="@currentUser.Email" Label=@L["Email"] AdornmentIcon="@Icons.Material.Outlined.Email" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"] Type="mail"></MudTextField>

            <MudChipSet T="string">
                @foreach (var role in roleSelections.OrderBy(x => x.DisplayValue))
                {
                    <MudChip T="string" Text="@role.DisplayValue" Icon="@((role.Selected) ? Icons.Material.Filled.Done : "")" @onclick="@(()=>UpdateUserRole(role))" />
                }
            </MudChipSet>
        </EditForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@CancelChanges">@L["Cancel"]</MudButton>
        <MudButton ButtonType="ButtonType.Submit" form="updateUserForm" Disabled="@disableUpdateUserButton" Variant="Variant.Filled" Color="Color.Primary">@L["Update"]</MudButton>
    </DialogActions>
</MudDialog>

<MudDialog @bind-Visible="@deleteUserDialogOpen" Style="z-index:100">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@(currentUser.Block ? Icons.Material.Filled.VerifiedUser : Icons.Material.Filled.DeleteForever)" Class="mr-3 mb-n1" />
            @L["Confirm {0}", (currentUser.Block ? "active" : "block")]
        </MudText>
    </TitleContent>
    <DialogContent>
        @L["Are you sure you want to {0} {1}?",(currentUser.Block ? "active" : "block"), currentUser.UserName]
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(e => { deleteUserDialogOpen = false; })">@L["Cancel"]</MudButton>
        <MudButton OnClick="@DeleteUserAsync" Variant="Variant.Filled" Color="@(currentUser.Block ? Color.Success : Color.Error)">@(currentUser.Block ? "Active" : "Block")</MudButton>
    </DialogActions>
</MudDialog>

<MudDialog @bind-Visible="@changePasswordDialogOpen">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.Edit" Class="mr-3 mb-n1" />
            @L["Change password for {0}", currentUser.UserName]
        </MudText>
    </TitleContent>
    <DialogContent>
        <EditForm id="changePasswordForm" Model="@changePasswordViewModel" OnValidSubmit="@ResetUserPasswordAsync">
            <FluentValidationValidator />
            <MudValidationSummary />
            <MudTextField @bind-Value="@changePasswordViewModel.Password" Label=@L["Password"] AdornmentIcon="@Icons.Material.Outlined.Lock" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"] InputType="InputType.Password"></MudTextField>

            <MudTextField @bind-Value="@changePasswordViewModel.PasswordConfirm" Label=@L["Password Confirmation"] AdornmentIcon="@Icons.Material.Outlined.Lock" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"] InputType="InputType.Password"></MudTextField>
        </EditForm>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(e => { changePasswordDialogOpen = false; })">@L["Cancel"]</MudButton>
        <MudButton ButtonType="ButtonType.Submit" form="changePasswordForm" Disabled="@disableChangePasswordButton" Variant="Variant.Filled" Color="Color.Primary">@L["Reset Password"]</MudButton>
    </DialogActions>
</MudDialog>

@code {
    private MudTable<ApplicationUser> table;

    private async Task<TableData<ApplicationUser>> ServerReload(TableState state, CancellationToken token)
    {
        await OnPage(state.Page, state.PageSize);

        return new TableData<ApplicationUser>() { TotalItems = totalItemsCount, Items = users };
    }

    protected override async Task Reload()
    {
        await table.ReloadServerData();
    }
}
