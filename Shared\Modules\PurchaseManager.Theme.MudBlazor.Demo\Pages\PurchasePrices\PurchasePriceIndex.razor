﻿@page "/purchase-price"
@page "/purchase-price/{vendorNumberParam}/{vendorNameParam}"
@using PurchaseManager.Shared.Dto.Item
@using PurchaseManager.Shared.Dto.PurchasePrices

<MudToolBar>
    <MudText Typo="Typo.h6">@L["ListUnitToConvert"]</MudText>
    <MudSpacer />
    <MudAutocomplete T="GetVendorDto" ShrinkLabel ListItemClass="m-0" ResetValueOnEmptyText Label="@L["Vendor"]"
        ShowProgressIndicator Value="@SelectedVendor"
        ValueChanged="@(async value => { await OnSearchByVendorChanged(value); })" Placeholder="PleaseChooseVendor"
        ToStringFunc="@(dto => dto is null || string.IsNullOrEmpty(dto.Number) ? null : string.Concat(dto.Number, ' ', '-', ' ', dto.Name))"
        SearchFunc="@SearchFuncVendorByNumber" Margin="Margin.Dense">
        <ProgressIndicatorInPopoverTemplate>
            <MudList T="String" ReadOnly>
                <MudListItem>
                    Loading...
                </MudListItem>
            </MudList>
        </ProgressIndicatorInPopoverTemplate>
        <ItemTemplate Context="e">
            <MudText>@e.Name</MudText>
            <MudText Typo="Typo.body2">@e.Number</MudText>
        </ItemTemplate>
    </MudAutocomplete>
    @if (SelectedVendor is not null && !string.IsNullOrEmpty(SelectedVendor.Name))
    {
        <div class="">
            <MudIconButton Class="mt-3" OnClick="@(async _ => await OnClearFilterByVendor())"
                Icon="@Icons.Material.Filled.Clear" />
        </div>
    }
    <MudSpacer />
    <MudStack Style="" Row>
        @if (ListPurchaseUnitSelected.Count > 0)
        {
            <MudButton OnClick="@OnDeleteAllHeaderSelectedAsync" Color="Color.Error" Variant="Variant.Text"
                Size="Size.Small">
                @L["Delete"]
            </MudButton>
        }
        @if (!string.IsNullOrEmpty(SelectedVendor?.Number))
        {
            <MudButton OnClick="@ShowCreatePurchasePriceDialogChoseVendor" Color="Color.Success" Variant="Variant.Outlined"
                Size="Size.Small">@L["Add"]
            </MudButton>
        }
    </MudStack>
</MudToolBar>

<MudTable @ref="@TableHeaderRef" Loading="@IsLoading" T="GetPurchasePriceDto" TotalItems="@TotalItems" MultiSelection
    @bind-SelectedItems="@ListPurchaseUnitSelected"
    ServerData="@(new Func<TableState, CancellationToken, Task<TableData<GetPurchasePriceDto>>>(ServerReload))" Dense
    Hover>
    <HeaderContent>
        <MudTh>
            <MudIconButton OnClick="@ShowCreatePurchasePriceDialog" Icon="@Icons.Material.Filled.Add" Size="Size.Small"
                Variant="Variant.Outlined" Color="Color.Success">
            </MudIconButton>
        </MudTh>
        <MudTh>Number</MudTh>
        <MudTh>Vendor</MudTh>
        <MudTh>Item</MudTh>
        <MudTh>PurchasingUnit</MudTh>
        <MudTh>PriceBefVat</MudTh>
        <MudTh>PriceAftVat</MudTh>
        <MudTh>VAT</MudTh>
        <MudTh>ItemCategory</MudTh>
        <MudTh>PriceAftDiscountNoVat</MudTh>
        <MudTh>PriceAftDiscountVat</MudTh>
        <MudTh>Pic</MudTh>
        <MudTh>DiscountBySkus</MudTh>
        <MudTh>GroupProduct</MudTh>
        <MudTh>FoCPromotion</MudTh>
        <MudTh>Status</MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd>
            <MudIconButton OnClick="@(() => LoadPurchasePriceByNumberAsync(context.Number))"
                Icon="@Icons.Material.Filled.Edit" Variant="Variant.Outlined" Color="Color.Primary" Size="Size.Small" />
        </MudTd>
        <MudTd DataLabel="Number">@context.Number</MudTd>
        <MudTd DataLabel="@L["Vendor"]" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;">
            <MudText Typo="Typo.body1"></MudText> @context.VendorName
            <MudText Typo="Typo.subtitle2"> Number: @context.VendorNumber </MudText>
        </MudTd>
        <MudTd DataLabel="@L["Item"]" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;">
            <MudText Typo="Typo.body1"></MudText> @context.ItemName
            <MudText Typo="Typo.subtitle2"> Number: @context.ItemNumber </MudText>
        </MudTd>
        <MudTd DataLabel="PurchasingUnit" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;">
            @context.PurchasingUnit.ToUpper()
        </MudTd>
        <MudTd DataLabel="PriceBefVat" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;">
            @($"{context.PriceBefVat:N0}") VND
        </MudTd>
        <MudTd DataLabel="PriceAftVat" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;">
            @($"{context.PriceAftVat:N0}") VND
        </MudTd>
        <MudTd DataLabel="VAT" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;">
            @context.VAT%
        </MudTd>
        <MudTd DataLabel="ItemCategory" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;">
            @context.ItemCategory
        </MudTd>
        <MudTd DataLabel="PriceAftDiscountNoVat"
            Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;">
            @($"{context.PriceAftDiscountNoVat:N0}") VND
        </MudTd>
        <MudTd DataLabel="PriceAftDiscountVat" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;">
            @($"{context.PriceAftDiscountVat:N0}") VND
        </MudTd>
        <MudTd DataLabel="Pic" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;">
            @context.Pic
        </MudTd>
        <MudTd DataLabel="DiscountBySkus" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;">
            @context.DiscountBySkus%
        </MudTd>
        <MudTd DataLabel="GroupProduct" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;">
            @context.GroupProduct
        </MudTd>
        <MudTd DataLabel="FoCPromotion" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;">
            @context.FoCPromotion
        </MudTd>
        <MudTd DataLabel="Status">
            <MudChip T="String" Color="@(context.Status == 1 ? Color.Success : Color.Primary)">
                @GetStatusName(context.Status)</MudChip>
        </MudTd>
    </RowTemplate>
    <PagerContent>
        <MudTablePager />
    </PagerContent>
</MudTable>

<MudDialog @bind-Visible="@IsShowAddFormLine" TitleClass="mud-secondary" ContentStyle="height:700px" ContentClass="pa-6"
    Options="new DialogOptions { FullWidth = true, MaxWidth = MaxWidth.ExtraLarge, CloseButton = true }">
    <TitleContent>
        <MudText Typo="Typo.h6">Add New</MudText>
    </TitleContent>
    <DialogContent>
        <MudStack Row Justify="Justify.SpaceBetween" Class="my-3">
            <MudAutocomplete T="GetVendorDto" ShrinkLabel ListItemClass="m-0" ResetValueOnEmptyText
                Label="Chọn nhà cung cấp" ShowProgressIndicator Value="@VendorSelectedToAdd"
                ValueChanged="@(value => { OnSearchByVendor(value); })"
                ToStringFunc="@(dto => string.Concat(dto.Number, ' ', '-', ' ', dto.Name))"
                SearchFunc="@ItemSearchInMainView">
                <ProgressIndicatorInPopoverTemplate>
                    <MudList T="String" ReadOnly>
                        <MudListItem>
                            Loading...
                        </MudListItem>
                    </MudList>
                </ProgressIndicatorInPopoverTemplate>
                <ItemTemplate Context="e">
                    <MudText>@e.Name</MudText>
                    <MudText Typo="Typo.body2">@e.Number</MudText>
                </ItemTemplate>
            </MudAutocomplete>
            <MudStack Justify="Justify.FlexEnd">
                <div class="">
                    @if (ListPurchaseUnitSelectedWhenAdd.Any())
                    {
                        <MudButton OnClick="@OnRemoveNewItemsInList" StartIcon="@Icons.Material.Filled.Delete"
                            Color="Color.Error">
                            Delete All
                        </MudButton>
                    }
                    @if (!string.IsNullOrEmpty(VendorSelectedToAdd.Number))
                    {
                        <MudButton OnClick="@OnAddNewItemToList" Variant="Variant.Filled" Color="Color.Success">
                            New Item
                        </MudButton>
                    }
                </div>
            </MudStack>
        </MudStack>

        <MudForm @ref="addFormRef" @bind-IsValid="@IsAddFormValid">
            <MudTable MultiSelection SelectOnRowClick="false" @bind-SelectedItems="@ListPurchaseUnitSelectedWhenAdd"
                Items="@ListPurchasePriceDtoToCreate" Hover="true" Breakpoint="Breakpoint.Sm">
                <HeaderContent>
                    <MudTh>Item Number</MudTh>
                    <MudTh>Purchasing Unit</MudTh>
                    <MudTh>Price</MudTh>
                    <MudTh>% VAT</MudTh>
                    <MudTh>FoC Promotion</MudTh>
                    <MudTh>% Discount By Skus</MudTh>
                    <MudTh>Group Product</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd DataLabel="Item">
                        <MudAutocomplete T="DetailItemDto" ShrinkLabel="true" ShowProgressIndicator="true"
                            ValueChanged="@(dto => OnItemSelectedInAutoComplete(dto, context))"
                            ToStringFunc="dto => dto == null ? null : string.Concat(dto.Number, ' ', string.IsNullOrEmpty(dto.Number) ? ' ' : '-', ' ', dto.Name)"
                            Required="true" SearchFunc="@ItemSearch" Margin="Margin.Dense" Dense="true">
                            <ProgressIndicatorInPopoverTemplate>
                                <MudList T="String" ReadOnly>
                                    <MudListItem>
                                        Loading...
                                    </MudListItem>
                                </MudList>
                            </ProgressIndicatorInPopoverTemplate>
                            <ItemTemplate Context="e">
                                <MudStack Row="false" StretchItems="StretchItems.All">
                                    <MudStack Row Spacing="0" Justify="Justify.SpaceBetween">
                                        <MudStack Spacing="0">
                                            <MudText>@e.Number</MudText>
                                            <MudText Typo="Typo.caption">@e.Name</MudText>
                                        </MudStack>
                                        <MudChip T="String" Variant="Variant.Text"
                                            Color="@(e.Blocked == 1 ? Color.Warning : Color.Success)">
                                            @(e.Blocked == 1 ? "Blocked" : "Active")
                                        </MudChip>
                                    </MudStack>
                                </MudStack>
                            </ItemTemplate>
                        </MudAutocomplete>
                    </MudTd>
                    <MudTd DataLabel="PurchasingUnit">
                        <MudSelect Required Value="@context.PurchasingUnit" T="String"
                            ValueChanged="@(value => OnUnitSelected(value, context))">
                            @foreach (var item in context.ListUnitToChoose)
                            {
                                <MudSelectItem Value="@item">@item</MudSelectItem>
                            }
                        </MudSelect>
                    </MudTd>
                    <MudTd DataLabel="Price">
                        <MudNumericField Required RequiredError="Price is required!" Format="N2"
                            Culture="@CultureInfo.GetCultureInfo("vi-VN")" @bind-Value="@context.Price"
                            Variant="Variant.Text" />
                    </MudTd>
                    <MudTd DataLabel="VAT">
                        <MudNumericField @bind-Value="@context.VAT" Variant="Variant.Text" />
                    </MudTd>
                    <MudTd DataLabel="FoCPromotion">
                        <MudTextField @bind-Value="@context.FoCPromotion" Variant="Variant.Text" />
                    </MudTd>
                    <MudTd DataLabel="DiscountBySkus">
                        <MudNumericField Min="0" Max="100" Step="1" @bind-Value="@context.DiscountBySkus"
                            Variant="Variant.Text" />
                    </MudTd>
                    <MudTd DataLabel="GroupProduct">
                        <MudTextField @bind-Value="@context.GroupProduct" Variant="Variant.Text" />
                    </MudTd>
                </RowTemplate>
            </MudTable>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton Variant="Variant.Text" Color="Color.Error" DropShadow="false" Class="mx-3"
            OnClick="@OnCancelAddNewPurchaseUnit">
            Cancel
        </MudButton>
        <MudButton Variant="Variant.Filled" Color="Color.Primary" DropShadow="false"
            OnClick="@OnCreateNewPurchaseUnitAsync">
            Create
        </MudButton>
    </DialogActions>
</MudDialog>
<MudDrawer @bind-Open="EditDrawerOpen" Overlay="false" Anchor="Anchor.Right" Width="450px" Elevation="4"
    Variant="@DrawerVariant.Temporary" ClipMode="DrawerClipMode.Always">
    <MudDrawerHeader>
        <MudText Typo="Typo.h6">Edit Purchase Price</MudText>
    </MudDrawerHeader>
    <MudDrawerContainer>
        @if (ItemToEdit != null)
        {
            <div class="px-4">
                <MudForm @ref="EditForm">
                    <MudField Label="Item">@ItemToEdit.ItemNumber - @ItemToEdit.ItemName</MudField>
                    <MudField Label="Vendor">@ItemToEdit.VendorNumber - @ItemToEdit.VendorName</MudField>
                    <MudField Label="Purchase Unit">@ItemToEdit.PurchasingUnit</MudField>
                    <MudNumericField Label="Price" @bind-Value="ItemToEdit.PriceBefVat" Format="N2" />
                    <MudNumericField Label="VAT" @bind-Value="ItemToEdit.VAT" />
                    <MudNumericField Label="Discount by Skus" @bind-Value="ItemToEdit.DiscountBySkus" />
                    <MudTextField Label="Group Product" @bind-Value="ItemToEdit.GroupProduct" />
                    <MudStack Row="true" Justify="Justify.SpaceBetween" Class="mt-4">
                        <MudButton Color="Color.Error" Variant="Variant.Outlined" OnClick="CloseEditDrawer">Cancel
                        </MudButton>
                        <MudButton Color="Color.Primary" Variant="Variant.Filled"
                            OnClick="async () => await SaveEditAsync()">Save</MudButton>
                    </MudStack>
                </MudForm>
            </div>
        }
    </MudDrawerContainer>
</MudDrawer>
