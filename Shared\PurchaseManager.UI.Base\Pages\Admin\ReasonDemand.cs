﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;

using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Providers;

namespace PurchaseManager.UI.Base.Pages.Admin
{
    public class ReasonDemandBase : ComponentBase
    {
        [Inject] IViewNotifier viewNotifier { get; set; }
        [Inject] AuthenticationStateProvider authStateProvider { get; set; }
        [Inject] IApiClient apiClient { get; set; }

        [Inject] protected IStringLocalizer<Global> L { get; set; }

        protected IdentityAuthenticationStateProvider identityAuthenticationStateProvider;
        protected IEnumerable<DetailDemandReasonDto> listDemandReason { get; set; }
        protected bool createDialogOpen = false;
        protected bool editDialogOpen = false;
        protected CreateDemandReasonDto newReasonModel { get; set; } = new CreateDemandReasonDto();
        protected DetailDemandReasonDto currentReason { get; set; } = new DetailDemandReasonDto();
        protected override async Task OnInitializedAsync()
        {
            await LoadData();
            await base.OnInitializedAsync();
        }

        protected async Task LoadData()
        {
            try
            {
                var result = await apiClient.GetAllDemandReason();
                if (result.IsSuccessStatusCode) listDemandReason = result.Result;
                else viewNotifier.Show(result.Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
            catch (Exception e)
            {
                viewNotifier.Show(e.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
        }
        protected async Task UpdateReasonAsync()
        {
            try
            {
                var result = await apiClient.UpdateDemandReason(new UpdateDemandReasonDto() { ReasonCode = currentReason.ReasonCode, ReasonName = currentReason.ReasonName });
                if (result.IsSuccessStatusCode)
                {
                    viewNotifier.Show(result.Message, ViewNotifierType.Success, L["Operation Successful"]);
                    editDialogOpen = false;
                    await LoadData();
                }
                else viewNotifier.Show(result.Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
            catch (Exception e)
            {
                viewNotifier.Show(e.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
        }
        protected async Task OnDeleteReason(string reasonCode)
        {
            try
            {
                var result = await apiClient.BlockDemandReason(reasonCode);
                if (result.IsSuccessStatusCode)
                {
                    viewNotifier.Show(result.Message, ViewNotifierType.Success, L["Operation Successful"]);
                    await LoadData();
                }
                else viewNotifier.Show(result.Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
            catch (Exception e)
            {
                viewNotifier.Show(e.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
        }
        protected async Task CreateReasonAsync()
        {
            try
            {
                var result = await apiClient.InsertDemandReason(new CreateDemandReasonDto() { ReasonName = newReasonModel.ReasonName });
                if (result.IsSuccessStatusCode)
                {
                    viewNotifier.Show(result.Message, ViewNotifierType.Success, L["Operation Successful"]);
                    createDialogOpen = false;
                    await LoadData();
                }
                else viewNotifier.Show(result.Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
            catch (Exception e)
            {
                viewNotifier.Show(e.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
        }
    }
}
