﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Dto.Response;
using PurchaseManager.Shared.Models.PurchasePrice;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Managers;

public class PurchasePriceManager : IPurchasePriceManager
{
    private readonly ApplicationDbContext _context;
    private const string Business = "PURCHASEPRICE";
    private const string Branch = "AL";
    private readonly IMapper _mapper;
    private readonly IAdminManager _adminManager;
    public PurchasePriceManager(ApplicationDbContext context, IMapper mapper, IAdminManager adminManager)
    {
        _context = context;
        _mapper = mapper;
        _adminManager = adminManager;
    }
    public async Task<ApiResponse<List<ResponseWithDetailError>>> ValidateData(List<CreatePurchasePriceDto> listDataToAdd)
    {
        var errors = new List<ResponseWithDetailError>();

        var vendorNumbers = listDataToAdd.Select(x => x.VendorNumber).Distinct().ToList();
        var itemNumbers = listDataToAdd.Select(x => x.ItemNumber).Distinct().ToList();

        var validVendorNumbers = await _context.Vendors
            .Where(v => vendorNumbers.Contains(v.Number))
            .Select(v => v.Number)
            .ToListAsync();

        var invalidVendors = vendorNumbers.Except(validVendorNumbers);
        errors.AddRange(invalidVendors.Select(vendor => new ResponseWithDetailError
        {
            Field = vendor, Description = "Vendor not found"
        }));

        var validItemNumbers = await _context.Items
            .Where(i => itemNumbers.Contains(i.Number))
            .Select(i => i.Number)
            .ToListAsync();

        var invalidItems = itemNumbers.Except(validItemNumbers);
        errors.AddRange(invalidItems.Select(item => new ResponseWithDetailError
        {
            Field = item, Description = "Item not found"
        }));

        return errors.Count > 0 ? new ApiResponse<List<ResponseWithDetailError>>(400, "Validation failed", errors)
            : new ApiResponse<List<ResponseWithDetailError>>(200, "Validation successful", errors);
    }

    public async Task<ApiResponse> ImportFileData(List<CreatePurchasePriceDto> listDataToAdd)
    {
        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var itemNumbers = listDataToAdd.Select(x => x.ItemNumber).Distinct().ToList();
            var vendorNumbers = listDataToAdd.Select(x => x.VendorNumber).Distinct().ToList();

            var items = await _context.Items
                .Where(x => itemNumbers.Contains(x.Number))
                .ToDictionaryAsync(x => x.Number);

            var existingPrices = await _context.PurchasePrices
                .Where(x => vendorNumbers.Contains(x.VendorNumber) && itemNumbers.Contains(x.ItemNumber))
                .ToListAsync();

            foreach (var headerDto in listDataToAdd)
            {
                if (!items.TryGetValue(headerDto.ItemNumber, out var itemCheck) || itemCheck.BaseUnitOfMeasure == null)
                {
                    continue;
                }

                var existing = existingPrices.FirstOrDefault(x =>
                    x.VendorNumber == headerDto.VendorNumber &&
                    x.ItemNumber == headerDto.ItemNumber);

                var priceNumber = await _adminManager.CreateNumberSeries(Business, Branch);

                if (itemCheck.BaseUnitOfMeasure == null)
                {
                    continue;
                }

                if (existing != null)
                {
                    var updateDto = new UpdatePurchasePriceDto
                    {
                        DiscountBySkus = headerDto.DiscountBySkus,
                        FoCPromotion = headerDto.FoCPromotion,
                        GroupProduct = headerDto.GroupProduct,
                        Price = headerDto.Price,
                        VAT = headerDto.VAT,
                        Number = existing.Number
                    };
                    _mapper.Map(updateDto, existing);
                    existing.LastUpdateBy = _adminManager.GetUserLogin();
                    existing.Pic = _adminManager.GetUserLogin();
                    existing.LastUpdateAt = DateTime.Now;
                    existing.Status = (int)PurchasePriceEnum.Saved;
                }
                else
                {
                    // Create mới
                    var newEntry = _mapper.Map<PurchasePrice>(headerDto);
                    newEntry.Number = priceNumber;
                    newEntry.CreateBy = _adminManager.GetUserLogin();
                    newEntry.Pic = newEntry.CreateBy;
                    newEntry.CreateAt = DateTime.Now;
                    newEntry.Status = (int)PurchasePriceEnum.NewCreated;
                    newEntry.ItemCategory = itemCheck.ItemCategoryCode;

                    await _context.PurchasePrices.AddAsync(newEntry);
                }
            }
            await _context.SaveChangesAsync();
            await transaction.CommitAsync();
            return ApiResponse.S200("Import completed successfully.");
        }
        catch (Exception e)
        {
            await transaction.RollbackAsync();
            return ApiResponse.S500("Import failed: " + e.Message);
        }
    }
    public async Task<ApiResponse> CreatePurchasePrice(CreatePurchasePriceDto createPurchasePriceDto)
    {
        try
        {
            var item = await _context.Items.Where(x => x.Number == createPurchasePriceDto.ItemNumber).FirstOrDefaultAsync();
            if (item == null)
            {
                return new ApiResponse(404, "Item not found", createPurchasePriceDto.ItemNumber);
            }
            var vendor = await _context.Vendors.Where(x => x.Number == createPurchasePriceDto.VendorNumber).FirstOrDefaultAsync();
            if (vendor == null)
            {
                return new ApiResponse(404, "Vendor not found", createPurchasePriceDto.VendorNumber);
            }
            var priceNumber = await _adminManager.CreateNumberSeries(Business, Branch);
            var header = _mapper.Map<PurchasePrice>(createPurchasePriceDto);
            header.CreateBy = _adminManager.GetUserLogin();
            header.Pic = _adminManager.GetUserLogin();
            header.Number = priceNumber;
            header.CreateAt = DateTime.Now;
            header.Status = (int)PurchasePriceEnum.NewCreated;
            header.ItemCategory = item.ItemCategoryCode;
            await _context.PurchasePrices.AddAsync(header);
            await _context.SaveChangesAsync();
            return new ApiResponse(201, "Purchase Price Created", createPurchasePriceDto);
        }
        catch (Exception e)
        {
            return ApiResponse.S500("Error creating Purchase Price: " + e.Message);
        }
    }
    public async Task<ApiResponse> GetPurchasePrices(PurchasePriceFilter filter)
    {
        try
        {
            var take = filter.PageSize ?? 10;
            var skip = (filter.PageIndex ?? 0) * take;
            var query = _context.PurchasePrices
                .Where(h =>
                    (filter.VendorNumber == null || h.VendorNumber.Contains(filter.VendorNumber))
                    && h.Status != (int)PurchasePriceEnum.Deleted
                )
                .Include(x => x.ItemNumberNavigation)
                .Include(x => x.VendorNumberNavigation);

            var totalRecords = await query.CountAsync();

            var result = await query
                .OrderByDescending(h => h.RowId)
                .Skip(skip)
                .Take(take)
                .ToListAsync();

            var pagedResult = new PagedResultDto<GetPurchasePriceDto>
            {
                RowCount = totalRecords,
                CurrentPage = filter.PageIndex ?? 0,
                PageSize = filter.PageSize ?? 10,
                Data = _mapper.Map<List<GetPurchasePriceDto>>(result)
            };
            return new ApiResponse(200, "Purchase Prices", pagedResult);
        }
        catch (Exception e)
        {
            return new ApiResponse(500, "Error fetching Purchase Prices", e.Message);
        }
    }

    public async Task<ApiResponse> GetPurchasePrice(string priceNumber)
    {
        try
        {
            var purchasePrice = await _context.PurchasePrices
                .Include(x => x.ItemNumberNavigation)
                .Include(x => x.VendorNumberNavigation)
                .FirstOrDefaultAsync(x => x.Number == priceNumber);
            if (purchasePrice == null)
            {
                return new ApiResponse(404, "Purchase Price not found", priceNumber);
            }
            var mappedPurchasePrice = _mapper.Map<GetPurchasePriceDto>(purchasePrice);
            return new ApiResponse(200, "Purchase Price", mappedPurchasePrice);
        }
        catch (Exception e)
        {
            return new ApiResponse(500, "Error fetching Purchase Price", e.Message);
        }
    }
    public async Task<ApiResponse> UpdatePurchasePrice(UpdatePurchasePriceDto updatePurchasePriceDto)
    {
        try
        {
            var purchasePrice = await _context.PurchasePrices.FirstOrDefaultAsync(x => x.Number == updatePurchasePriceDto.Number);
            if (purchasePrice == null)
            {
                return new ApiResponse(404, "Purchase Price not found", updatePurchasePriceDto.Number);
            }

            _mapper.Map(updatePurchasePriceDto, purchasePrice);
            purchasePrice.LastUpdateBy = _adminManager.GetUserLogin();
            purchasePrice.Pic = _adminManager.GetUserLogin();
            purchasePrice.LastUpdateAt = DateTime.Now;
            purchasePrice.Status = (int)PurchasePriceEnum.Saved;
            await _context.SaveChangesAsync();
            return new ApiResponse(200, "Purchase Price updated successfully", purchasePrice);
        }
        catch (Exception e)
        {
            return ApiResponse.S500("Error updating Purchase Price", e.Message);
        }
    }
    public async Task<ApiResponse> DeletePurchasePrice(string priceNumber)
    {
        try
        {
            var purchasePrice = await _context.PurchasePrices.FirstOrDefaultAsync(x => x.Number == priceNumber);
            purchasePrice.Status = (int)PurchasePriceEnum.Deleted;
            purchasePrice.LastUpdateBy = _adminManager.GetUserLogin();
            purchasePrice.LastUpdateAt = DateTime.Now;
            await _context.SaveChangesAsync();
            return new ApiResponse(200, "Purchase Price deleted successfully", priceNumber);
        }
        catch (Exception e)
        {
            return new ApiResponse(500, "Error deleting Purchase Price", e.Message);
        }
    }
    public async Task<ApiResponse<List<GetPurchasePriceDto>>> GetPriceAndUnit(string itemNumber, string vendorNumber)
    {
        try
        {
            var data = await _context.PurchasePrices
                .Where(x => x.ItemNumber == itemNumber && x.VendorNumber == vendorNumber)
                .Include(h => h.ItemNumberNavigation)
                .Include(h => h.VendorNumberNavigation)
                .ToListAsync();
            var response = _mapper.Map<List<GetPurchasePriceDto>>(data);
            return new ApiResponse<List<GetPurchasePriceDto>>(200, "Success", response);
        }
        catch (Exception e)
        {
            return new ApiResponse(500, "Error deleting Purchase Price", e.Message);
        }
    }
}
