using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.PO;

namespace PurchaseManager.Theme.Material.Shared.Components;

public partial class AlertPaymentDayBase : ComponentBase
{
        [Parameter] public List<CheckPaymentDayResponse> Parameters { get; set; }
        [Inject] public IStringLocalizer<Global> L { get; set; }

        protected override void OnParametersSet()
        {
                base.OnParametersSet();
        }
}
