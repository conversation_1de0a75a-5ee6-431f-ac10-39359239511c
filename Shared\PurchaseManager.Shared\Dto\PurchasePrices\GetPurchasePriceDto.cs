﻿namespace PurchaseManager.Shared.Dto.PurchasePrices;

public class GetPurchasePriceDto
{
    public string Number { get; set; }
    public string VendorNumber { get; set; }
    public string VendorName { get; set; }
    public string ItemNumber { get; set; }
    public string ItemName { get; set; }
    public string PurchasingUnit { get; set; }
    public decimal PriceAftDiscountNoVat { get; set; }
    public decimal PriceAftDiscountVat { get; set; }
    public int VAT { get; set; }
    public int Status { get; set; }
    public string ItemCategory { get; set; }
    public decimal PriceBefVat { get; set; }
    public decimal PriceAftVat { get; set; }
    public string FoCPromotion { get; set; }
    public string Pic { get; set; }
    public int DiscountBySkus { get; set; }
    public string GroupProduct { get; set; } = string.Empty;
}
