@using PurchaseManager.Shared.Dto.PO
@inherits ReplenishmentBasePage
@page "/PO/replenishment"
@attribute [Authorize]
<PageTitle>Replenishment</PageTitle>
<MudCard>
    <MudCardHeader>
        <CardHeaderContent>
            <MudText Typo="Typo.h4" Align="Align.Center">@L["Replenishment"]</MudText>
        </CardHeaderContent>
    </MudCardHeader>
    <MudCardContent>
        <MudTable T="DetailPoDto"
                  @ref="table"
                  @bind-SelectedItems="SelectedItems"
                  @bind-SelectedItem="SelectedDetailPO"
                  Dense
                  Hover
                  ReadOnly="@IsDisableEditTable"
                  CanCancelEdit
                  MultiSelection
                  RowClass=""
                  Elevation="0"
                  Height="68vh"
                  Outlined="true"
                  FixedHeader="true"
                  SortLabel="Sort By"
                  Class="rounded-0 pa-2"
                  SelectOnRowClick="false"
                  CommitEditTooltip="@L["Save"]"
                  LoadingProgressColor="Color.Info"
                  Loading="@isBusy"
                  ServerData="@(new Func<TableState, CancellationToken, Task<TableData<DetailPoDto>>>(ServerReload))"
                  GroupBy="@GroupDefinition"
                  GroupHeaderStyle="background-color:var(--mud-palette-background-gray)"
                  GroupFooterClass="mb-4">
            <ToolBarContent>
                <MudBadge Origin="Origin.TopRight" Content="@(SelectedItems.Count())" Color="Color.Primary" Overlap="true" Bordered="@true">
                    <MudButton StartIcon="@Icons.Material.Filled.Add"
                               Disabled="@(SelectedItems.Any(x => x.Quantity == 0) || !SelectedItems.Any() || POCreating)"
                        OnClick="CheckPaymentDayByVendorNumber" ButtonType="ButtonType.Submit" Variant="Variant.Filled"
                        Color="Color.Success" Class="">
                        @if (POCreating)
                        {
                            @L["Creating PO, please wait..."]
                        }
                        else
                        {
                            @L["Create PO"]
                        }
                    </MudButton>
                </MudBadge>
                <MudSpacer />
                <MudDatePicker Label="Create Month" OpenTo="OpenTo.Month" @ref="DateFilterRef" FixYear="@DateTime.Today.Year" FixDay="@DateTime.Today.Day"
                               Clearable="true" DateFormat="MM" DateChanged="@(async (date) => await OnSearchByDate(date))" Class="mr-4"/>
                <MudTextField T="string"
                              ValueChanged="@(s=>OnSearchByVendor(s))"
                              @ref="SearchByVendorRef"
                              OnAdornmentClick="@(async () => { await SearchByVendorRef.Clear(); })"
                              Adornment="Adornment.End"
                              AdornmentIcon="@Icons.Material.Filled.Clear"
                              Label="Search by vendor name or number"
                              Variant="Variant.Text"
                              IconSize="Size.Small"
                              AdornmentColor="Color.Error"
                              Class="mt-0">
                </MudTextField>
                <MudTextField T="string"
                              ValueChanged="@(s=>OnSearchByItemNameOrItemNumber(s))"
                              Label="Search by item name or number"
                              Variant="Variant.Text"
                              @ref="SearchByItemRef"
                              OnAdornmentClick="@(async () => { await SearchByItemRef.Clear(); })"
                              Adornment="Adornment.End"
                              AdornmentIcon="@Icons.Material.Filled.Clear"
                              IconSize="Size.Small"
                              AdornmentColor="Color.Error"
                              Class="mt-0 ml-4">
                </MudTextField>
                <div class="mx-4">
                    <MudIconButton Icon="@Icons.Material.Filled.ClearAll" Color="Color.Error" OnClick="@(async () =>
                                                                                                       {
                                                                                                           await SearchByVendorRef.Clear();
                                                                                                           await SearchByItemRef.Clear();
                                                                                                           await DateFilterRef.ResetAsync();
                                                                                                       })" Size="Size.Small" Class="ml-2 mt-4"/>
                    <MudIconButton Icon="@Icons.Material.Filled.Refresh"
                                   OnClick="@(async () =>
                                            {
                                                await Reload();
                                                SelectedItems.Clear();
                                            })" Size="Size.Small"
                        Class="ml-2 mt-4" />
                </div>
            </ToolBarContent>
            <ColGroup>
                <col style="width: 60px;" />
                <col/>
                <col/>
                <col />
                <col />
                <col/>
                <col />
                <col />
                <col />
                <col />
                <col />
                <col/>
            </ColGroup>
            <GroupHeaderTemplate>
                <MudTh Class="mud-table-cell-custom-group" colspan="10">@($"{context.Key}") </MudTh>
            </GroupHeaderTemplate>
            <HeaderContent>
                <MudTh>
                    <MudTableSortLabel SortLabel="VendorNumber" T="DetailPoDto">VendorNo </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="VendorNumber" T="DetailPoDto">Vendor Name </MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="ItemName" T="DetailPoDto">Item</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="Min" T="DetailPoDto">Min</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="Max" T="DetailPoDto">Max</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="Stock" T="DetailPoDto">Stock</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="Quantity" T="DetailPoDto">Quantity</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="Suggest" T="DetailPoDto">Suggest</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="TotalOrder" T="DetailPoDto">TotalOrder</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="CreateDate" T="DetailPoDto">CreateDate</MudTableSortLabel>
                </MudTh>
            </HeaderContent>
            <RowTemplate Context="row">
                <MudTd DataLabel="VendorNumber">
                    <p>@row.VendorNumber</p>
                </MudTd>
                <MudTd DataLabel="VendorName">
                    <p>@row.VendorName</p>
                </MudTd>
                <MudTd DataLabel="ItemName">
                    <MudText iun Typo="Typo.caption"> @row.ItemName</MudText>
                    <div><MudChip T="string" Variant="Variant.Text" Color="Color.Primary" Size="Size.Small">ItemNo: @row.ItemNumber</MudChip></div>
                </MudTd>
                <MudTd DataLabel="Min">@row.Min</MudTd>
                <MudTd DataLabel="Max">@row.Max </MudTd>
                <MudTd DataLabel="Stock">@row.Stock </MudTd>
                <MudTd DataLabel="Quantity">
                    <MudNumericField T="Int32" Style="text-align:right" Immediate="true" @bind-Value="row.Quantity"
                                     OnBlur="() => DetailPOHasBeenCommitted(row)"/>
                </MudTd>
                <MudTd DataLabel="Suggest">@row.Suggest </MudTd>
                <MudTd DataLabel="TotalOrder">@row.TotalOrder</MudTd>
                <MudTd DataLabel="CreateDate">@row.CreateDate.ToString("dd/MM/yyyy")</MudTd>
            </RowTemplate>
            <PagerContent>
                <MudTablePager RowsPerPageString="Rows per page" />
            </PagerContent>
            <PagerContent>
                <MudTablePager PageSizeOptions="new int[] { 30, 60, 120 }" />
            </PagerContent>
            <NoRecordsContent>
                No Data
            </NoRecordsContent>
        </MudTable>
    </MudCardContent>
</MudCard>
<MudDialog @bind-Visible="@IsAlertDialogOpen" Options="@(new DialogOptions()
                                                       {
                                                           MaxWidth = MaxWidth.Medium,
                                                           FullWidth = true
                                                       })">
        <TitleContent>
            <MudText Typo="Typo.h6">
                <MudIcon Icon="@Icons.Material.Filled.ReportProblem"  Color="Color.Warning" Class="mr-3 mb-n1" />
                @L["There are a number of vendors who have PO available"]
            </MudText>
        </TitleContent>
        <DialogContent>
            <AlertPaymentDay Parameters="@ListCheckPaymentDayResponse"></AlertPaymentDay>
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="@(e => { IsAlertDialogOpen = false; })">@L["Cancel"]</MudButton>
        <MudButton OnClick="@( async ()=> await CreatePO())" Color="Color.Primary">@L["Continue create"]</MudButton>
        </DialogActions>
</MudDialog>