using System.Security.Claims;
using PurchaseManager.Infrastructure.AuthorizationDefinitions;
namespace PurchaseManager.Shared.Extensions;

public static class RoleExtension
{
    public static bool IsPurchaseUser(this ClaimsPrincipal principal)
    {
        return principal.Claims.Any(x => x.Type == Policies.IsPurchaseUser);
    }
    public static bool IsPurchaseManager(this ClaimsPrincipal principal)
    {
        return principal.Claims.Any(x => x.Type == Policies.IsPurchaseManager);
    }
    public static bool IsVendor(this ClaimsPrincipal principal)
    {
        return principal.Claims.Any(x => x.Type == Policies.IsVendor);
    }
    public static bool IsVendorContact(this ClaimsPrincipal principal)
    {
        return principal.Claims.Any(x => x.Type == Policies.IsVendorContact);
    }
    public static bool IsAdmin(this ClaimsPrincipal principal)
    {
        return principal.Claims.Any(x => x.Type == Policies.IsAdmin);
    }
    public static bool IsTSUser(this ClaimsPrincipal principal)
    {
        return principal.Claims.Any(x => x.Type == Policies.IsTSUser);
    }
    public static bool IsVendorAndPurchaseUser(this ClaimsPrincipal principal)
    {
        return principal.Claims.Any(x => x.Type == Policies.IsVendorAndPurchaseUser);
    }
    public static bool IsWarehouseManager(this ClaimsPrincipal principal)
    {
        return principal.Claims.Any(x => x.Type == Policies.IsWarehouseManager);
    }
    public static bool IsWarehouseUser(this ClaimsPrincipal principal)
    {
        return principal.Claims.Any(x => x.Type == Policies.IsWarehouseUser);
    }
    public static bool IsMKT(this ClaimsPrincipal principal)
    {
        return principal.Claims.Any(x => x.Type == Policies.IsMKT);
    }
}
