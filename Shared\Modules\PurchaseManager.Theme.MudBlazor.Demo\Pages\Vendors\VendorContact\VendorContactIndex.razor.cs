﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.Extensions.Localization;
using MudBlazor;
using PurchaseManager.Shared.Dto.Contact;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Models.Contact;
using PurchaseManager.Shared.Providers;
namespace PurchaseManager.Theme.Material.Demo.Pages.Vendors.VendorContact;
public class VendorPage : ComponentBase
{
    #region Parameters
    [Parameter]
    public string VendorNumberParam { get; set; }
    [Parameter]
    public string VendorNameParam { get; set; }
    [CascadingParameter] private Task<AuthenticationState> AuthenticationStateTask { get; set; }
    #endregion
    #region DIs
    protected MudAutocomplete<GetVendorDto> vendorAutoRef { get; set; } = new MudAutocomplete<GetVendorDto>();
    protected IVendorApiClient vendorApiClient { get; set; }
    [Inject] public IStringLocalizer<Global> L { get; set; }
    [Inject] protected IViewNotifier viewNotifier { get; set; }
    [Inject] protected HttpClient HttpClient { get; set; }
    [Inject] protected NavigationManager NavigationManager { get; set; }
    [Inject] protected AuthenticationStateProvider AuthStateProvider { get; set; }
    [Inject] protected IApiClient apiClient { get; set; }
    #endregion
    #region Variables
    protected ContactFilter contactFilter { get; set; } = new();
    protected bool IsLoading { get; set; }
    protected UserViewModel userViewModel { get; set; } = new UserViewModel();
    protected DateTime? FromDate { get; set; }
    protected DateTime? ToDate { get; set; }
    protected DateRange DateRange { get; set; } = new DateRange();
    protected string UserId { get; set; } = "";
    protected string VendorName { get; set; } = "";
    protected GetVendorDto vendorFilter { get; set; } = new GetVendorDto();
    protected VendorFilter vendorFilters { get; set; } = new VendorFilter();
    protected IEnumerable<GetContactDto> listGetContactDto { get; set; }
    #region Roles
    protected bool isPurchaseUser { get; set; }
    protected bool isPurchaseManager { get; set; }
    protected bool isVendor { get; set; }
    protected bool isVendorContact { get; set; }
    protected bool isAdmin { get; set; }
    protected bool isAuthorized { get; set; }
    #endregion
    #endregion
    protected override async Task OnInitializedAsync()
    {
        IsLoading = true;
        await GetUserRoleAsync();
        await GetUserInfoAsync();
        AuthorizeUser();
        if (isAuthorized) await GetAllContactAsync();
        await base.OnInitializedAsync();
        IsLoading = false;
    }
    #region Api Calls
    protected async Task GetAllContactAsync()
    {
        try
        {
            IsLoading = true;
            listGetContactDto = [];
            contactFilter.VendorNumber = VendorNumberParam;
            var resp = await apiClient.GetAllContacts(contactFilter);
            if (!resp.Results.Any())
                viewNotifier.Show("No Data", ViewNotifierType.Error, L["Operation Failed"]);
            else
            {
                var data = resp.Results.ToList();
                listGetContactDto = data;
                var firstContact = data.FirstOrDefault();
                if (firstContact is not null)
                {
                    VendorName = firstContact.VendorName;
                }
            }
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        finally
        {
            IsLoading = false;
        }
    }
    protected async Task OnToggleContactStatusAsync(string number)
    {
        try
        {
            var resp = await apiClient.BlockOrUnblockContact(number);
            viewNotifier.Show(resp.Message, ViewNotifierType.Success, L["Operation Successfully"]);
            await GetAllContactAsync();
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    protected async Task<IEnumerable<GetVendorDto>> SearchVendorByKeywordAsync(string value, CancellationToken token)
    {
        var apiResponse = await apiClient.SearchVendorByNameOrNumber(value, value, token);
        if (apiResponse.IsSuccessStatusCode)
        {
            return apiResponse.Result;
        }
        return [];
    }
    #endregion Api Calls
    protected async Task OnReloadTableAsync()
    {
        contactFilter = new ContactFilter();
        await GetAllContactAsync();
    }
    protected async Task OnKeyUpAsync(KeyboardEventArgs e)
    {
        if (e.Code == "Enter") await GetAllContactAsync();
    }
    protected async Task OnSearchContactByFieldAsync()
    {
        await GetAllContactAsync();
    }
    protected void OnClearFilterByVendor()
    {
        vendorFilters.Number = null;
        NavigationManager.NavigateTo("/vendor-contact");
        StateHasChanged();
    }
    protected async Task GetUserRoleAsync()
    {
        var authState = await AuthenticationStateTask;
        var user = authState.User;
        isPurchaseUser = user.IsPurchaseUser();
        isAdmin = user.IsAdmin();
        isVendor = user.IsVendor();
    }
    private void AuthorizeUser()
    {
        var userVendorCode = userViewModel.VendorCode;
        if (VendorNumberParam.Equals(userVendorCode, StringComparison.OrdinalIgnoreCase))
        {
            isAuthorized = true;
        }
        if (isAdmin || isPurchaseUser || isPurchaseManager)
            isAuthorized = true;
        // else
        //     isAuthorized = false;
        // if (!VendorNumberParam.Equals(userVendorCode, StringComparison.OrdinalIgnoreCase) && !isAdmin)
        // {
        //     isAuthorized = false;
        // }
    }
    protected async Task GetUserInfoAsync()
    {
        userViewModel = await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
        if (userViewModel is not null)
        {
            UserId = userViewModel.UserName;
        }
    }
    protected async Task OnSearchContactByVendorNameAsync(GetVendorDto vendorViewModel)
    {
        if (vendorViewModel is not null)
        {
            contactFilter.VendorNumber = vendorViewModel.Number;
            await GetAllContactAsync();
        }
    }
    protected void GetStaticData()
    {
        DateRange.Start = DateTime.Now.Date.AddDays(-30);
        DateRange.End = DateTime.Now.Date;
        FromDate = DateRange.Start;
        ToDate = DateRange.End;
    }
    protected List<string> RenderTags(string tags)
    {
        if (string.IsNullOrEmpty(tags)) return [];
        var ts = tags.Split(',').ToList();
        return ts.Count == 0 ? null : ts;
    }
    protected void OnNavigateToAddContact()
    {
        NavigationManager.NavigateTo($"/vendors/{VendorNumberParam}/contact/add");
    }
    protected void OnNavigateToListVendor()
    {
        NavigationManager.NavigateTo($"/vendors/{VendorNumberParam}/detail");
    }
    protected void OnNavigateToDetailContact(string contactNumber)
    {
        NavigationManager.NavigateTo($"vendors/{VendorNumberParam}/contacts/{contactNumber}/detail");
    }
}
