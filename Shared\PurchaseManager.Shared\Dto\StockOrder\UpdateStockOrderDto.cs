﻿namespace PurchaseManager.Shared.Dto.StockOrder;

public class UpdateStockOrderDto
{
    public string Number { get; set; } = null!;
    public string HeaderNumber { get; set; } = null!;
    public int LineNumber { get; set; }
    public int DocumentType { get; set; }
    public string ItemNumber { get; set; } = null!;
    public string LotNo { get; set; } = null!;
    public DateTime ExpirationDate { get; set; }
    public int TotalQuantity { get; set; }
    public int QuantityReceived { get; set; }
    public string ItemName { get; set; } = null!;
    public string Note { get; set; } = null!;
    public int Status { get; set; } = 1; // 1: Draft, 2: Saved, 3: Completed (đã tạo phiếu nhập)
}
