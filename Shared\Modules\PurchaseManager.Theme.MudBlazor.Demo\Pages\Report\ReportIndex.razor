@page "/reports"
@inherits PurchaseManager.Theme.Material.Demo.Pages.Report.ReportIndexBase
<MudCard>
    <MudCardHeader>
        <CardHeaderContent>
            <MudText Typo="Typo.h5" Align="Align.Center">@L["Reports"]</MudText>
        </CardHeaderContent>
    </MudCardHeader>
    <MudCardContent>
        <MudSelect T="PurchaseManager.Shared.Models.Report.ReportSelectItem" Underline="false" MultiSelection="false"
            Label="Please chose module to get report" Variant="Variant.Outlined" @bind-Value="@selectedModule"
            AnchorOrigin="Origin.BottomCenter">
            @foreach (var item in reportSelectItems)
            {
                <MudSelectItem Value="@item">@item.Value</MudSelectItem>
            }
        </MudSelect>
        @if (selectedModule is not null)
        {
            @if (selectedModule.Key.Equals("PO"))
            {
                <PoReport></PoReport>
            }
            else if (selectedModule.Key.Equals("VD"))
            {
                <MudPaper Class="mt-8 py-3">
                    <MudText Align="Align.Center">render report view by vendor here</MudText>
                </MudPaper>
            }
            else if (selectedModule.Key.Equals("IT"))
            {
                <MudPaper Class="mt-8 py-3">
                    <MudText Align="Align.Center">render report view by item here</MudText>
                </MudPaper>
            }
        }
    </MudCardContent>
</MudCard>