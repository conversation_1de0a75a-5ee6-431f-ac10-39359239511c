﻿using AutoMapper;
using Breeze.AspNetCore;
using Breeze.Persistence;
using Breeze.Persistence.EFCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.AuthorizationDefinitions;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.PO;
using PurchaseManager.Storage;

using ApiLogItem = PurchaseManager.Infrastructure.Storage.DataModels.ApiLogItem;
using ApplicationRole = PurchaseManager.Infrastructure.Storage.DataModels.ApplicationRole;
using ApplicationUser = PurchaseManager.Infrastructure.Storage.DataModels.ApplicationUser;
using DbLog = PurchaseManager.Infrastructure.Storage.DataModels.DbLog;
using NumberSeries = PurchaseManager.Infrastructure.Storage.DataModels.NumberSeries;
using NumberSeriesLine = PurchaseManager.Infrastructure.Storage.DataModels.NumberSeriesLine;
using PurchaseOrderHeader = PurchaseManager.Infrastructure.Storage.DataModels.PurchaseOrderHeader;
using TenantSetting = PurchaseManager.Infrastructure.Storage.DataModels.TenantSetting;
using Todo = PurchaseManager.Infrastructure.Storage.DataModels.Todo;
using UserProfile = PurchaseManager.Infrastructure.Storage.DataModels.UserProfile;

namespace PurchaseManager.Server.Controllers;

// [OpenApiIgnore]
[Route("api/data/[action]")]
[Authorize]
[BreezeQueryFilter]
public class ApplicationController : Controller
{
    private readonly ApplicationPersistenceManager persistenceManager;
    private readonly IMapper _mapper;
    public ApplicationController(ApplicationPersistenceManager persistenceManager, IMapper mapper)
    {
        this.persistenceManager = persistenceManager;
        _mapper = mapper;
    }

    [AllowAnonymous]
    [HttpGet]
    public string Metadata()
    {
        return persistenceManager.Metadata();
    }

    [AllowAnonymous]
    [HttpGet]
    public Task<UserProfile> UserProfile()
    {
        return persistenceManager.GetUserProfile();
    }

    [AllowAnonymous]
    [HttpGet]
    public IQueryable<TenantSetting> TenantSettings()
    {
        return persistenceManager.GetEntities<TenantSetting>().AsNoTracking();
    }

    [HttpGet]
    [Authorize(Policies.IsAdmin)]
    public IQueryable<ApplicationUser> Users()
    {
        return persistenceManager.GetEntities<ApplicationUser>().AsNoTracking().Include(i => i.UserRoles).ThenInclude(i => i.Role).OrderBy(i => i.UserName);
    }

    [HttpGet]
    [Authorize(Policies.IsAdmin)]
    public IQueryable<ApplicationRole> Roles()
    {
        return persistenceManager.GetEntities<ApplicationRole>().AsNoTracking().OrderBy(i => i.Name);
    }

    [AllowAnonymous]
    [HttpGet]
    public IQueryable<Todo> Todos([FromQuery] ToDoFilter filter)
    {
        return persistenceManager.GetEntities<Todo>().AsNoTracking()
            .Include(i => i.CreatedBy)
            .Include(i => i.ModifiedBy)
            .Where(i =>
                (filter.From == null || i.CreatedOn >= filter.From) && (filter.To == null || i.CreatedOn <= filter.To) &&
                (filter.CreatedById == null || i.CreatedById == filter.CreatedById) &&
                (filter.ModifiedById == null || i.ModifiedById == filter.ModifiedById) &&
                (filter.IsCompleted == null || i.IsCompleted == filter.IsCompleted) &&
                (filter.Query == null || i.Title.ToLower().Contains(filter.Query.ToLower())))
            .OrderByDescending(i => i.CreatedOn);
    }

    [AllowAnonymous]
    [HttpGet]
    public IQueryable<ApplicationUser> TodoCreators([FromQuery] ToDoFilter filter)
    {
        filter.CreatedById = null;

        return Todos(filter).Where(i => i.CreatedBy != null).Select(i => i.CreatedBy).Distinct().AsNoTracking();
    }

    [AllowAnonymous]
    [HttpGet]
    public IQueryable<ApplicationUser> TodoEditors([FromQuery] ToDoFilter filter)
    {
        filter.ModifiedById = null;

        return Todos(filter).Where(i => i.ModifiedBy != null).Select(i => i.ModifiedBy).Distinct().AsNoTracking();
    }

    [HttpGet]
    [Authorize(Policies.IsAdmin)]
    public IQueryable<DbLog> Logs()
    {
        return persistenceManager.GetEntities<DbLog>().AsNoTracking().OrderByDescending(i => i.TimeStamp);
    }

    [HttpGet]
    [Authorize(Policies.IsAdmin)]
    public IQueryable<ApiLogItem> ApiLogs()
    {
        return persistenceManager.GetEntities<ApiLogItem>().AsNoTracking().OrderByDescending(i => i.RequestTime);
    }

    [AllowAnonymous]
    [HttpPost]
    public SaveResult SaveChanges([FromBody] JObject saveBundle)
    {
        try
        {
            return persistenceManager.SaveChanges(saveBundle);
        }
        catch (EntityErrorsException)
        {
            throw;
        }
        catch (Exception ex)
        {
            var errors = new List<EFEntityError>
            {
                new EFEntityError(null, null, ex.GetBaseException().Message, null)
            };

            throw new EntityErrorsException(errors);
        }
    }

    //---------------------------------------------------------------------------------------------------------------------------------------------
    [AllowAnonymous]
    [HttpGet]
    public IQueryable<NumberSeries> NumberSeries()
    {
        return persistenceManager.GetEntities<NumberSeries>().AsNoTracking()
            .Include(i => i.NumberSeriesLines)
            .OrderBy(i => i.RowId);
    }

    [AllowAnonymous]
    [HttpGet]
    public IQueryable<NumberSeriesLine> NumberSeriesLine()
    {
        return persistenceManager.GetEntities<NumberSeriesLine>().AsNoTracking()
            .Include(i => i.NumberSeriesCodeNavigation)
            .OrderBy(i => i.RowId);
    }

    [AllowAnonymous]
    [HttpGet]
    public IQueryable<string> SeriesCode()
    {
        return NumberSeries().Select(i => i.Code).Distinct().AsNoTracking();
    }
    [AllowAnonymous]
    [HttpGet]
    public IQueryable<Schedule> GetSchedule([FromQuery] ScheduleFilter filter)
    {
        var query = persistenceManager.GetEntities<PurchaseOrderHeader>().AsNoTracking()
            .Where(i =>
                (filter.IsMKT == null || i.IsMKT == filter.IsMKT) &&
                (filter.From == null || i.DueDate >= filter.From) &&
                (filter.To == null || i.DueDate <= filter.To) &&
                (i.Status >= (int)PurchaseOrderEnum.Approve))
            .OrderByDescending(i => i.RowId);
        return _mapper.ProjectTo<Schedule>(query);
    }
    [AllowAnonymous]
    [HttpGet]
    [BreezeQueryFilter]
    public IQueryable<DetailPoDto> GetPurchases([FromQuery] PurchaseOrderFilter filter)
    {
        var query = persistenceManager.GetEntities<PurchaseManager.Infrastructure.Storage.DataModels.PurchaseOrder>().AsNoTracking()
        .Where(i =>
            (filter.Month == null || i.CreateDate.Month == filter.Month.Value.Month && i.CreateDate.Year == filter.Month.Value.Year) &&
            ((filter.Vendor == null || i.VendorNumber.ToLower().Contains(filter.Vendor.ToLower())) ||
            (filter.Vendor == null || i.VendorName.ToLower().Contains(filter.Vendor.ToLower()))) &&
            ((filter.Query == null || i.ItemName.ToLower().Contains(filter.Query.ToLower())) ||
             (filter.Query == null || i.ItemNumber.ToLower().Contains(filter.Query.ToLower()))))
        .OrderByDescending(i => i.RowId);
        return _mapper.ProjectTo<DetailPoDto>(query);
    }
}
