using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using System.Reflection;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Report;
using PurchaseManager.Shared.Models.Report.PO;
using Microsoft.JSInterop;
namespace PurchaseManager.Theme.Material.Demo.Pages.Report;
public partial class PoReportBase : ComponentBase
{
    protected bool isLoading { get; set; }
    [Inject] public IStringLocalizer<Global> L { get; set; }
    [Inject] protected IViewNotifier viewNotifier { get; set; }
    protected PoReportFields poReportFields { get; set; } = new PoReportFields();
    protected PoReportParams poReportParams { get; set; } = new PoReportParams();
    protected IReadOnlyCollection<string> selectedFields;
    protected bool isDownloading { get; set; }
    protected List<string> listFieldCanSelect { get; set; } = new List<string>();
    protected object selectedStatus { get; set; }
    protected string dataToDownload { get; set; }
    protected bool selectAllField { get; set; }
    protected MudAutocomplete<GetVendorDto> vendorAutoRef { get; set; } = new MudAutocomplete<GetVendorDto>();
    protected DateRange dateRange { get; set; } = new DateRange();
    protected MudDateRangePicker fromToDatePickerRef { get; set; } = new MudDateRangePicker();
    [Inject] public IApiClient apiClient { get; set; }
    [Inject] public IJSRuntime jSRuntime { get; set; }
    protected override void OnInitialized()
    {
        isLoading = true;
        var today = DateTime.Today;
        var month = new DateTime(today.Year, today.Month, 1);
        var first = month;// the first day of current month
        var last = month.AddMonths(1).AddDays(-1); // the last day of current month
        poReportParams.FromDate = first;
        poReportParams.ToDate = last;
        dateRange = new DateRange(first, last);
        LoadSelectItems();
        base.OnInitialized();
        isLoading = false;
    }
    protected void LoadSelectItems()
    {
        listFieldCanSelect = typeof(PoReportFields).GetProperties(BindingFlags.Public | BindingFlags.Instance).Select(x => x.Name).ToList();
    }
    protected void OnSearchContactByVendorName(GetVendorDto vendorViewModel)
    {
        if (vendorViewModel is not null)
        {
            poReportParams.VendorNumber = vendorViewModel.Number;
        }
    }
    protected void OnClickCheckAllField()
    {
        if (selectAllField) selectedFields = listFieldCanSelect;
        else selectedFields = new List<string>();
    }
    protected void OnClickChip()
    {
        if (selectedFields.Count() != listFieldCanSelect.Count()) selectAllField = false;
    }
    protected async Task<IEnumerable<GetVendorDto>> ItemSearch(string value, CancellationToken token)
    {
        var apiResponse = await apiClient.SearchVendorByNameOrNumber(value, value, token);
        if (apiResponse.IsSuccessStatusCode)
        {
            return apiResponse.Result;
        }
        return new List<GetVendorDto>();
    }
    protected async Task DownloadPoReport()
    {
        try
        {
            var fields = new PoReportFields();
            foreach (var property in typeof(PoReportFields).GetProperties(BindingFlags.Public | BindingFlags.Instance))
            {
                if (selectedFields.Contains(property.Name))
                {
                    property.SetValue(fields, true);
                }
            }
            var pars = new PoReportParams
            {
                FromDate = dateRange.Start,
                ToDate = dateRange.End
            };
            if (selectedStatus is not null) pars.Status = (int)selectedStatus;
            if (vendorAutoRef.Value is not null) pars.VendorNumber = vendorAutoRef.Value.Number.ToString();
            var req = new PoReportRequest()
            {
                poReportFields = fields,
                poReportParams = pars
            };
            isDownloading = true;
            var resp = await apiClient.GetAllPoReportByParams(req);
            if (resp.IsSuccessStatusCode)
            {
                if (resp.Result != null)
                {
                    var fileName = "PO_" + DateTime.Now.ToString("dd-MM-yyyy_") + DateTime.Now.ToString("HH-mm-ss") + ".xlsx";
                    await jSRuntime.InvokeVoidAsync("downloadFileFromBase64", fileName, resp.Result);
                }
                else viewNotifier.Show("No data to export", ViewNotifierType.Info, L["Operation Successfully"]);
            }
            else
            {
                viewNotifier.Show(resp.Result.ToString(), ViewNotifierType.Error, L["Operation Failed"]);
            }
            isDownloading = false;
        }
        catch (Exception ex)
        {
            isDownloading = false;
            viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }
    protected async Task FilterByDate()
    {
        if (fromToDatePickerRef.DateRange is null) return;
        dateRange = fromToDatePickerRef.DateRange;
        await fromToDatePickerRef.CloseAsync();
    }
}
