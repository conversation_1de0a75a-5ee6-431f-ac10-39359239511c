﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="AutoMapper" Version="13.0.1"/>
        <PackageReference Include="Breeze.Persistence.EFCore" Version="7.3.0" />
        <PackageReference Include="Dapper" Version="2.1.35" />
        <PackageReference Include="Finbuckle.MultiTenant.AspNetCore" Version="6.13.1" />
        <PackageReference Include="Finbuckle.MultiTenant.EntityFrameworkCore" Version="6.13.1" />
        <PackageReference Include="Karambolo.PO" Version="1.11.1" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.6" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.6" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.6" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.6">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.4" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite" Version="8.0.6" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Shared\PurchaseManager.Infrastructure\PurchaseManager.Infrastructure.csproj" />
        <ProjectReference Include="..\..\Shared\PurchaseManager.Shared.Localizer\PurchaseManager.Shared.Localizer.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Migrations\ApplicationDb\" />
        <Folder Include="Migrations\LocalizationDb\" />
        <Folder Include="Migrations\TenantStoreDb\" />
    </ItemGroup>
</Project>
