using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Models;

namespace PurchaseManager.Infrastructure.Server;

public interface IGrpcManager
{
    public Task<ApiResponse> SyncItem(SyncRequest syncRequest, CancellationToken cancellationToken);
    public Task<ApiResponse> SyncIUOM(SyncRequest syncRequest, CancellationToken cancellationToken);
    public Task<ApiResponse> SyncUOM(SyncRequest syncRequest, CancellationToken cancellationToken);
    public Task<ApiResponse> SyncVendor(SyncRequest syncRequest, CancellationToken cancellationToken);
}
