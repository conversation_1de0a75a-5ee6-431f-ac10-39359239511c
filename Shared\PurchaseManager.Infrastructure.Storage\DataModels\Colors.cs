﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Table("Colors")]
public partial class Colors
{
    [Key]
    [StringLength(100)]
    public string ColorCode { get; set; } = null!;
    
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }
    
    [StringLength(100)]
    public string ColorName { get; set; } = null!;
    
    
    [InverseProperty("ColorCodeNavigation")]
    public virtual ICollection<ItemColors> ItemColors { get; set; } = new List<ItemColors>();
}

