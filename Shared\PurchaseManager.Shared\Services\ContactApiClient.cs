﻿using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Contact;
using PurchaseManager.Shared.Interfaces;
namespace PurchaseManager.Shared.Services;

public class ContactApiClient : BaseApiClient, IContactApiClient
{
    public ContactApiClient(HttpClient httpClient, ILogger<BaseApiClient> logger, string rootApiPath = "api/data") : base(httpClient,
    logger, rootApiPath) {}
    public async Task<ApiResponseDto<List<GetContactDto>>> SearchContactAsync(string contactNumber, CancellationToken token)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<List<GetContactDto>>>(
        $"{rootApiPath}/Search?number={contactNumber}", token);
    }
}
