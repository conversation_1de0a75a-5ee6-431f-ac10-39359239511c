﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

[PrimaryKey("ItemNumber", "StartingDate", "UnitOfMeasureCode")]
[Table("SalesPrice")]
public partial class SalesPrice
{
    [Key]
    [StringLength(100)]
    public string ItemNumber { get; set; } = null!;

    [Key]
    [Column(TypeName = "datetime")]
    public DateTime StartingDate { get; set; }

    [Key]
    [StringLength(50)]
    public string UnitOfMeasureCode { get; set; } = null!;

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal UnitPrice { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime EndingDate { get; set; }

    [StringLength(50)]
    public string? SourceCode { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal? Quantity { get; set; }

    [Column(TypeName = "decimal(28, 10)")]
    public decimal? QuantityPerUnitOfMeasure { get; set; }

    [StringLength(250)]
    public string? Description { get; set; }

    public int? Block { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? LastDateModified { get; set; }

    [StringLength(50)]
    public string? LoginId { get; set; }

    [ForeignKey("ItemNumber")]
    [InverseProperty("SalesPrices")]
    public virtual Item ItemNumberNavigation { get; set; } = null!;
}
