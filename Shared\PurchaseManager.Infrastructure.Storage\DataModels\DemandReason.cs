﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Table("DemandReason")]
public partial class DemandReason
{
    public int RowId { get; set; }

    [Key]
    [StringLength(100)]
    public string ReasonCode { get; set; }

    [Required]
    [StringLength(100)]
    public string ReasonName { get; set; }

    public bool Block { get; set; }

    public int Status { get; set; }

    [Required]
    [StringLength(250)]
    [Unicode(false)]
    public string CreateBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime CreateAt { get; set; }

    [StringLength(250)]
    [Unicode(false)]
    public string UpdateBy { get; set; }

    [Column(TypeName = "datetime")]
    public DateTime? UpdateAt { get; set; }

    [InverseProperty("ReasonCodeNavigation")]
    public virtual ICollection<PurchaseSuggestedPaymentLine> PurchaseSuggestedPaymentLines { get; set; } = new List<PurchaseSuggestedPaymentLine>();
}
