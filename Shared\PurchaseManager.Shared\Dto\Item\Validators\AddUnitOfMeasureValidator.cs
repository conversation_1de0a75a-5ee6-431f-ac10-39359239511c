﻿using FluentValidation;
namespace PurchaseManager.Shared.Dto.Item.Validators;

public class AddUnitOfMeasureValidator
{
    public class UnitOfMeasureDtoValidator : AbstractValidator<CreateUnitOfMeasureDto>
    {
        public UnitOfMeasureDtoValidator()
        {
            RuleFor(x => x.Code)
                .NotEmpty().WithMessage("Unit of measure code is required.");

            RuleFor(x => x.Description)
                .MaximumLength(160).WithMessage("Description cannot exceed 160 characters.");
        }
    }
}
