﻿using System.Globalization;
using Microsoft.JSInterop;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.Report.PO;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder.Services;

public class POServicesHandler
{
    private readonly IPurchaseOrderApiClient _apiClient;
    public POServicesHandler(IPurchaseOrderApiClient apiClient)
    {
        _apiClient = apiClient;
    }

    public async Task<(bool IsSuccess, string Message)> SaveHeaderAndProcessAsync(UpdatePOHeaderDto dto, POHeaderGetDto header)
    {
        var updateResult = await _apiClient.UpdateHeader(dto);
        if (!updateResult.IsSuccessStatusCode)
        {
            return (false, updateResult.Message);
        }
        
        if (!string.IsNullOrEmpty(header.PurchaserApprovalBy) && !string.IsNullOrEmpty(header.VendorApprovalBy))
        {
            var approveResult = await _apiClient.ApproveHeader(header.Number);
            if (!approveResult.IsSuccessStatusCode)
            {
                return (approveResult.IsSuccessStatusCode, approveResult.Message);
            }

            header.Status = approveResult.Result;
        }
        else
        {
            var rejectResult = await _apiClient.RejectHeader(header.Number);
            if (!rejectResult.IsSuccessStatusCode)
            {
                return (rejectResult.IsSuccessStatusCode, rejectResult.Message);
            }

            header.Status = rejectResult.Result;
        }

        if (header.Status < (int)PurchaseOrderEnum.Confirm)
        {
            var confirmResult = await _apiClient.ConfirmHeader(header.Number);
            if (!confirmResult.IsSuccessStatusCode)
            {
                return (false, confirmResult.Message);
            }

            header.Status = confirmResult.Result;
        }

        var closeResult = await _apiClient.CloseDocument(header.Number);
        return !closeResult.IsSuccessStatusCode ? (closeResult.IsSuccessStatusCode, closeResult.Message) : (true, "Success");
    }
    
    public async Task<(bool IsSuccess, string Message)> ExportDetailPo(List<POLineGetDto> poLineCalculated, POHeaderGetDto poHeader, IJSRuntime jsRuntime)
    {
        try
        {
            var headers = new List<string>
            {
                    "PO Number",
                    "Vendor No",
                    "Item No",
                    "Item Name",
                    "Unit",
                    "Quantity",
                    "Price",
                    "Amount",
                    "Discount",
                    "Vat Base Amount",
                    "Vat",
                    "Vat Amount",
                    "Amount Including Vat",
                    "Description"
                };

            foreach (var item in poLineCalculated)
            {
                item.UpdateAmount();
            }
            var dataExcel = poLineCalculated.Select(line => new PoDetailModel()
            {
                Vat = line.Vat.ToString(CultureInfo.InvariantCulture),
                ItemNo = line.ItemNumber,
                ItemName = line.ItemName,
                Unit = line.UnitOfMeasure,
                PONumber = line.DocumentNumber,
                Amount = line.Amount.ToString(CultureInfo.InvariantCulture),
                Description = line.Description,
                Price = line.UnitCost.ToString(CultureInfo.InvariantCulture),
                Quantity = line.Quantity.ToString(CultureInfo.InvariantCulture),
                VatAmount = line.VatAmount.ToString(CultureInfo.InvariantCulture),
                VendorNo = poHeader.BuyFromVendorNumber,
                VatBaseAmount = line.VatBaseAmount.ToString(CultureInfo.InvariantCulture),
                Discount = line.LineDiscountAmount.ToString(CultureInfo.InvariantCulture),
                VatIncludingAmount = line.AmountIncludingVat.ToString(CultureInfo.InvariantCulture)
            }
            ).ToList();
            var excelOpts = new ExcelExportOptions
            {
                SheetName = "PO Detail " + poHeader.OrderDate.ToString("dd-MM-yyyy"),
                AutoFilter = false
            };
            string fileData = ExcelExportExtension.ExportToExcel(headers, dataExcel, excelOpts);
            if (fileData == null)
            {
                return (false, "Cannot export file at the moment. Try again!");
            }
            var fileName = poHeader.Number + "_Detail_" + poHeader.OrderDate.ToString("dd-MM-yyyy") + "_" +
                           DateTime.Now.ToString("HH-mm-ss") + ".xlsx";
            await jsRuntime.InvokeVoidAsync("downloadFileFromBase64", fileName, fileData);
            return (true, "File exported successfully.");
        }
        catch (Exception ex)
        {
            return (false, ex.GetBaseException().Message);
        }
    }
}
