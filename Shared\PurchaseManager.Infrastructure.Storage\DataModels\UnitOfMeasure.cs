﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Table("UnitOfMeasure")]
public partial class UnitOfMeasure
{
    [Key]
    [StringLength(100)]
    public string Code { get; set; } = null!;

    [StringLength(200)]
    public string Description { get; set; } = null!;

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }

    public int? Status { get; set; }

    [InverseProperty("CodeNavigation")]
    public virtual ICollection<ItemUnitOfMeasure> ItemUnitOfMeasures { get; set; } = new List<ItemUnitOfMeasure>();
}
