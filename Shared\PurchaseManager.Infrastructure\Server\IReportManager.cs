using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Models.Report;
using PurchaseManager.Shared.Models.Report.PO;

namespace PurchaseManager.Infrastructure.Server;

public interface IReportManager
{
    public ApiResponse GetPOReport(PoReportRequest request);
    public Task<ApiResponse> GetVendorReport(PoReportParams reportParams, string module, VendorReportFields fields);
    public Task<ApiResponse> GetReportVendorAccount();
}
