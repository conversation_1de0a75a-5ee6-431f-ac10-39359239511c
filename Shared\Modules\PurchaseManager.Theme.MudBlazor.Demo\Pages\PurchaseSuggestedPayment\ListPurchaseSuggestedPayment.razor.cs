﻿using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Theme.Material.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseSuggestedPayment
{
    public partial class ListPSPBase : ItemsTableBase<DetailPurchaseSuggestedPaymentHeaderDto>
    {
        protected bool isLoading { get; set; }
        protected override void OnInitialized()
        {
            from = "get-header";
            base.OnInitialized();
        }
        protected string DisplayStatus(int status)
        {
            var finalStatus = "Opened";
            switch (status)
            {
                case (int)PurchaseOrderEnum.Approve:
                {
                    finalStatus = "Finished";
                    break;
                }
                case (int)PurchaseOrderEnum.PartiallyReceived:
                {
                    finalStatus = "Partially Received";
                    break;
                }
                case (int)PurchaseOrderEnum.Completed:
                {
                    finalStatus = "Received";
                    break;
                }
            }
            return finalStatus;
        }
    }
}
