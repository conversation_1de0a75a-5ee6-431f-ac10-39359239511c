using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Controllers;

[Route("api/data/")]
[ApiController]
public class AccountHrmController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly IAccountManager _accountManager;
    public AccountHrmController(ApplicationDbContext context, IAccountManager accountManager)
    {
        _context = context;
        _accountManager = accountManager;
    }
    [HttpPost("employee-login")]
    public async Task<ApiResponse> EmployeeLoginOrRegister([FromBody] string employeeCode)
    {
        try
        {
            var checkEmployee = await _context.Employees.FirstOrDefaultAsync(x => x.Code == employeeCode);
            var checkEmployeeAccount = await _context.Users.FirstOrDefaultAsync(x => x.UserName == employeeCode);
            var input = new LoginInputModel
            {
                UserName = employeeCode, Password = string.Empty, RememberMe = true
            };
            if (checkEmployee != null && checkEmployeeAccount != null)
            {
                return await _accountManager.Login(input);
            }
            // Register
            if (checkEmployee == null)
            {
                // If accounts not exists, create new account
                var newEmployee = new Employee
                {
                    Code = employeeCode,
                    FirstName = employeeCode,
                    Name = employeeCode,
                    Mail = employeeCode + "@trungsonpharma.com",
                    Address = "TS",
                    CreatedBy = "System",
                    CreatedAt = DateTime.Now,
                };
                await _context.Employees.AddAsync(newEmployee);
                await _context.SaveChangesAsync();
            }
            var createEmployee = new RegisterViewModel
            {
                UserName = employeeCode, Password = "123456", PasswordConfirm = "123456", Email = employeeCode + "@trungsonpharma.com"
            };
            await _accountManager.Register(createEmployee);
            // Auto accept new employee
            await _accountManager.ConfirmEmail(createEmployee.Email);
            var loginModel = new LoginInputModel
            {
                UserName = createEmployee.UserName, Password = createEmployee.Password, RememberMe = true
            };
            return await _accountManager.Login(loginModel);
            // Login
            // var userNameByEmployeeCode = checkEmployee.Code;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }
}
