using System.Runtime.Serialization;

namespace PurchaseManager.Shared.Dto;

[DataContract]
public class PagedResultDto<T>
{
    [DataMember]
    public List<T> Data { get; set; }
    [DataMember]
    public int CurrentPage { get; set; }
    [DataMember]
    public int PageSize { get; set; }
    [DataMember]
    public int RowCount { get; set; }
    [DataMember]
    public int PageCount { get; set; }

}
[DataContract]
public class PagedResultDto : PagedResultDto<object>
{
}
