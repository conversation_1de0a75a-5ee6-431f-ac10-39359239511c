using Microsoft.AspNetCore.Components;

using PurchaseManager.Shared.Dto.Email;
using PurchaseManager.Theme.Material.Shared.Components;

namespace PurchaseManager.Theme.Material.Admin.Pages.Admin.Settings
{
    public partial class ListEmailPage : ItemsTableBase<DetailEmailDto>
    {
        // this page display email in queue 
        [Inject] protected NavigationManager navigation { get; set; }
        protected bool isLoading { get; set; }

        protected override void OnInitialized()
        {
            from = "GetEmails";
            base.OnInitialized();
        }
    }
}