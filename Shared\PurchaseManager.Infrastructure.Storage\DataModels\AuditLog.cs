﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Table("AuditLogs")]
public class AuditLog
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int Id { get; set; }
    [StringLength(50)]
    public string UserName { get; set; }
    [StringLength(50)]
    public string UserId { get; set; }
    [StringLength(50)]
    public required string EntityName { get; set; }
    [StringLength(50)]
    public required string Action { get; set; }
    public DateTime Timestamp { get; set; }
    [StringLength(500)]
    public required string Changes { get; set; }
}
