
update iuom
set iuom.Block = uto.Block,
iuom.QuantityPerUnitOfMeasure = uto.[Qty_ per Unit of Measure],
iuom.[Last Date Modified] = uto.[Last Date Modified]
from [192.168.10.2].PurchaseManager.dbo.ItemUnitOfMeasure iuom ,
(
	select iuom.* 
	from Item ix, [Item Unit of Measure] iuom WHERE 
	convert(date, ix.[Last Date Modified] ) ='2024-07-18'
	and ix.No_  =  iuom.[Item No_]
)uto
where iuom.ItemNumber = uto.[Item No_]
and iuom.Code = uto.Code

update sp
set sp.Block = uto.Block,
sp.UnitPrice = uto.[Unit Price],
sp.EndingDate = uto.[Ending Date],
sp.[LastDateModified] = uto.[Last Date Modified]
from [192.168.10.2].PurchaseManager.dbo.SalesPrice sp ,
(
	select iuom.* 
	from Item ix, [Sales Price] iuom WHERE 
	convert(date, ix.[Last Date Modified] ) ='2024-07-18'
	and ix.No_  =  iuom.[Item No_]
)uto
where sp.ItemNumber = uto.[Item No_]
and sp.UnitOfMeasureCode = uto.[Unit of Measure Code]
