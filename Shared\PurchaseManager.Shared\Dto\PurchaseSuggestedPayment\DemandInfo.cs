namespace PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;

public class DemandInfo
{
    public int Index { get; set; }
    public int ItemNo { get; set; }
    public string StoreId { get; set; }
    public int QtyDemand { get; set; }
    public int QtyRequest { get; set; }
    public string UnitOfMeasure { get; set; }
    public int Quantity { get; set; } // asm want to order with specifically reason
    public int? DemandReasonId { get; set; }
    public string? Tags { get; set; }
}
