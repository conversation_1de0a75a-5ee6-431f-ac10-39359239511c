﻿@inherits ProfilePage
@page "/account/profile"
@attribute [Authorize]

@layout MainLayout

@if (userViewModel != null)
{
        <PageTitle>@L["User Profile"]</PageTitle>
        @if (userViewModel.HasPassword)
        {
            <MudButton StartIcon="@Icons.Material.Filled.RotateRight" OnClick="@(() => OpenUpdatePasswordDialog())" Style="margin-left: 10px">@L["Change Password"]</MudButton>
        }
        @if (userViewModel.HasAuthenticator)
        {
            <MudButton StartIcon="@Icons.Material.Filled.RotateRight" OnClick="@(() => DisableAuthenticator())" Style="margin-left: 10px">@L["ResetAuthenticator"]</MudButton>
        }

        <EditForm id="mainForm" Model="@userViewModel" OnValidSubmit="@UpdateUser">
            <FluentValidationValidator />
            <MudValidationSummary />
            <MudTextField @bind-Value="@userViewModel.UserName" Label=@L["UserName"] AdornmentIcon="@Icons.Material.Filled.Person" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"] ReadOnly="true"></MudTextField>

            <MudTextField @bind-Value="@userViewModel.Email" Label="Email" AdornmentIcon="@Icons.Material.Outlined.Mail" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"]></MudTextField>

            <MudTextField @bind-Value="@userViewModel.FirstName" Label=@L["FirstName"] FullWidth="true"></MudTextField>

            <MudTextField @bind-Value="@userViewModel.LastName" Label=@L["LastName"] FullWidth="true"></MudTextField>

            <strong>@L["Roles"]</strong><br />
            <MudChipSet T="string">
                @foreach (var role in userViewModel.Roles)
                {
                    <MudChip T="string" Text="@role"></MudChip>
                }
            </MudChipSet>

            <MudButton ButtonType="ButtonType.Submit" form="mainForm" Variant="Variant.Filled" Color="Color.Primary">@L["Save"]</MudButton>
            <MudButton Href="/" form="mainForm" Variant="Variant.Filled" Color="Color.Secondary">@L["Cancel"]</MudButton>
        </EditForm>
        @if (userViewModel.TwoFactorEnabled)
        {
            <MudSwitch T="bool" @bind-Value="@TwoFactorEnabled" Label=@L["TwoFactorAuthentication"] Color="Color.Primary" />

            @if (userViewModel.BrowserRemembered)
            {
                <MudSwitch T="bool" @bind-Value="@BrowserRemembered" Label=@L["BrowserRemembered"] Color="Color.Primary" />
            }
            if (userViewModel.RecoveryCodes != null)
            {
                <MudChipSet T="string">
                    @foreach (var rcode in userViewModel.RecoveryCodes)
                    {
                        <MudChip T="string" Text="@rcode"></MudChip>
                    }
                </MudChipSet>
            }
            else
            {
                <span>@L["RecoveryCodesLeft"]</span> @userViewModel.CountRecoveryCodes
            }
        }
        else
        {
            <h3>@L["TwoFactorAuthentication"]</h3>
            @L["EnableAuthenticatorInstructions", userViewModel.SharedKey, $"https://chart.googleapis.com/chart?chs=200x200&chld=M|0&cht=qr&chl={userViewModel.AuthenticatorUri}"].ToMarkup()
            <MudTextField @bind-Value="@authenticatorVerificationCodeViewModel.Code" Label=@L["Code"] FullWidth="true"></MudTextField>
            <MudButton OnClick="@EnableAuthenticator">@L["VerifyCode"]</MudButton>
        }

    <MudDialog @bind-Visible="@updatePasswordDialogOpen">
        <TitleContent>
            <MudText Typo="Typo.h6">
                <MudIcon Icon="@Icons.Material.Filled.Lock" Class="mr-3 mb-n1" />
                Password Update for @userViewModel.UserName
            </MudText>
        </TitleContent>
        <DialogContent>
            <EditForm Model="@updatePasswordViewModel" OnValidSubmit="@UpdatePassword">
                <FluentValidationValidator />
                <MudValidationSummary />
                <MudTextField @bind-Value="@updatePasswordViewModel.CurrentPassword" Label=@L["CurrentPassword"] AdornmentIcon="@Icons.Material.Outlined.Lock" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"] InputType="InputType.Password"></MudTextField>

                <MudTextField @bind-Value="@updatePasswordViewModel.NewPassword" Label=@L["NewPassword"] AdornmentIcon="@Icons.Material.Outlined.Lock" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"] InputType="InputType.Password"></MudTextField>

                <MudTextField @bind-Value="@updatePasswordViewModel.NewPasswordConfirm" Label=@L["Password Confirmation"] AdornmentIcon="@Icons.Material.Outlined.Lock" Adornment="Adornment.End" FullWidth="true" Required="true" RequiredError=@L["Required"] InputType="InputType.Password"></MudTextField>

            </EditForm>
        </DialogContent>
        <DialogActions>
            <MudButton OnClick="@(e => { updatePasswordDialogOpen = false; })">@L["Cancel"]</MudButton>
            <MudButton OnClick="@UpdatePassword" Variant="Variant.Filled" Color="Color.Primary">@L["Update Password"]</MudButton>
        </DialogActions>
    </MudDialog>

}

@code {

}
