﻿@inherits ListEmailTemplatePage
@layout AdminLayout

@page "/admin/settings/email/email-templates"
@if (isLoading)
{
    <LoadingBackground />
}
else
{
    <MudCard Elevation="0" Class="mt-2" Outlined>
        <MudCardHeader Class="">
            <CardHeaderContent>
                <div>
                    <MudText Typo="Typo.h5">Email Template</MudText>
                    <MudText Typo="Typo.caption">List Email Template</MudText>
                </div>
            </CardHeaderContent>
            <CardHeaderActions>
                <MudButton StartIcon="@Icons.Material.Filled.Add"
                           OnClick="@(() => {navigation.NavigateTo("/admin/settings/email/email-template/create");})"
                           ButtonType="ButtonType.Submit"
                           Variant="Variant.Filled" Color="Color.Primary" Class="">
                    Add
                </MudButton>
                @*<MudIconButton Icon="@Icons.Material.Filled.Refresh" OnClick="@(() => Reload())" Size="Size.Medium" Class="ma-2" />*@
            </CardHeaderActions>
        </MudCardHeader>
        <MudCardContent>
            <MudTable ServerData="@(new Func<TableState, CancellationToken, Task<TableData<PurchaseManager.Shared.Dto.Email.DetailEmailTemplate>>>(ServerReload))"
                      Striped="false"
                      Bordered="false"
                      Dense="true"
                      Hover="true"
                      Elevation="0"
                      Outlined="true"
                      FixedHeader="true"
                      LoadingProgressColor="Color.Primary">
                <HeaderContent>
                    <MudTh>Template Name</MudTh>
                    <MudTh>Actions</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd>@context.TemplateName</MudTd>
                    <MudTd>
                        <MudIconButton Icon="@Icons.Material.Filled.Preview"
                                       OnClick="@(() => {navigation.NavigateTo("/admin/settings/email/email-template/edit/"+ context.EmailTemplatesId);})"
                                       Color="Color.Surface" />
                    </MudTd>
                </RowTemplate>
                <PagerContent>
                    <MudTablePager RowsPerPageString=@L["Rows per page"] />
                </PagerContent>
            </MudTable>
        </MudCardContent>
    </MudCard>
}
