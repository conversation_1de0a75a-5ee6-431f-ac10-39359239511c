﻿@page "/purchase-price-upload"
@using PurchaseManager.Shared.Dto.PurchasePrices
<MudText Typo="Typo.h4" Class="my-4" Align="Align.Center">@L["Upload Purchase Price"]</MudText>
@if (!IsFileRead)
{
    @if (IsFileLoading)
    {
        <MudAlert Severity="Severity.Info" ContentAlignment="HorizontalAlignment.Left">Progressing...</MudAlert>
    }
    else
    {
        <MudAlert Severity="Severity.Info" ContentAlignment="HorizontalAlignment.Left">
            <MudStack Row Spacing="5" Justify="Justify.SpaceAround" AlignItems="AlignItems.Stretch">
                <MudText Class="mud-underline">@L["Upload purchase price SKUs Vendor."]</MudText>
                <MudText>
                    @L["Download sample file "]
                    <MudLink Underline="Underline.Always" Href="/files/Import_Purchase_Price_SampleData.xlsx">
                        @L["here"]
                    </MudLink>
                </MudText>
            </MudStack>
        </MudAlert>
        <MudFileUpload T="IBrowserFile" Class="mt-4" Accept=".xlsx" MaximumFileCount="1" FilesChanged="UploadFiles">
            <ActivatorContent>
                <MudButton Style="width: 100%" Variant="Variant.Filled" Color="Color.Primary"
                           StartIcon="@Icons.Material.Filled.CloudUpload">
                    @L["Upload"]
                </MudButton>
            </ActivatorContent>
        </MudFileUpload>
    }
}
else
{
    if (ResponseWithDetailError.Count > 0)
    {
        <MudButton Variant="Variant.Filled" Color="Color.Error" OnClick="ClearAllDataInList" Disabled="@IsCreatePO"
                   StartIcon="@Icons.Material.Filled.Clear">
            @L["Clear"]
        </MudButton>
        <MudStack Wrap="Wrap.Wrap" Row>
            @foreach (var group in ResponseWithDetailError.GroupBy(x => x.Description))
            {
                <MudText Typo="Typo.subtitle2" Class="mt-2 mb-1">
                    @group.Key
                </MudText>

                @foreach (var error in group)
                {
                    <MudChip T="String" Size="Size.Small" Variant="Variant.Outlined" Color="Color.Error" Class="me-1 mb-1">
                        @error.Field
                    </MudChip>
                }
            }
        </MudStack>
    }
    else
    {
        <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
            <MudButton Variant="Variant.Filled" Color="Color.Error" OnClick="ClearAllDataInList" Disabled="@IsCreatePO"
                       StartIcon="@Icons.Material.Filled.Clear">
                @L["Clear"]
            </MudButton>
            @if (ListCheckPaymentDayResponse.All(x => x.DuplicateCount == 0))
            {
                <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="CheckPaymentDayByVendorNumber"
                           Disabled="@(IsCreatePO)" StartIcon="@Icons.Material.Filled.Add">
                    @L["Create Purchase Price Data"]
                </MudButton>
            }
            @if (ListCheckPaymentDayResponse.Any(x => x.DuplicateCount != 0))
            {
                <MudAlert Severity="Severity.Warning" ContentAlignment="HorizontalAlignment.Left">
                    @L["Duplicate data. Please check again"]
                </MudAlert>
            }
        </MudStack>
    }

}
@if (IsCreatePO)
{
    <MudStack Row Justify="Justify.Center" class="mt-4">
        <MudOverlay @bind-Visible="IsCreatePO" LightBackground ZIndex="9999">
            <MudText Typo="Typo.h6">@L["Creating PO. Pleases wait"]</MudText>
            <MudProgressLinear Color="Color.Info" Indeterminate="true"/>
        </MudOverlay>
    </MudStack>
}
@if (PurchasePriceHeaderCheckDtos.Any())
{
    <MudStack Class="my-2">
        <MudTable T="CreatePurchasePriceDto" Items="@PurchasePriceHeaderCheckDtos"
                  FixedHeader="true" FixedFooter="true" Hover="true" Breakpoint="Breakpoint.Sm" LoadingProgressColor="Color.Info">
            <HeaderContent>
                <MudTh>
                    <MudTableSortLabel SortLabel="VendorNumber" T="CreatePurchasePriceDto">Vendor Number</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="ItemNumber" T="CreatePurchasePriceDto">Item Number</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="PurchasingUnit" T="CreatePurchasePriceDto">PurchasingUnit</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="Price" T="CreatePurchasePriceDto">Price</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="VAT" T="CreatePurchasePriceDto">VAT</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="DiscountBySkus" T="CreatePurchasePriceDto">DiscountBySkus</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="GroupProduct" T="CreatePurchasePriceDto">GroupProduct</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="FoCPromotion" T="CreatePurchasePriceDto">FoCPromotion</MudTableSortLabel>
                </MudTh>
            </HeaderContent>
            <RowTemplate Context="row">
                <MudTd DataLabel="VendorNumber">
                    <p>@row.VendorNumber</p>
                </MudTd>
                <MudTd DataLabel="ItemNumber">
                    <MudText>
                        @row.ItemNumber
                    </MudText>
                </MudTd>
                <MudTd DataLabel="PurchasingUnit">
                    <MudText>@row.PurchasingUnit</MudText>
                </MudTd>
                <MudTd DataLabel="Price">
                    <MudText>@row.Price</MudText>
                </MudTd>
                <MudTd DataLabel="VAT">
                    <MudText>@row.VAT</MudText>
                </MudTd>
                <MudTd DataLabel="DiscountBySkus">
                    <MudText>@row.DiscountBySkus</MudText>
                </MudTd>
                <MudTd DataLabel="GroupProduct">
                    <MudText>@row.GroupProduct</MudText>
                </MudTd>
                <MudTd DataLabel="FoCPromotion">
                    <MudText>@row.FoCPromotion</MudText>
                </MudTd>
            </RowTemplate>
        </MudTable>
    </MudStack>
}
<MudDialog @bind-Visible="@IsAlertDialogOpen"
           Options="@(new DialogOptions
                    {
                        MaxWidth = MaxWidth.Medium,
                        FullWidth = true
                    })">
    <TitleContent>
        <MudText Typo="Typo.h6">
            <MudIcon Icon="@Icons.Material.Filled.ReportProblem" Color="Color.Warning" Class="mr-3 mb-n1"/>
            @L["Existing SKUs + Vendor + StartDate please check again"]
        </MudText>
    </TitleContent>
    <DialogContent>
        <AlertPurchasePriceUploadFile Parameters="@ListCheckPaymentDayResponse"></AlertPurchasePriceUploadFile>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(_ => { IsAlertDialogOpen = false; })">@L["Cancel"]</MudButton>
    </DialogActions>
</MudDialog>