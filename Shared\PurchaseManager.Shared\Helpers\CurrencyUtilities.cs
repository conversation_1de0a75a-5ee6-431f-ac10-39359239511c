using System.Globalization;

namespace PurchaseManager.Shared.Helpers;

public static class CurrencyUtilities
{
    private static readonly CultureInfo VietnameseCulture = new CultureInfo("vi-VN");

    /// <summary>
    /// Định dạng số thành tiền tệ theo định dạng Việt Nam.
    /// </summary>
    /// <param name="value"><PERSON><PERSON><PERSON> trị số cần định dạng.</param>
    /// <returns>Chuỗi định dạng tiền tệ.</returns>
    public static string FormatCurrency(double value)
    {
        return string.Format(VietnameseCulture, "{0:C}", value);
    }

    /// <summary>
    /// Định dạng số thành tiền tệ theo định dạng Việt Nam với dấu chấm làm dấu phân cách hàng nghìn.
    /// </summary>
    /// <param name="value"><PERSON>i<PERSON> tr<PERSON> số cần định dạng.</param>
    /// <returns>Chuỗi định dạng tiền tệ.</returns>
    public static string FormatCurrencyCustom(double value)
    {
        // Tạo một bản sao của CultureInfo để không ảnh hưởng đến CultureInfo gốc
        var customCulture = (CultureInfo)VietnameseCulture.Clone();

        // Đổi dấu phân cách hàng nghìn và thập phân
        customCulture.NumberFormat.NumberDecimalSeparator = ",";
        customCulture.NumberFormat.NumberGroupSeparator = ".";

        return string.Format(customCulture, "{0:N0}", value);
    }
}
