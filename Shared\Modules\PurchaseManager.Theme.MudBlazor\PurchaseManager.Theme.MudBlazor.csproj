﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <RootNamespace>PurchaseManager.Theme.Material</RootNamespace>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="AutoMapper" Version="13.0.1"/>
		<PackageReference Include="ExcelDataReader" Version="3.7.0" />
		<PackageReference Include="Heron.MudCalendar" Version="2.0.0" />
        <PackageReference Include="MudBlazor" Version="7.0.0" />
        <PackageReference Include="MudBlazor.Extensions" Version="2.0.0" />
      <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.0"/>
        <PackageReference Include="System.Net.Http" Version="4.3.4"/>
        <PackageReference Include="System.Net.Http.Json" Version="8.0.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\PurchaseManager.Shared\PurchaseManager.Shared.csproj" />
        <ProjectReference Include="..\..\PurchaseManager.UI.Base\PurchaseManager.UI.Base.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="obj\Debug\net8.0\" />
        <Folder Include="wwwroot\fonts\" />
    </ItemGroup>

    <Target Name="PostBuild" AfterTargets="PostBuildEvent">
        <Copy SourceFiles="$(TargetPath)" DestinationFolder="$(SolutionDir)Server\PurchaseManager.Server\Themes\MudBlazor\" />
    </Target>
</Project>
