﻿using Microsoft.AspNetCore.Components;

using MudBlazor;

using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.Item;
namespace PurchaseManager.Theme.Material.Demo.Shared.Components;

public partial class SalesPricePage : ComponentBase
{
    [Parameter]
    public DetailSalesPriceDto SalesPrice { get; set; }
    [Parameter]
    public bool IsCreate { get; set; }
    [Parameter]
    public List<UnitOfMeasureDto> ListUnitOfMeasureSelected { get; set; }

    public UnitOfMeasureDto unitOfMeasure { get; set; }

    public DateRange dateRange { get; set; } = new DateRange();
    public Func<UnitOfMeasureDto, string> converter = p => p?.Description;


    protected override void OnInitialized()
    {
        dateRange.Start = SalesPrice.StartingDate.Value;
        dateRange.End = SalesPrice.EndingDate;
        unitOfMeasure = ListUnitOfMeasureSelected.FirstOrDefault();
        SalesPrice.UnitOfMeasureCode = unitOfMeasure.Code;
        base.OnInitialized();
    }
    protected void OnPickerClose()
    {
        SalesPrice.UnitOfMeasureCode = unitOfMeasure.Code;
        SalesPrice.StartingDate = dateRange.Start;
        SalesPrice.EndingDate = (DateTime)dateRange.End;
    }
}
