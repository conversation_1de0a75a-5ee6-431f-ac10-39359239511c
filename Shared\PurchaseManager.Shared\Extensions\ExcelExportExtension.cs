using System.Threading.Tasks;
using ClosedXML.Excel;
namespace PurchaseManager.Shared.Extensions;
public class ExcelExportOptions
{
    public string SheetName { get; set; } = "Sheet1";
    public bool AutoFilter { get; set; } = true;
}
public static class ExcelExportExtension
{

    public static string ExportToExcel<T>(List<string> headers, List<T> dataList, ExcelExportOptions? options = null)
    {
        options ??= new ExcelExportOptions(); // Nếu không truyền options thì dùng mặc định
        using (var workbook = new XLWorkbook())
        {
            var worksheet = workbook.Worksheets.Add(options.SheetName);
            // Thêm header
            for (int i = 0; i < headers.Count; i++)
            {
                worksheet.Cell(1, i + 1).Value = headers[i];
                worksheet.Cell(1, i + 1).Style
                                            .Font.SetFontSize(13)
                                            .Font.SetFontColor(XLColor.WhiteSmoke)
                                            .Fill.SetBackgroundColor(XLColor.Green)
                                            .Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
            }
            // Thêm dữ liệu vào các hàng
            for (int row = 0; row < dataList.Count; row++)
            {
                var data = dataList[row];
                var properties = typeof(T).GetProperties();
                for (int col = 0; col < properties.Length && col < headers.Count; col++)
                {
                    var value = properties[col].GetValue(data);
                    worksheet.Cell(row + 2, col + 1).Value = value is null ? "" : value.ToString();
                }
            }
            // Áp dụng tùy chọn
            if (options.AutoFilter)
            {
                worksheet.RangeUsed().SetAutoFilter();
            }
            worksheet.Columns().AdjustToContents();
            // Lưu vào MemoryStream
            using (var stream = new MemoryStream())
            {
                workbook.SaveAs(stream);
                return Convert.ToBase64String(stream.ToArray());
            }
        }
    }
    /// <summary>
    /// Reads an Excel file and maps its rows to a generic type T.
    /// </summary>
    /// <typeparam name="T">The type to map the rows to.</typeparam>
    /// <param name="stream">The stream containing the Excel file.</param>
    /// <param name="mapFunction">A function to map row data to type T.</param>
    /// <returns>A list of T objects.</returns>
    public static async Task<List<T>> ReadExcel<T>(Stream stream, Func<IDictionary<string, string>, T> mapFunction)
    {
        var result = new List<T>();

        // Ensure the stream is read asynchronously
        using (var memoryStream = new MemoryStream())
        {
            await stream.CopyToAsync(memoryStream); // Copy the stream to memory asynchronously
            memoryStream.Position = 0; // Reset the stream position

            using (var workbook = new XLWorkbook(memoryStream))
            {
                var worksheet = workbook.Worksheet(1); // Read the first worksheet
                var headers = new List<string>();
                var firstRow = worksheet.FirstRowUsed();

                // Extract headers from the first row
                foreach (var cell in firstRow.Cells())
                {
                    headers.Add(cell.Value.ToString());
                }

                // Iterate through rows dynamically
                foreach (var row in worksheet.RowsUsed().Skip(1)) // Skip header row
                {
                    var rowData = new Dictionary<string, string>();

                    for (int i = 0; i < headers.Count; i++)
                    {
                        var cellValue = row.Cell(i + 1).Value.ToString();
                        rowData[headers[i]] = cellValue;
                    }

                    // Map the row data to the generic type using the provided function
                    result.Add(mapFunction(rowData));
                }
            }
        }

        return result;
    }
}
