﻿using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Dto.Response;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.PurchasePrice;
namespace PurchaseManager.Shared.Services;

public class PurchasePriceApiClient : BaseApiClient, IPurchasePriceApiClient
{

    public PurchasePriceApiClient(HttpClient httpClient, ILogger<BaseApiClient> logger, string rootApiPath = "api/data/") : base(httpClient, logger, rootApiPath)
    {
    }
    public async Task<ApiResponseDto> CreatePurchasePriceAsync(CreatePurchasePriceDto createPurchasePriceDto)
    {
        return await httpClient.PostAsJsonAsync("/api/PurchasePrice/create", createPurchasePriceDto)
            .ContinueWith(t => t.Result.Content.ReadFromJsonAsync<ApiResponseDto>()).Unwrap();
    }

    public async Task<ApiResponseDto> DeletePurchasePriceByPriceNumberAsync(string priceNumber)
    {
        return await httpClient.DeleteAsync($"/api/PurchasePrice/delete/{priceNumber.Trim()}")
            .ContinueWith(t => t.Result.Content.ReadFromJsonAsync<ApiResponseDto>()).Unwrap();
    }

    public async Task<ApiResponseDto<List<GetPurchasePriceDto>>> GetPurchasePriceAsync(string itemNumber, string vendorNumber)
        => await httpClient.GetFromJsonAsync<ApiResponseDto<List<GetPurchasePriceDto>>>(
        $"/api/PurchasePrice/get-price-and-unit/{itemNumber.Trim()}/{vendorNumber.Trim()}");

    public async Task<ApiResponseDto<PagedResultDto<GetPurchasePriceDto>>> GetPurchasePriceByFilterAsync(PurchasePriceFilter filter)
        => await httpClient.GetFromJsonAsync<ApiResponseDto<PagedResultDto<GetPurchasePriceDto>>>("/api/PurchasePrice/gets?" +
    filter.ToQuery());

    public async Task<ApiResponseDto<GetPurchasePriceDto>> GetPurchasePriceByPriceNumberAsync(string priceNumber)
        => await httpClient.GetFromJsonAsync<ApiResponseDto<GetPurchasePriceDto>>($"/api/PurchasePrice/get/{priceNumber.Trim()}");

    public async Task<ApiResponseDto> UpdatePurchasePriceAsync(UpdatePurchasePriceDto updatePurchasePriceDto)
    {
        return await httpClient.PutAsJsonAsync("/api/PurchasePrice/update", updatePurchasePriceDto)
            .ContinueWith(t => t.Result.Content.ReadFromJsonAsync<ApiResponseDto>()).Unwrap();
    }

    public async Task<ApiResponseDto<List<ResponseWithDetailError>>> ValidateDataAsync(List<CreatePurchasePriceDto> fileUploaded)
    {
        return await httpClient.PostAsJsonAsync("/api/PurchasePrice/validate-data", fileUploaded)
            .ContinueWith(t => t.Result.Content.ReadFromJsonAsync<ApiResponseDto<List<ResponseWithDetailError>>>()).Unwrap();
    }

    public async Task<ApiResponseDto> ImportFileDataAsync(List<CreatePurchasePriceDto> fileUploaded)
    {
        return await httpClient.PostAsJsonAsync("/api/PurchasePrice/import-file-data", fileUploaded)
            .ContinueWith(t => t.Result.Content.ReadFromJsonAsync<ApiResponseDto>()).Unwrap();
    }
}
