﻿namespace PurchaseManager.Shared.Dto.QueueGenPO;

public class CreateQueueGenPoDemandDto
{
    public string ItemNumber { get; set; } = null!;
    public int Quantity { get; set; }
    public string PurchaseUnitOfMeasure { get; set; } = null!;
    public string VendorNumber { get; set; } = string.Empty;
    public string BrandCode { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal PriceB4VAT { get; set; }
    public decimal VAT { get; set; }
    /// <summary>
    /// 0: Order
    /// 1: Consigned
    /// 2: Promotion
    /// | reference: PurchaseManager.Constants.DocNoOccurrenceEnum
    /// </summary>
    /// <value></value>
    public int DocumentType { get; set; }
}
