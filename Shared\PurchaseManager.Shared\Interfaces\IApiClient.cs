using System.Linq.Expressions;
using Breeze.Sharp;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Contact;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.Email;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Dto.QueueGenPO;
using PurchaseManager.Shared.Dto.Response;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Models.Contact;
using PurchaseManager.Shared.Models.Email;
using PurchaseManager.Shared.Models.Item;
using PurchaseManager.Shared.Models.PO;
using PurchaseManager.Shared.Models.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Models.Report.PO;
namespace PurchaseManager.Shared.Interfaces;

public interface IApiClient : IBaseApiClient
{
    Task<UserProfile> GetUserProfile();
    Task<QueryResult<TenantSetting>> GetTenantSettings();
    Task<QueryResult<ApplicationUser>> GetUsers(Expression<Func<ApplicationUser, bool>> predicate = null, int? take = null,
        int? skip = null);
    Task<QueryResult<ApplicationRole>> GetRoles(Expression<Func<ApplicationRole, bool>> predicate = null, int? take = null,
        int? skip = null);
    Task<QueryResult<DbLog>> GetLogs(Expression<Func<DbLog, bool>> predicate = null, int? take = null, int? skip = null);
    Task<QueryResult<ApiLogItem>> GetApiLogs(Expression<Func<ApiLogItem, bool>> predicate = null, int? take = null, int? skip = null);
    Task<QueryResult<Todo>> GetToDos(ToDoFilter filter, int? take = null, int? skip = null);
    Task<QueryResult<ApplicationUser>> GetTodoCreators(ToDoFilter filter);
    Task<QueryResult<ApplicationUser>> GetTodoEditors(ToDoFilter filter);
    public Task<ApiResponseDto<DetailItemDto>> GetItemByNumber(string itemNumber);
    public Task<QueryResult<UnitOfMeasureDto>> GetAllUnitOfMeasure(Expression<Func<UnitOfMeasureDto, bool>> predicate = null,
        UnitFilter filter = null, int? take = null, int? skip = null);
    public Task<ApiResponseDto> CreateUOM(List<CreateUnitOfMeasureDto> createUnitOfMeasureDto);
    public Task<ApiResponseDto> UpdateUOM(UpdateUnitOfMeasureDto updateUnitOfMeasureDto, string code);
    // Email API
    Task<ApiResponseDto> SendTestEmail(EmailRequestDto email);
    public Task<QueryResult<DetailEmailTemplate>> GetTemplates(EmailTemplateFilter filter = null);

    Task<ApiResponseDto> SendEmail(EmailRequestDto email);
    Task<ApiResponseDto<DetailEmailTemplate>> GetEmailTemplateById(int emailTemplateId);
    public Task<ApiResponseDto> CreateEmailTemplate(CreateEmailTemplateDto createEmailTemplateDto);
    public Task<ApiResponseDto> UpdateEmailTemplate(UpdateEmailTemplateDto dto, int templateId);

    public Task<ApiResponseDto> UpdateItem(UpdateItemDto item, string number);
    public Task<ApiResponseDto> CreateIUOM(List<CreateItemUnitOfMeasureDto> itemUnitOfMeasure);
    public Task<ApiResponseDto> UpdateIUOM(string itemNumber, string unitOfMeasureCode, UpdateItemUnitOfMeasureDto itemUnitOfMeasure);
    public Task<ApiResponseDto> CreateSalePrice(List<CreateSalesPriceDto> salesPrices);
    public Task<ApiResponseDto> CreateItem(CreateItemDto createItemDto);
    public Task<ApiResponseDto> CreatePO(List<DetailPoDto> detailPos);
    public Task<ApiResponseDto> UpdateSalePrice(string itemNumber, DateTime startDate, string unitOfMeasure,
        UpdateSalesPriceDto salesPrice);
    //---------------------------------------------------------------------------------------------------------------------------------------------
    Task<QueryResult<NumberSeries>> GetNumberSeries(int? take = null, int? skip = null);
    Task<QueryResult<NumberSeriesLine>> GetNumberSeriesLine(int? take = null, int? skip = null);
    Task<QueryResult<string>> GetNumberCode();
    Task<ApiResponseDto> UploadFiles(MultipartFormDataContent content);
    Task<ApiResponseDto> CreateDemand(List<CreateQueueGenPoDemandDto> detailPoDtos);
    Task<HttpResponseMessage> DeleteFile(string fileId);
    public Task<QueryResult<Schedule>> GetSchedules(ScheduleFilter filter);
    public Task<QueryResult<ColorDto>> GetColors();
    public Task<ApiResponseDto> InsertOrUpdateColors(List<InsertOrUpdateItemColorsDto> colorsDtos);
    public Task<ApiResponseDto<DetailPurchaseSuggestedPaymentHeaderDto>> GetPurchaseSuggestedPaymentHeaderByDocumentNo(
        string documentNumber, PurchaseSuggestedPaymentHeaderFilter filter);
    public Task<ApiResponseDto> UpdatePurchaseSuggestedPaymentLine(
        UpdatePurchaseSuggestedPaymentLineDto updatePurchaseSuggestedPaymentLineDto);
    public Task<ApiResponseDto> OpenPurchaseSuggestedPayment(string documentNumber);
    public Task<ApiResponseDto> ClosePurchaseSuggestedPayment(string documentNumber);
    public Task<ApiResponseDto> ApprovePurchaseSuggestedPayment(string documentNumber);

    public Task<ApiResponseDto> InsertDemandReason(CreateDemandReasonDto item);
    public Task<ApiResponseDto<List<DetailDemandReasonDto>>> GetAllDemandReason();
    public Task<ApiResponseDto> UpdateDemandReason(UpdateDemandReasonDto item);
    public Task<ApiResponseDto> BlockDemandReason(string id);
    Task<ApiResponseDto> CreatePOFromPsp(List<ApproveDocumentDto> dtos);
    public Task<QueryResult<GetContactDto>> GetAllContacts(ContactFilter filter);
    public Task<ApiResponseDto> CreateContact(CreateContactDto dto);
    public Task<ApiResponseDto> UpdateContact(UpdateContactDto dto);
    public Task<ApiResponseDto> BlockOrUnblockContact(string number);
    public Task<ApiResponseDto<List<GetVendorDto>>> SearchVendorByNameOrNumber(string name, string number, CancellationToken token);
    public Task<ApiResponseDto> GetAllPoReportByParams(PoReportRequest request);
    public Task<ApiResponseDto<List<CheckPaymentDayResponse>>> CheckPaymentDayByVendorNumber(List<string> listVendorNumber);
    public Task<ApiResponseDto<List<CheckPaymentDayResponse>>> CheckPaymentDayByItemNumber(List<string> listItemNumber);
    public Task<ApiResponseDto<List<CheckPaymentDayResponse>>> CheckPaymentDayBySuggestDocumentNumber(
        List<string> listSuggestDocumentNumber);
    public Task<ApiResponseDto> SendEmailDetailPOToVendor(EmailInfoInPODetailDto emailInfoInPODetailDto);
    public Task<ApiResponseDto<List<string>>> GetPurchaseSuggestPaymentTags();
    public Task<ApiResponseDto<List<InvalidItem>>> ValidateItemsInDemandV2(List<DemandItemInfo> listDemandV2);
    public Task<ApiResponseDto> CreateAccountVendor(RegisterVendorViewModel dto);
    public Task<ApiResponseDto> CheckTaxCode(string taxCode);
    public Task<ApiResponseDto<PagedResult<UserVendorViewModel>>> GetsAccountVendor(AccountVendorFilter filter);
    public Task<ApiResponseDto> ActiveAccountVendor(string useName);
    public Task<ApiResponseDto> BlockAccountVendor(string useName);
    public Task<ApiResponseDto> ResetVendorPassword(string useName);
    public Task<ApiResponseDto> GetReportVendorAccount();
    // vendor item
    public Task<QueryResult<DetailVendorItemDto>> GetAllVendorItems(VendorItemFilter filter);
    public Task<ApiResponseDto> CreateVendorItem(CreateVendorItemDto item);
    public Task<ApiResponseDto> UpdateVendorItem(UpdateVendorItemDto item);
    public Task<ApiResponseDto<DetailVendorItemDto>> ToggleVendorItemStatus(int rowId);// block => unblock | unblock => block
    public Task<ApiResponseDto> CreatePurchaseSuggestedPaymentV2(MultipartFormDataContent content);
    public Task<ApiResponseDto<LoginResponseModel>> LoginEmployee(string employeeCode);
    Task<ApiResponseDto<List<ResponseWithDetailError>>> ValidateFileData(List<CreatePurchasePriceDto> dtos);
    Task<ApiResponseDto> CreateMultiplePurchasePriceHeaderViaFile(List<CreatePurchasePriceDto> dtos);
}
