﻿using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Validators;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace PurchaseManager.Shared.Models.Account.Validators
{
    public class RegisterInputModelValidator<T> : LocalizedAbstractValidator<T> where T : RegisterVendorViewModel
    {
        public RegisterInputModelValidator(IStringLocalizer<Global> l) : base(l)
        {
            RuleFor(p => p.UserName)
                .NotEmpty()
                .Matches(@"^[^\s]+$").WithMessage(x => L["SpacesNotPermitted"])
                .Length(2, 64).WithName(L["UserName"]);

            RuleFor(p => p.Password).Password(L);
        }
    }
}
