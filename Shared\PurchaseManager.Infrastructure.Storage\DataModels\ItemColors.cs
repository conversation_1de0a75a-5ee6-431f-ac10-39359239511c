﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace PurchaseManager.Infrastructure.Storage.DataModels;
[Table("ItemColors")]
public partial class ItemColors
{
    [Key]
    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }
    
    [StringLength(50)]
    public string ColorCode { get; set; } = null!;
    
    [StringLength(50)]
    public string ItemNumber { get; set; } = null!;
    
    public bool Block { get; set; }
    
    [ForeignKey("ItemNumber")]
    [InverseProperty("ItemColors")]
    public virtual Item ItemNumberNavigation { get; set; } = null!;
    
    [ForeignKey("ColorCode")]
    [InverseProperty("ItemColors")]
    public virtual Colors ColorCodeNavigation { get; set; } = null!;
}
