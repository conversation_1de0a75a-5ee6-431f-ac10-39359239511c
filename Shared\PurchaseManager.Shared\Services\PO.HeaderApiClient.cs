﻿using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.User;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.PO;
namespace PurchaseManager.Shared.Services;

public partial class PurchaseOrderApiClient : BaseApiClient, IPurchaseOrderApiClient
{
    private const string BaseUrl = "api/PurchaseOrder";
    public PurchaseOrderApiClient(HttpClient httpClient, ILogger<BaseApiClient> logger, string rootApiPath = "api/data/") : base(
    httpClient, logger, rootApiPath)
    { }
    /// <summary>
    ///     Create a header by vendor number
    /// </summary>
    /// <param name="vendorNumber"></param>
    /// <param name="contactNumber"></param>
    /// <returns></returns>
    public async Task<ApiResponseDto<POHeaderGetDto>> CreateHeaderAsync(string vendorNumber, string contactNumber)
    {
        if (!string.IsNullOrEmpty(vendorNumber))
        {
            return await httpClient.PostJsonAsync<ApiResponseDto<POHeaderGetDto>>(
            $"{BaseUrl}/header-vendor?VendorNumber={vendorNumber}", null);
        }
        return await httpClient.PostJsonAsync<ApiResponseDto<POHeaderGetDto>>(
        $"{BaseUrl}/header-contact?contactNumber={contactNumber}", null);
    }

    public async Task<ApiResponseDto<List<CheckPaymentDayResponse>>> CheckPaymentDayByVendorNumberAsync(List<string> listVendorNumber)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<List<CheckPaymentDayResponse>>>(
        $"{BaseUrl}/vendors/verify-recent-order", listVendorNumber);
    }

    public async Task<ApiResponseDto<List<CheckPaymentDayResponse>>> CheckPaymentDayByItemNumberAsync(List<string> listItemNumber)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<List<CheckPaymentDayResponse>>>(
        $"{BaseUrl}/items/verify-recent-order", listItemNumber);
    }

    public async Task<ApiResponseDto<List<CheckPaymentDayResponse>>> CheckPaymentDayBySuggestedNumberAsync(
        List<string> listSuggestedNumber)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<List<CheckPaymentDayResponse>>>(
        $"{BaseUrl}/suggests/verify-recent-order", listSuggestedNumber);
    }

    public async Task<ApiResponseDto<POHeaderGetDto>> GetHeader(string purchaseOrderNumber)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<POHeaderGetDto>>(
        $"{BaseUrl}/header/{purchaseOrderNumber}");
    }

    /// <summary>
    ///     Get PO header by Filter
    /// </summary>
    /// <param name="filter"></param>
    /// <returns></returns>
    public async Task<ApiResponseDto<PagedResult<POHeaderGetDto>>> GetPoHeadersAsync(PurchaseOrderFilter filter)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<PagedResult<POHeaderGetDto>>>(
        $"{BaseUrl}/headers/filter?" + filter?.ToQuery());
    }

    public async Task<ApiResponseDto<GetLatestPriceDto>> GetLatestPrice(string itemCode, string unit, string vendorCode)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<GetLatestPriceDto>>(
        $"{BaseUrl}/latest-price/{itemCode}/{unit}/{vendorCode}");
    }

    public async Task<ApiResponseDto> DownloadPo(string purchaseOrderNumber)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto>(
        $"{BaseUrl}/DownloadPO?number={purchaseOrderNumber}");
    }
    public async Task<ApiResponseDto<UserInfoDto>> GetUserCreatedPOByPONumberAsync(string poNumber)
    {
        return await httpClient.GetNewtonsoftJsonAsync<ApiResponseDto<UserInfoDto>>(
        $"{BaseUrl}/user-created/{poNumber}");
    }

    public async Task<ApiResponseDto<int>> UpdateHeader(UpdatePOHeaderDto header)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>($"{BaseUrl}/header", header);
    }

    public async Task<ApiResponseDto<int>> OpenDocument(string purchaseOrderNumber)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>(
        $"{BaseUrl}/open-document/{purchaseOrderNumber}", null);
    }

    public async Task<ApiResponseDto<int>> StockOrderOpenDocument(string purchaseOrderNumber)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>(
        $"{BaseUrl}/stock-order/open-document/{purchaseOrderNumber}", null);
    }

    public async Task<ApiResponseDto<int>> ConfirmHeader(string purchaseOrderNumber)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>(
        $"{BaseUrl}/confirm-header/{purchaseOrderNumber}", null);
    }

    public async Task<ApiResponseDto<int>> ApproveHeader(string purchaseOrderNumber)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>(
        $"{BaseUrl}/approve-header/{purchaseOrderNumber}", null);
    }

    public async Task<ApiResponseDto<int>> RejectHeader(string purchaseOrderNumber)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>(
        $"{BaseUrl}/un-approve-header/{purchaseOrderNumber}", null);
    }

    public async Task<ApiResponseDto<int>> CompletedHeader(string purchaseOrderNumber, bool condition)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>(
        $"{BaseUrl}/complete-header/{purchaseOrderNumber}/{condition}", null);
    }

    public async Task<ApiResponseDto<int>> ChangeDocumentStatus(string documentNumber, int status)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>(
        $"{BaseUrl}/change-document-status/{documentNumber}/{status}", null);
    }

    public async Task<ApiResponseDto<int>> CloseDocument(string purchaseOrderNumber)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>(
        $"{BaseUrl}/close-document/{purchaseOrderNumber}", null);
    }

    /// <summary>
    ///     Delete Multiple PO
    /// </summary>
    /// <param name="purchaseCode"></param>
    /// <returns></returns>
    public async Task<ApiResponseDto> DeleteMultipleAsync(List<string> purchaseCode)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>(
        $"{BaseUrl}/header/delete", purchaseCode);
    }
}
