﻿using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Shared.Dto.Email;
using FormatWith;
using Microsoft.Extensions.Localization;

namespace PurchaseManager.Server.Factories;

public class EmailFactory : IEmailFactory
{
    private readonly IStringLocalizer<EmailFactory> L;
    private readonly string _baseUrl;
    public EmailFactory(IStringLocalizer<EmailFactory> l, IConfiguration configuration)
    {
        L = l;
        _baseUrl = configuration[$"{nameof(PurchaseManager)}:ApplicationUrl"];
    }
    // Set up the email message with the provided emailDto
    public EmailMessageDto BuildEmail(EmailRequestDto emailDto)
    {
        var emailMessage = new EmailMessageDto
        {
            Body = emailDto.Body, Subject = emailDto.Subject, IsHtml = emailDto.IsHtml
        };
        return emailMessage;
    }
    public EmailMessageDto GetPlainTextTestEmail(DateTime date)
    {
        var emailMessage = new EmailMessageDto
        {
            Body = L["PlainTextTestEmail.template"].Value
                .FormatWith(new
                {
                    date
                })
        };

        emailMessage.Subject = L["PlainTextTestEmail.subject", emailMessage.ToAddresses[0].Name];

        emailMessage.IsHtml = false;

        return emailMessage;
    }
    public EmailMessageDto BuildNewUserConfirmationEmail(string fullName, string userName, string callbackUrl)
    {
        var emailMessage = new EmailMessageDto
        {
            Body = L["NewUserConfirmationEmail.template"].Value
                .FormatWith(new
                {
                    baseUrl = _baseUrl, name = fullName, userName, callbackUrl
                }),
            Subject = L["NewUserConfirmationEmail.subject", fullName]
        };

        return emailMessage;
    }
    public EmailMessageDto BuildNewUserEmail(string fullName, string userName, string emailAddress, string password)
    {
        var emailMessage = new EmailMessageDto
        {
            Body = L["NewUserEmail.template"].Value
                .FormatWith(new
                {
                    baseUrl = _baseUrl,
                    fullName = userName,
                    userName,
                    email = emailAddress,
                    password
                }),
            Subject = L["NewUserEmail.subject", fullName]
        };

        return emailMessage;
    }
    public EmailMessageDto BuildNewUserNotificationEmail(string creator, string name, string userName, string company, string roles)
    {
        var emailMessage = new EmailMessageDto
        {
            //placeholder not actually implemented
            Body = L["NewUserNotificationEmail.template"].Value
                .FormatWith(new
                {
                    baseUrl = _baseUrl,
                    creator,
                    name,
                    userName,
                    roles,
                    company
                }),
            Subject = L["NewUserNotificationEmail.subject", userName]
        };

        return emailMessage;
    }
    public EmailMessageDto BuildForgotPasswordEmail(string name, string callbackUrl, string token)
    {
        var emailMessage = new EmailMessageDto
        {
            Body = L["ForgotPassword.template"].Value
                .FormatWith(new
                {
                    baseUrl = _baseUrl, name, callbackUrl, token
                }),
            Subject = L["ForgotPassword.subject", name]
        };

        return emailMessage;
    }
    public EmailMessageDto BuildPasswordResetEmail(string userName)
    {
        var emailMessage = new EmailMessageDto
        {
            Body = L["PasswordReset.template"].Value
                .FormatWith(new
                {
                    baseUrl = _baseUrl, userName
                }),
            Subject = L["PasswordReset.subject", userName]
        };

        return emailMessage;
    }
}
