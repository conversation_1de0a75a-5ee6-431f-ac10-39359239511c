﻿using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Validators;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace PurchaseManager.Shared.Dto.Db.Validators
{
    public class VednorValidator : LocalizedAbstractValidator<Vendor>
    {
        public VednorValidator(IStringLocalizer<Global> l) : base(l)
        {
            RuleFor(p => p.Name)
                .NotEmpty()
                .MaximumLength(160).WithName(L["Name"]);
        }
    }
}
