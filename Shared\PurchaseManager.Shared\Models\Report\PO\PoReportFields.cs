namespace PurchaseManager.Shared.Models.Report;

public class PoReportFields
{
    // [Display(Name = "Số chứng từ")]
    public bool Number { get; set; }
    public bool PayToName { get; set; }
    public bool OrderDate { get; set; }
    public bool DueDate { get; set; }
    public bool PaymentDiscount { get; set; }
    public bool PurchaserCode { get; set; }
    public bool VatregistrationNumber { get; set; }
    public bool DocumentDate { get; set; }
    public bool SourceCode { get; set; }
    public bool RequestedReceiptDate { get; set; }
    public bool PromisedReceiptDate { get; set; }
    public bool DateReceived { get; set; }
    public bool TimeReceived { get; set; }
    public bool DocumentTime { get; set; }
    public bool BuyFromVendorNumber { get; set; }
    public bool OrderingUser { get; set; }
    public bool DeliveryUser { get; set; }
    public bool InvoicingUser { get; set; }
    public bool WarehouseUser { get; set; }
    public bool PickingUser { get; set; }
    public bool PackingUser { get; set; }
    public bool CreatedAtTime { get; set; }

}
