﻿using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Services;
namespace PurchaseManager.Theme.Material.Pages.Account
{
    public class RegisterPage : ComponentBase
    {
        [Inject] NavigationManager navigationManager { get; set; }
        [Inject] AuthenticationStateProvider authStateProvider { get; set; }
        [Inject] protected AppState appState { get; set; }
        [Inject] protected IStringLocalizer<Global> L { get; set; }
        [Inject] protected IApiClient apiClient { get; set; }

        [Inject] IViewNotifier viewNotifier { get; set; }

        [CascadingParameter]
        Task<AuthenticationState> authenticationStateTask { get; set; }

        protected bool isValidVendor { get; set; }
        protected bool isTSEmployee { get; set; }
        protected bool isLoading { get; set; }
        protected string companyName { get; set; }
        protected string respMsg { get; set; } = "";

        protected RegisterVendorViewModel registerViewModel { get; set; } = new RegisterVendorViewModel();

        protected override async Task OnInitializedAsync()
        {
            var user = (await authenticationStateTask).User;

            if (user.Identity.IsAuthenticated)
                navigationManager.NavigateTo("/");
        }

        protected async Task VerifyVendor()
        {
            isLoading = true;
            try
            {
                var resp = await apiClient.CheckTaxCode(registerViewModel.TaxCode);
                if (resp.IsSuccessStatusCode)
                {
                    companyName = resp.Result.ToString();
                    respMsg = string.Empty;
                }
                else
                    respMsg = L["Tax Code invalid! Let's check again"];
                isValidVendor = resp.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["ValidateTaxCodeFailed"]);
            }
            finally
            {
                isLoading = false;
            }
        }

        protected void OnTaxCodeChanged(string value)
        {
            string result = Regex.Replace(value, @"[^0-9-]", "");
            registerViewModel.TaxCode = result;
        }
        protected async Task RegisterVendor()
        {
            try
            {
                var resp = await apiClient.CreateAccountVendor(registerViewModel);

                if (resp.IsSuccessStatusCode)
                {
                    viewNotifier.Show("New User added, but not verify by system.", ViewNotifierType.Success, L["UserCreationSuccessful"]);
                    navigationManager.NavigateTo("/");
                }
                else
                {
                    viewNotifier.Show(resp.Message, ViewNotifierType.Error, L["UserCreationFailed"]);
                }
            }
            catch (Exception ex)
            {
                viewNotifier.Show(ex.Message, ViewNotifierType.Error, L["UserCreationFailed"]);
            }
            finally
            {
                isLoading = false;
            }
        }
    }
}
