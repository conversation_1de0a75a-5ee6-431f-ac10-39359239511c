﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>


    <ItemGroup>
        <ProjectReference Include="..\PurchaseManager.Infrastructure.AuthorizationDefinitions\PurchaseManager.Infrastructure.AuthorizationDefinitions.csproj"/>
        <ProjectReference Include="..\PurchaseManager.Infrastructure.Storage\PurchaseManager.Infrastructure.Storage.csproj"/>
        <ProjectReference Include="..\PurchaseManager.Shared\PurchaseManager.Shared.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <None Update="Server\IAdminManager.tt">
            <LastGenOutput>IAdminManager.cs</LastGenOutput>
            <Generator>TextTemplatingFileGenerator</Generator>
        </None>
    </ItemGroup>

    <ItemGroup>
        <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}"/>
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Server\IAdminManager.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>IAdminManager.tt</DependentUpon>
        </Compile>
    </ItemGroup>

</Project>
