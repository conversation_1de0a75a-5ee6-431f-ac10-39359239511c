﻿using Microsoft.AspNetCore.Components;

using MudBlazor;

using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Models.Item;
using PurchaseManager.Theme.Material.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Pages.Items.ListItem;
public partial class ListItemPage : ItemsTableBase<DetailItemDto>
{
    [Inject] public NavigationManager navigation { get; set; }
    protected List<string> clickedEvents = new();
    protected MudTextField<string> SearchRef { get; set; }

    protected ItemFilter itemFilter { get; set; } = new();

    protected bool isLoading { get; set; } = true;


    protected override void OnInitialized()
    {
        from = "GetItems";
        queryParameters = itemFilter;
        base.OnInitialized();
        isLoading = false;

    }
    protected override async Task OnSearch(string text)
    {
        itemFilter.Query = text;
        apiClient.ClearEntitiesCache();
        await Reload();
    }
}
