﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PurchaseManager.Infrastructure.Storage.Permissions
{
    public static partial class Permissions
    {
        public static class PO
        {

            [Display(Name = "CreatePOPermission")]
            public const string Create = "PO.Create";

            [Display(Name = "UpdatePOPermission")]
            public const string Update = "PO.Update";

            [Display(Name = "ReadPOPermission")]
            public const string Read = "PO.Read";

            [Display(Name = "DeletePOPermission")]
            public const string Delete = "PO.Delete";

        }
    }
}
