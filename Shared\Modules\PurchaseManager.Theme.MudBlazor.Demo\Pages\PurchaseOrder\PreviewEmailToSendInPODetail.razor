@page "/po/send-mail/preview"
@inherits PreviewEmailToSendInPODetailBase
@layout BlankLayout
@if (isLoading)
{
    <MudProgressLinear />
}
else if (!isLoading && isValidParams)
{
    @if (emailBody is not null && emailBody != "")
    {
        <MudCard Class="my-2" Style="position:relative;">
            <MudOverlay Visible="isProcessing" DarkBackground="true" Absolute="true">
                <MudProgressCircular Color="Color.Secondary" Indeterminate="true" />
            </MudOverlay>
            <MudStack Class="my-3 px-8 " Row Justify="Justify.SpaceBetween">
                <MudIconButton Icon="@Icons.Material.Filled.ArrowBack"
                    OnClick="@(_ => navigationManager.NavigateTo($"/po/{PONumber}/detail"))" Color="Color.Default" />
                <MudText Typo="Typo.h6">Preview Email to send to @VendorName</MudText>
                <MudButton Variant="Variant.Filled" StartIcon="@Icons.Material.Filled.Send" OnClick="SendEmail"
                    Color="Color.Primary">Send</MudButton>
            </MudStack>
            <MudBlazor.Extensions.Components.MudExHtmlEdit Style="min-height:1000px" Class="border-0" ReadOnly="true"
                Value="@emailBody" />
        </MudCard>
    }
}
else if (!isValidParams)
{
    <PageNotFound /> 
}