﻿using System.ComponentModel.DataAnnotations.Schema;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

public partial class PurchasePrice
{
    public string Number { get; set; }

    public string VendorNumber { get; set; }

    public string ItemNumber { get; set; }

    public string PurchasingUnit { get; set; }

    public int Vat { get; set; }

    public int DiscountBySkus { get; set; }

    public decimal PriceBefVat { get; set; }

    public decimal PriceAftVat { get; set; }

    public decimal PriceAftDiscountNoVat { get; set; }

    public decimal PriceAftDiscountVat { get; set; }

    public int Status { get; set; }

    public string FoCpromotion { get; set; } = string.Empty;

    /// <summary>
    /// Personal change
    /// </summary>
    public string Pic { get; set; }

    public string ItemCategory { get; set; }

    public string GroupProduct { get; set; }

    public DateTime? CreateAt { get; set; }

    public DateTime? LastUpdateAt { get; set; }

    public string CreateBy { get; set; }

    public string LastUpdateBy { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }
    public virtual Item ItemNumberNavigation { get; set; }

    public virtual ICollection<PurchasePriceFieldChangeLog> PurchasePriceFieldChangeLogs { get; set; } =
        new List<PurchasePriceFieldChangeLog>();

    public virtual Vendor VendorNumberNavigation { get; set; }
}
