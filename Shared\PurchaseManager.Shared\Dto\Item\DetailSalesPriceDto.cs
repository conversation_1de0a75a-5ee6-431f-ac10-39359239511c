﻿namespace PurchaseManager.Shared.Dto.Item;

public class DetailSalesPriceDto
{
    public string? ItemNumber { get; set; }
    public DateTime? StartingDate { get; set; }
    public string? UnitOfMeasureCode { get; set; }
    public decimal UnitPrice { get; set; }
    public DateTime EndingDate { get; set; }
    public string? SourceCode { get; set; }
    public decimal Quantity { get; set; }
    public decimal QuantityPerUnitOfMeasure { get; set; }
    public string? Description { get; set; }

    public int Block { get; set; }

    // No remove ? from DateTime
    public DateTime? LastDateModified { get; set; }
    public string LoginId { get; set; } = default!;
}
