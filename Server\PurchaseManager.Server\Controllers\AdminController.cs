﻿//Autogenerated from AdminController.tt
using AutoMapper;

using Finbuckle.MultiTenant;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
// using NSwag.Annotations;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.Permissions;
using PurchaseManager.Shared.Dto.Admin;

using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Controllers
{
    /// <summary>
    /// This controller is the entry API for the platform administration.
    /// </summary>
    //[OpenApiIgnore]
    [Route("api/[controller]")]
    [ApiController]
    public class AdminController : ControllerBase
    {
        private readonly IMapper _autoMapper;
        private readonly IAdminManager _adminManager;

        public AdminController(IMapper autoMapper, IAdminManager adminManager)
        {
            _autoMapper = autoMapper;
            _adminManager = adminManager;
        }

        [HttpGet("Tenant")]
        public ApiResponse Get()
            => new ApiResponse(Status200OK, string.Empty, _autoMapper.Map<TenantDto>(HttpContext.GetMultiTenantContext<TenantInfo>().TenantInfo));

        [HttpGet("Users")]
        [Authorize(Permissions.User.Read)]
        public async Task<ApiResponse> GetUsers([FromQuery] int pageSize = 10, [FromQuery] int pageNumber = 0)
            => await _adminManager.GetUsers(pageSize, pageNumber);

        [HttpGet("Permissions")]
        [Authorize]
        public ApiResponse GetPermissions([FromQuery] string? name)
            => _adminManager.GetPermissions(name);



        #region Roles
        [HttpGet("Roles")]
        [Authorize(Permissions.Role.Read)]
        public async Task<ApiResponse> GetRoles([FromQuery] int pageSize = 10, [FromQuery] int pageNumber = 0)
            => await _adminManager.GetRolesAsync(pageSize, pageNumber);

        [HttpGet("Role/{name}")]
        [Authorize]
        public async Task<ApiResponse> GetRoleAsync(string name)
            => await _adminManager.GetRoleAsync(name);


        [HttpPost("Role")]
        [Authorize(Permissions.Role.Create)]
        public async Task<ApiResponse> CreateRoleAsync([FromBody] RoleDto roleDto)
            => await _adminManager.CreateRoleAsync(roleDto);


        [HttpPut("Role")]
        [Authorize(Permissions.Role.Update)]
        public async Task<ApiResponse> UpdateRoleAsync([FromBody] RoleDto roleDto)
            => await _adminManager.UpdateRoleAsync(roleDto);


        [HttpDelete("Role/{name}")]
        [Authorize(Permissions.Role.Delete)]
        public async Task<ApiResponse> DeleteRoleAsync(string name)
            => await _adminManager.DeleteRoleAsync(name);
        #endregion

        #region Tenants
        [HttpGet("Tenants")]
        [Authorize(Permissions.Tenant.Read)]
        public async Task<ApiResponse> GetTenants([FromQuery] int pageSize = 10, [FromQuery] int pageNumber = 0)
            => await _adminManager.GetTenantsAsync(pageSize, pageNumber);

        [HttpGet("Tenant/{id}")]
        [Authorize]
        public async Task<ApiResponse> GetTenantAsync(string id)
            => await _adminManager.GetTenantAsync(id);


        [HttpPost("Tenant")]
        [Authorize(Permissions.Tenant.Create)]
        public async Task<ApiResponse> CreateTenantAsync([FromBody] TenantDto tenantDto)
            => await _adminManager.CreateTenantAsync(tenantDto);


        [HttpPut("Tenant")]
        [Authorize(Permissions.Tenant.Update)]
        public async Task<ApiResponse> UpdateTenantAsync([FromBody] TenantDto tenantDto)
            => await _adminManager.UpdateTenantAsync(tenantDto);


        [HttpDelete("Tenant/{id}")]
        [Authorize(Permissions.Tenant.Delete)]
        public async Task<ApiResponse> DeleteTenantAsync(string id)
            => await _adminManager.DeleteTenantAsync(id);
        #endregion
    }



}


