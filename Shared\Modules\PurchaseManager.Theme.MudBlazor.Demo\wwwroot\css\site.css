.jobs-container {
    display: flex;
    justify-content: space-around;
}

.job-status {
    display: flex;
    flex-direction: column;
    width: 312px;
}

    .job-status ul {
        flex: 1;
    }

.dropzone {
    padding: 30px;
    border: 1px dashed #75868a;
    list-style: none;
}

.no-drop {
    border: 2px dashed red;
}

.can-drop {
    border: 2px dashed green;
}

.draggable {
    margin-bottom: 10px;
    padding: 10px 25px;
    border: 1px solid #424d5c;
    cursor: grab;
    background: #5c6b7f;
    color: #ffffff;
    border-radius: 5px;
    width: 250px;
}

    .draggable:active {
        cursor: grabbing;
    }

.dragging {
    cursor: grabbing;
}

.description {
    font-size: 18px;
}

.last-updated {
    margin-bottom: 0;
    font-size: 11px;
    color: #e1e5ea;
    font-weight: bold;
}

    .last-updated small {
        text-transform: uppercase;
        color: #c4cbd4;
        font-size: 11px;
    }

h1, h2, h3 {
    color: var(--mud-palette-primary-darken);
}
p {
    margin-bottom: 12px;
}
a {
    color: var(--mud-palette-primary-lighten);
}


.mud-overlay.mud-overlay-dialog {
    z-index: 99;
}