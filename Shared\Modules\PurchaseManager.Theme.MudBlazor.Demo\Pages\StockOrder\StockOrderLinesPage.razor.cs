﻿using AutoMapper;
using Karambolo.Common;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using MudBlazor;
using NetTopologySuite.Utilities;
using ObjectCloner.Extensions;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Dto.User;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.StockOrder;
using PurchaseManager.Shared.Providers;
using SOLineGetDto = PurchaseManager.Shared.Dto.StockOrder.SOLineGetDto;
namespace PurchaseManager.Theme.Material.Demo.Pages.StockOrder;

public partial class StockOrderLinesPage : ComponentBase, IAsyncDisposable
{
    [Parameter]
    public string DocumentNo { get; set; }
    [Parameter]
    public string DocumentStatus { get; set; } = string.Empty;
    [Inject]
    private IVendorApiClient VendorApiClient { get; set; }
    [Inject]
    public IPurchaseOrderApiClient PurchaseOrderApiClient { get; set; }
    [Inject]
    public IStockOrderApiClient StockOrderApiClient { get; set; }
    private bool IsProcessing { get; set; }
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    [Inject]
    protected IStringLocalizer<Global> L { get; set; }
    [Inject]
    protected IMapper Mapper { get; set; }
    [Inject]
    protected NavigationManager NavigationManager { get; set; }
    private List<SOLineGetDto> PoLines { get; set; } = [];
    // private List<EditStockOrderDto> ListStockOrderEditing { get; set; } = [];
    private List<SOLineGetDto> OriginalPoLines { get; set; } = [];
    [Inject]
    private AuthenticationStateProvider AuthStateProvider { get; set; }
    private POHeaderGetDto PoHeader { get; set; } = new POHeaderGetDto();
    private GetVendorDto VendorInfo { get; set; } = new GetVendorDto();
    private bool IsEdit { get; set; }

    /// <summary>
    /// Kiểm tra có được phép edit không (không cho edit nếu có StockOrder với status = 3)
    /// </summary>
    private bool CanEdit => !HasCompletedStockOrders();

    /// <summary>
    /// Kiểm tra có StockOrder nào đã hoàn thành (status = 3) không
    /// </summary>
    private bool HasCompletedStockOrders()
    {
        return ListStockOrderDtos.Any(x => x.Status == 3);
    }

    /// <summary>
    /// Kiểm tra StockOrder cụ thể có được phép edit không
    /// </summary>
    private bool CanEditStockOrder(GetStockOrderDto stockOrder)
    {
        return stockOrder.Status != 3;// Không cho edit nếu status = 3 (đã tạo phiếu nhập)
    }

    /// <summary>
    /// Kiểm tra có được phép thêm StockOrder cho POLine này không
    /// </summary>
    private bool IsArrowDisabled(SOLineGetDto poLine)
    {
        // Không cho thêm nếu item này đã có StockOrder với status = 3 (đã tạo phiếu nhập)
        return ListStockOrderDtos.Any(x => x.ItemNumber == poLine.ItemNumber && x.Status == 3);
    }

    /// <summary>
    /// Xử lý khi click arrow để thêm StockOrder từ POLine
    /// </summary>
    private void OnArrowClick(SOLineGetDto poLine)
    {
        if (IsArrowDisabled(poLine))
        {
            ViewNotifier.Show("Item này đã tạo phiếu nhập, không thể thêm mới!", ViewNotifierType.Warning);
            return;
        }

        // Logic thêm StockOrder từ POLine
        // TODO: Implement logic to add StockOrder from POLine
        ViewNotifier.Show($"Thêm StockOrder cho item {poLine.ItemNumber}", ViewNotifierType.Info);
    }
    [Inject]
    protected IDialogService DialogService { get; set; }
    protected UserInfoDto UserCreatedPO { get; set; } = new UserInfoDto();
    private List<GetStockOrderDto> ListStockOrderDtos { get; set; } = [];
    private IEnumerable<GetStockOrderDto> ListStockOrderEditing { get; set; } = [];
    private GetStockOrderDto GetStockOrderDtoEditing { get; set; } = new GetStockOrderDto();
    private GetStockOrderDto StockOrderBeforeEdit { get; set; }
    private bool HasAnyStockOrderEditing { get; set; }
    private SOLineGetDto SOLineSelected { get; set; }
    private readonly DateTime _minDate = DateTime.UtcNow.AddDays(1);
    private string ItemFilterInPOLineTable { get; set; }
    private bool IsShowBuyerInfo { get; set; }
    private bool IsShowAddOrEditDialog { get; set; }
    private bool HasActualChanges { get; set; }
    private int OriginalStatus { get; set; }
    private int MaxAllowedQuantity { get; set; }
    protected override async Task OnInitializedAsync()
    {
        IsProcessing = true;
        await LoadDataAsync();
        await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
        IsProcessing = false;
        await base.OnInitializedAsync();
    }
    private async Task LoadDataAsync()
    {
        await GetDetailPo(DocumentNo);
        await GetHeaderPo(DocumentNo);
        await GetUserCreatePOAsync();
        await LoadStockOrderAsync();
        SOLineSelected = null;
    }
    protected async Task GetItemFromStockOrder(string itemNumber, string itemName)
    {
        await GetDetailPo(DocumentNo);
        ListStockOrderEditing = [];
        GetStockOrderDtoEditing = ListStockOrderDtos.FirstOrDefault(x => x.ItemNumber == itemNumber);
        if (GetStockOrderDtoEditing is null)
        {
            var lineSelected = PoLines.FirstOrDefault(x => x.ItemNumber == itemNumber);
            if (lineSelected is not null)
            {
                GetStockOrderDtoEditing = new GetStockOrderDto
                {
                    ItemNumber = itemNumber,
                    ItemName = itemName,
                    TotalQuantity = (int)lineSelected.QuantityToReceive
                };
            }
        }
        ListStockOrderEditing = ListStockOrderDtos.Where(x => x.ItemNumber == itemNumber).Select((x, i) => { x.Index = i + 1; return x; }).ToList();
    }
    private async Task GetDetailPo(string number)
    {
        if (number.IsNullOrEmpty())
        {
            ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
        }
        try
        {
            PoLines = [];
            OriginalPoLines = [];
            var apiResponse = await StockOrderApiClient.GetLinesForStockOrder(DocumentNo);
            if (!apiResponse.IsSuccessStatusCode)
            {
                ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
            }
            if (apiResponse.Result != null)
            {
                PoLines =
                [
                    ..apiResponse.Result.OrderByDescending(x => x.QuantityToReceive)
                ];
                OriginalPoLines =
                [
                    ..apiResponse.Result.OrderByDescending(x => x.QuantityToReceive)
                ];
            }
            StateHasChanged();
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.ToString(), ViewNotifierType.Error);
        }
    }
    private async Task GetHeaderPo(string number)
    {
        if (string.IsNullOrEmpty(number))
        {
            ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
        }
        else
        {
            try
            {
                var apiResponse = await PurchaseOrderApiClient.GetHeader(DocumentNo);
                if (!apiResponse.IsSuccessStatusCode)
                {
                    ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
                    return;
                }
                PoHeader = apiResponse.Result;
                if (PoHeader != null)
                {
                    var getVendorByVendorNumber = await VendorApiClient.GetSingleVendor(PoHeader.BuyFromVendorNumber);
                    if (!getVendorByVendorNumber.IsSuccessStatusCode)
                    {
                        ViewNotifier.Show(L["Vendor Not Found"], ViewNotifierType.Error);
                    }
                    else
                    {
                        VendorInfo = getVendorByVendorNumber.Result;
                    }
                }
                StateHasChanged();
            }
            catch (Exception e)
            {
                ViewNotifier.Show(e.ToString(), ViewNotifierType.Error);
            }
        }
    }
    private async Task SaveChangeHeaderPoOrOpenPo(bool isChange)
    {
        try
        {
            if (isChange)
            {
                var result = await DialogService.ShowMessageBox(
                "Confirm",
                "Are you sure you want to save changes?",
                "Saved", cancelText: "Cancel");
                if (result != null)
                {
                    await HandleStatusHeader();
                    StateHasChanged();
                }
            }
            else
            {
                await GetDetailPo(DocumentNo);
                await GetHeaderPo(DocumentNo);
                await LoadStockOrderAsync();
                IsEdit = false;
            }
            await GetHeaderPo(DocumentNo);
            await GetDetailPo(DocumentNo);
            await LoadStockOrderAsync();
            StateHasChanged();
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.Message, ViewNotifierType.Error);
        }
    }
    private async Task OpenedPo()
    {
        // Lưu status gốc trước khi mở
        OriginalStatus = PoHeader.Status;
        HasActualChanges = false;
        var openDocument = await PurchaseOrderApiClient.StockOrderOpenDocument(DocumentNo);
        if (!openDocument.IsSuccessStatusCode)
        {
            ViewNotifier.Show(openDocument.Message, ViewNotifierType.Error);
            return;
        }
        IsEdit = true;
        ViewNotifier.Show($"PO:{DocumentNo} is opened", ViewNotifierType.Success);
        // Reload header update new status
        await GetHeaderPo(DocumentNo);
        StateHasChanged();
    }
    private void OnDateChanged(string value, GetStockOrderDto item)
    {
        item.ExpirationDateText = value;
        if (DateTime.TryParseExact(value, "dd/MM/yyyy", null, System.Globalization.DateTimeStyles.None, out var date))
        {
            if (date < _minDate)
            {
                item.HasError = true;
                item.ErrorText = $"Date must be greater than or equal to {_minDate:dd/MM/yyyy}";
            }
            else
            {
                item.HasError = false;
                item.ErrorText = "";
                item.ExpirationDate = date;
            }
        }
        else
        {
            item.HasError = true;
            item.ErrorText = "Invalid date format (dd/MM/yyyy required)";
        }
    }
    protected async Task GetUserCreatePOAsync()
    {
        try
        {
            var resp = await PurchaseOrderApiClient.GetUserCreatedPOByPONumberAsync(DocumentNo);
            if (resp.IsSuccessStatusCode)
            {
                UserCreatedPO = resp.Result;
            }
            else
            {
                ViewNotifier.Show(resp.Message, ViewNotifierType.Error);
            }
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.Message, ViewNotifierType.Error);
        }
    }
    private async Task LoadStockOrderAsync()
    {
        var filter = new StockOrderFilter
        {
            PageIndex = 0,
            PageSize = 1000,
            HeaderNumber = DocumentNo
        };
        var apiResponse = await StockOrderApiClient.GetStockOrderByPoHeaderAsync(filter);
        if (apiResponse.IsSuccessStatusCode)
        {
            ListStockOrderDtos = apiResponse.Result.Data;
            ListStockOrderEditing =
            [
                .. ListStockOrderDtos.Select((x, index) =>
                {
                    x.Index = index + 1;
                    return x;
                })
            ];
            // TotalItems = apiResponse.Result.RowCount;
        }
        else
        {
            ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Error);
        }
    }
    private string SelectedRowClassFunc(SOLineGetDto sOLineGetDto, int rowNumber)
    {
        var classes = "";
        var isOverReceived = sOLineGetDto.QuantityReceived - sOLineGetDto.Quantity > 0;
        if (isOverReceived)
        {
            classes += "received-over";
        }
        else if (sOLineGetDto.QuantityReceived == sOLineGetDto.Quantity)
        {
            classes += "received-full";
        }
        if (SOLineSelected is null || SOLineSelected.ItemNumber != sOLineGetDto.ItemNumber ||
            SOLineSelected.LineNumber != sOLineGetDto.LineNumber || SOLineSelected.DocumentType != sOLineGetDto.DocumentType)
        {
            return classes;
        }
        classes += " selected";
        return classes;
    }
    private async Task RowClickEvent(TableRowClickEventArgs<SOLineGetDto> tableRowClickEventArgs)
    {
        var selectedLine = (SOLineGetDto)tableRowClickEventArgs.Row.Item;
        if (selectedLine == null)
        {
            return;
        }
        await GetItemFromStockOrder(selectedLine.ItemNumber, selectedLine.ItemName);
        SOLineSelected = selectedLine;
        IsShowAddOrEditDialog = true;
    }
    #region Table editing
    private void BackupItem(object getStockOrderDto)
    {
        var selectedSO = (GetStockOrderDto)getStockOrderDto.DeepClone();
        if (selectedSO is not null && selectedSO.IsCreateSO)
        {
            return;
        }
        StockOrderBeforeEdit = selectedSO;
        HasAnyStockOrderEditing = true;
    }
    private bool CanEditStockOrderRow(GetStockOrderDto item)
    {
        // Ví dụ: chỉ cho phép edit nếu thuộc tính CanEdit true
        return !item.IsCreateSO && SOLineSelected.ItemNumber == item.ItemNumber && SOLineSelected.LineNumber == item.LineNumber;
    }
    private async Task ItemHasBeenCommittedAsync(object poLineGetDto)
    {
        var stockOrderToUpdate = (GetStockOrderDto)poLineGetDto;
        // Validate before saving
        if (!ValidateQuantityInput(stockOrderToUpdate, stockOrderToUpdate.QuantityReceived))
        {
            ViewNotifier.Show("Cannot save due to validation errors. Please check the quantity input.", ViewNotifierType.Error);
            stockOrderToUpdate.QuantityReceived = 0;
            return;
        }
        if (stockOrderToUpdate.IsAdding)
        {
            var stockOrder = new CreateStockOrderDto
            {
                DocumentType = stockOrderToUpdate.DocumentType,
                LineNumber = stockOrderToUpdate.LineNumber,
                LotNo = stockOrderToUpdate.LotNo,
                ExpirationDate = stockOrderToUpdate.ExpirationDate,
                QuantityReceived = stockOrderToUpdate.QuantityReceived,
                HeaderNumber = DocumentNo,
                ItemNumber = stockOrderToUpdate.ItemNumber,
                TotalQuantity = stockOrderToUpdate.TotalQuantity,
                ItemName = stockOrderToUpdate.ItemName
            };
            var createStockOrder = await StockOrderApiClient.CreateMultipleStockOrder(stockOrder);
            if (!createStockOrder.IsSuccessStatusCode)
            {
                ViewNotifier.Show(createStockOrder.Message, ViewNotifierType.Error);
                return;
            }
            ViewNotifier.Show(createStockOrder.Message, ViewNotifierType.Success);
            await GetDetailPo(DocumentNo);
            stockOrderToUpdate.IsAdding = false;
            HasActualChanges = true;// Đánh dấu có thao tác thực sự
        }
        else
        {
            var req = Mapper.Map<UpdateStockOrderDto>(stockOrderToUpdate);
            // Status will be updated to Saved (2) when user clicks Save button for all draft items
            var resp = await StockOrderApiClient.UpdateStockOrderAsync(stockOrderToUpdate.Number, req);
            if (!resp.IsSuccessStatusCode) ViewNotifier.Show(resp.Message, ViewNotifierType.Error);
            else
            {
                ViewNotifier.Show(resp.Message, ViewNotifierType.Success);
                await GetDetailPo(DocumentNo);
                await GetHeaderPo(DocumentNo);
                HasActualChanges = true;// Đánh dấu có thao tác thực sự
            }
        }
        HasAnyStockOrderEditing = false;
        await LoadStockOrderAsync();
        if (SOLineSelected != null)
        {
            await GetItemFromStockOrder(SOLineSelected.ItemNumber, SOLineSelected.ItemName);//load lại stock order cho table
        }
        StateHasChanged();
    }
    private void ResetItemToOriginalValues(object getStockOrderDto)
    {
        ((GetStockOrderDto)getStockOrderDto).Number = StockOrderBeforeEdit.Number;
        ((GetStockOrderDto)getStockOrderDto).HeaderNumber = StockOrderBeforeEdit.HeaderNumber;
        ((GetStockOrderDto)getStockOrderDto).LineNumber = StockOrderBeforeEdit.LineNumber;
        ((GetStockOrderDto)getStockOrderDto).DocumentType = StockOrderBeforeEdit.DocumentType;
        ((GetStockOrderDto)getStockOrderDto).ItemNumber = StockOrderBeforeEdit.ItemNumber;
        ((GetStockOrderDto)getStockOrderDto).LotNo = StockOrderBeforeEdit.LotNo;
        ((GetStockOrderDto)getStockOrderDto).ExpirationDate = StockOrderBeforeEdit.ExpirationDate;
        ((GetStockOrderDto)getStockOrderDto).TotalQuantity = StockOrderBeforeEdit.TotalQuantity;
        ((GetStockOrderDto)getStockOrderDto).QuantityReceived = StockOrderBeforeEdit.QuantityReceived;
        ((GetStockOrderDto)getStockOrderDto).ItemName = StockOrderBeforeEdit.ItemName;
        ((GetStockOrderDto)getStockOrderDto).Note = StockOrderBeforeEdit.Note;
        ((GetStockOrderDto)getStockOrderDto).CreateBy = StockOrderBeforeEdit.CreateBy;
        ((GetStockOrderDto)getStockOrderDto).Status = StockOrderBeforeEdit.Status;// Restore status
        HasAnyStockOrderEditing = false;
    }
    private void OnPOLineSelectedChanged(SOLineGetDto sOLineGetDto)
    {
        // if (HasAnyStockOrderEditing && sOLineGetDto != SOLineSelected)
        // {
        //     return;
        // }
        SOLineSelected = sOLineGetDto;
    }
    private void OnAddStockOrderClicked()
    {
        // if (HasAnyStockOrderEditing) return;
        if (SOLineSelected == null)
        {
            return;
        }
        var newStockOrder = new GetStockOrderDto
        {
            LineNumber = SOLineSelected.LineNumber,
            DocumentType = SOLineSelected.DocumentType,
            ItemNumber = SOLineSelected.ItemNumber,
            LotNo = SOLineSelected.LotNo,
            TotalQuantity = 0,
            IsAdding = true,
            QuantityReceived = 0,
            ItemName = SOLineSelected.ItemName,
            ExpirationDate = DateTime.Today.AddDays(30)
        };
        newStockOrder.IsAdding = true;
        newStockOrder.TotalQuantity = (int)SOLineSelected.QuantityInvoiced;
        newStockOrder.LotNo = "notset";
        newStockOrder.Number = DateTime.Now.Ticks.ToString();
        var prevList = ListStockOrderEditing.ToList();
        prevList.Insert(0, newStockOrder);
        ListStockOrderEditing = [];
        ListStockOrderEditing = prevList.Select((x, i) => { x.Index = i + 1; return x; }).ToList();
        MaxAllowedQuantity = (int)(SOLineSelected?.Quantity ?? 0) - (int)(SOLineSelected?.QuantityReceived ?? 0);
    }
    private void OnRemoveStockOrderClicked(string number)
    {
        if (HasAnyStockOrderEditing) return;
        ListStockOrderEditing = ListStockOrderEditing.Where(x => x.Number != number).ToList();
    }
    private bool CanOpen()
    {
        return PoHeader.Status != (int)PurchaseOrderEnum.Completed
               && PoHeader.Status > (int)PurchaseOrderEnum.Approve;
    }
    protected void OnFilterItem(string value)
    {
        ItemFilterInPOLineTable = value;
        if (string.IsNullOrEmpty(value))
        {
            PoLines = OriginalPoLines;
        }
        if (value == null)
        {
            return;
        }
        var trim = value.Trim();
        PoLines = OriginalPoLines
            .Where(x => x.ItemName.Contains(trim, StringComparison.CurrentCultureIgnoreCase) || x.ItemNumber.Contains(trim, StringComparison.CurrentCultureIgnoreCase))
            .ToList();
    }
    #endregion
    private async Task HandleStatusHeader()
    {
        await GetDetailPo(DocumentNo);
        await HandleStatusStockOrder();
        if (!HasActualChanges)
        {
            var revertStatus = await PurchaseOrderApiClient.ChangeDocumentStatus(DocumentNo, OriginalStatus);
            if (!revertStatus.IsSuccessStatusCode)
            {
                ViewNotifier.Show($"Cannot revert to original status: {revertStatus.Message}", ViewNotifierType.Error);
            }
            var closeDocument = await PurchaseOrderApiClient.CloseDocument(DocumentNo);
            if (!closeDocument.IsSuccessStatusCode)
            {
                ViewNotifier.Show(closeDocument.Message, ViewNotifierType.Error);
                return;
            }
            IsEdit = false;
            return;
        }

        var allLinesCompleted = true;
        var hasAnyReceived = false;
        foreach (var line in PoLines)
        {
            // Nếu có dòng nào nhận chưa đủ (quantityReceived < quantity)
            if (line.QuantityReceived < line.Quantity)
            {
                allLinesCompleted = false;
            }
            // Nếu có dòng nào đã nhận hàng
            if (line.QuantityReceived > 0)
            {
                hasAnyReceived = true;
            }
        }
        if (allLinesCompleted && hasAnyReceived)
        {
            // Tất cả dòng đã nhận đủ hoặc nhiều hơn số lượng đặt -> Completed
            var updateHeader = await PurchaseOrderApiClient.CompletedHeader(DocumentNo, true);
            if (!updateHeader.IsSuccessStatusCode)
            {
                ViewNotifier.Show(updateHeader.Message, ViewNotifierType.Error);
                return;
            }
        }
        else if (hasAnyReceived)
        {
            // Có dòng đã nhận nhưng chưa đủ -> Partially Received
            var updateHeader = await PurchaseOrderApiClient.CompletedHeader(DocumentNo, false);
            if (!updateHeader.IsSuccessStatusCode)
            {
                ViewNotifier.Show(updateHeader.Message, ViewNotifierType.Error);
                return;
            }
        }
        //Close Document
        var closeDocumentFinal = await PurchaseOrderApiClient.CloseDocument(DocumentNo);
        if (!closeDocumentFinal.IsSuccessStatusCode)
        {
            ViewNotifier.Show(closeDocumentFinal.Message, ViewNotifierType.Error);
            return;
        }
        IsEdit = false;
        ViewNotifier.Show(L["Update purchase orders details success"], ViewNotifierType.Success);
    }
    // Validation method for quantity input
    private bool ValidateQuantityInput(GetStockOrderDto item, int newQuantity)
    {
        if (newQuantity <= 0)
        {
            item.HasError = true;
            item.ErrorText = "Quantity must be greater than 0";
            return false;
        }
        // Calculate total without current item
        var totalOtherItems = ListStockOrderEditing.Where(x => x.Number == item.Number && x.LineNumber == item.LineNumber).Sum(x => x.QuantityReceived);
        var totalWithNewQuantity = totalOtherItems;
        if (totalOtherItems > MaxAllowedQuantity)
        {
            item.HasError = true;
            item.ErrorText = $"Total quantity received ({totalWithNewQuantity}) exceeds ordered quantity ({MaxAllowedQuantity})";
            return false;
        }
        item.HasError = false;
        item.ErrorText = string.Empty;
        return true;
    }
    // Check if there are any validation errors in stock orders
    public async ValueTask DisposeAsync()
    {
        if (IsEdit)
        {
            // When there is a problem with the browser: F5, close,... it does not automatically save.
            await HandleStatusHeader();
        }
    }
    public int GetMaxQuantityForCurrentInput(GetStockOrderDto item)
    {
        var quantityReceived = ListStockOrderEditing.Where(x => x.ItemNumber == item.ItemNumber && x.LineNumber == item.LineNumber).Sum(x => x.QuantityReceived);
        var result = MaxAllowedQuantity - quantityReceived;
        return result > 0 ? result : (int)SOLineSelected.QuantityToReceive;
    }

    private async Task HandleStatusStockOrder()
    {
        var saveDraftsResult = await StockOrderApiClient.SaveDraftStockOrdersAsync(DocumentNo);
        if (!saveDraftsResult.IsSuccessStatusCode)
        {
            ViewNotifier.Show($"Cannot save draft stock orders: {saveDraftsResult.Message}", ViewNotifierType.Warning);
        }
        else
        {
            ViewNotifier.Show("All draft stock orders have been saved", ViewNotifierType.Success);
        }
    }

    /// <summary>
    /// Đóng phiếu nhập: Lấy tất cả StockOrder chưa tạo phiếu nhập và tạo 1 Header + nhiều Lines
    /// </summary>
    private async Task SaveStockOrderAsync()
    {
        try
        {
            // Lấy tất cả StockOrder của PO này mà chưa được tạo phiếu nhập (Status != 3)
            await LoadStockOrderAsync();// Refresh data
            var pendingStockOrders = ListStockOrderDtos.Where(x => x.Status == 2).ToList();// Chỉ lấy những StockOrder đã Saved

            if (!pendingStockOrders.Any())
            {
                ViewNotifier.Show("Không có StockOrder nào đã lưu để tạo phiếu nhập", ViewNotifierType.Warning);
                return;
            }

            // Hiển thị dialog xác nhận
            var result = await DialogService.ShowMessageBox(
            "Xác nhận đóng phiếu nhập",
            $"Bạn có chắc chắn muốn đóng phiếu nhập cho {pendingStockOrders.Count} item? Sau khi đóng sẽ tạo StockOrderHeader và cập nhật trạng thái.",
            "Đóng phiếu nhập",
            cancelText: "Hủy");

            if (result != true) return;

            IsProcessing = true;

            // Tạo DTO cho Header và Lines từ StockOrder hiện có
            var createStockOrderDto = new CreateStockOrderHeaderDto
            {
                POHeaderNumber = DocumentNo,
                DocumentType = 1,// Default document type
                Note = $"Phiếu nhập cho PO: {DocumentNo} - {DateTime.Now:dd/MM/yyyy HH:mm}",
                Lines = []
            };

            // Chuyển đổi StockOrder thành StockOrderLines
            var stockOrderLines = pendingStockOrders
                .Select((stockOrder, _) => new CreateStockOrderLineDto
                {
                    POHeaderNumber = stockOrder.HeaderNumber,
                    POLineNumber = stockOrder.LineNumber,// Sử dụng LineNumber từ StockOrder
                    DocumentType = 1,// Default document type
                    ItemNumber = stockOrder.ItemNumber,
                    LotNo = stockOrder.LotNo ?? string.Empty,
                    ExpirationDate = stockOrder.ExpirationDate,
                    TotalQuantity = stockOrder.TotalQuantity,
                    QuantityReceived = stockOrder.QuantityReceived,
                    ItemName = stockOrder.ItemName,
                    Note = stockOrder.Note ?? string.Empty
                })
                .ToList();

            createStockOrderDto.Lines = stockOrderLines;

            // Gọi API để tạo luôn Final StockOrderHeader/Lines (không qua draft)
            var createResult = await StockOrderApiClient.CreateFinalStockOrderAsync(createStockOrderDto);
            if (!createResult.IsSuccessStatusCode)
            {
                ViewNotifier.Show($"Lỗi khi tạo StockOrderHeader: {createResult.Message}", ViewNotifierType.Error);
                return;
            }

            // Cập nhật status của các StockOrder thành "đã tạo phiếu nhập" (Status = 3)
            await UpdateStockOrderStatusToCompletedAsync(pendingStockOrders.Select(x => x.Number).ToList());

            ViewNotifier.Show($"Đóng phiếu nhập thành công!", ViewNotifierType.Success);

            // Reload data để cập nhật giao diện
            await LoadDataAsync();
            IsEdit = false;
        }
        catch (Exception ex)
        {
            ViewNotifier.Show($"Lỗi: {ex.Message}", ViewNotifierType.Error);
        }
        finally
        {
            IsProcessing = false;
            StateHasChanged();
        }
    }

    /// <summary>
    /// Cập nhật status của StockOrder thành "đã tạo phiếu nhập"
    /// </summary>
    private async Task UpdateStockOrderStatusToCompletedAsync(List<string> stockOrderNumbers)
    {
        try
        {
            // Gọi API batch update để cập nhật status hàng loạt
            var batchUpdateDto = new BatchUpdateStockOrderStatusDto
            {
                StockOrderNumbers = stockOrderNumbers,
                Status = 3// 3: Đã tạo phiếu nhập
            };

            var result = await StockOrderApiClient.BatchUpdateStockOrderStatusAsync(batchUpdateDto);
            if (!result.IsSuccessStatusCode)
            {
                ViewNotifier.Show($"Lỗi khi cập nhật status StockOrder: {result.Message}", ViewNotifierType.Warning);
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show($"Lỗi khi cập nhật status StockOrder: {ex.Message}", ViewNotifierType.Warning);
        }
    }

    /// <summary>
    /// Lấy status text của StockOrder
    /// </summary>
    private static string GetStockOrderStatusText(int status)
    {
        return status switch
        {
            1 => "Nháp",
            2 => "Đã lưu",
            3 => "Đã tạo phiếu",
            _ => "Không xác định"
        };
    }

    /// <summary>
    /// Lấy màu hiển thị cho StockOrder status
    /// </summary>
    private static Color GetStockOrderStatusColor(int status)
    {
        return status switch
        {
            1 => Color.Warning,// Nháp
            2 => Color.Info,// Đã lưu
            3 => Color.Success,// Đã tạo phiếu
            _ => Color.Default
        };
    }
}
