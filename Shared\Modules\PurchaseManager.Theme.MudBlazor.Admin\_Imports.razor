﻿@using System.Globalization
@using System.Linq
@using System.Net.Http
@using System.Net.Http.Json
@using System.Security.Claims
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Http;
@using Microsoft.Extensions.Localization;
@using static Microsoft.AspNetCore.Http.StatusCodes
@using Microsoft.JSInterop
@using PurchaseManager.Constants
@using PurchaseManager.Shared
@using PurchaseManager.Shared.Dto
@using PurchaseManager.Shared.Models.Account
@using PurchaseManager.Shared.Dto.Admin
@using PurchaseManager.Shared.Dto.Db
@using PurchaseManager.Shared.Dto.ExternalAuth
@using PurchaseManager.Infrastructure.AuthorizationDefinitions
@using PurchaseManager.Theme.Material
@using PurchaseManager.Theme.Material.Services
@using PurchaseManager.Theme.Material.Shared
@using PurchaseManager.Theme.Material.Pages
@using PurchaseManager.Theme.Material.Shared.Layouts
@using PurchaseManager.Theme.Material.Shared.Components
@using PurchaseManager.Theme.Material.Admin.Shared.Components
@using PurchaseManager.Theme.Material.Admin.Shared.Layouts
@using PurchaseManager.Shared.Extensions
@using PurchaseManager.Shared.Interfaces
@using PurchaseManager.Shared.Providers
@using PurchaseManager.Shared.Services
@using PurchaseManager.Shared.Localizer
@using PurchaseManager.UI.Base.Pages.Admin
@using PurchaseManager.UI.Base.Pages.Admin.Settings
@using PurchaseManager.UI.Base.Shared.Components
@using PurchaseManager.UI.Base.Shared.Layouts
@using Blazored.FluentValidation
@using Karambolo.Common.Localization
@using MudBlazor
@using Humanizer
