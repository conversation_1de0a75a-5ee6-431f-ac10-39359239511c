﻿using PurchaseManager.Shared.Localizer;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace PurchaseManager.Shared.Models.Account.Validators
{
    public class RegisterVendorViewModelValidator : RegisterInputModelValidator<RegisterVendorViewModel>
    {
        public RegisterVendorViewModelValidator(IStringLocalizer<Global> l) : base(l)
        {
            RuleFor(p => p.PasswordConfirm)
                .Equal(p => p.Password).WithMessage(x => L["PasswordConfirmationFailed"]).WithName(L["ConfirmPassword"]);
        }
    }
}
