﻿using System.Data;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Models.PurchasePrice;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder.Services;

public static class POExtensions
{
    public static void UpdateAmount(this POLineGetDto row)
    {
        var vat = row.Vat / 100;// 0.08
        var discount = row.LineDiscountPercent;// 8
        
        var amountB4Vat = row.UnitCost * row.Quantity;// 3 * 2  = 6

        // cal discount
        var discountAmountB4VAT = discount / 100 * amountB4Vat;

        // Số tiền trước VAT - Discount
        var amountB4VATDiscount = amountB4Vat - discountAmountB4VAT;

        var vatAmount = amountB4VATDiscount * vat;

        // Số tiền sau thuế đã trừ discount
        var amountAfterVat = amountB4VATDiscount + vatAmount;
        
        row.Amount = amountB4Vat;
        row.VatAmount = vatAmount;
        row.LineDiscountAmount = discountAmountB4VAT;
        row.VatBaseAmount = amountB4Vat - discountAmountB4VAT;
        row.AmountIncludingVat = amountAfterVat;
    }

    public static List<DuplicatePurchasePriceItemDto> GetDuplicateDetails(this List<CreatePurchasePriceDto> dataList)
    {
        var validateDtos = dataList
            .GroupBy(x => new
            {
                x.VendorNumber, x.ItemNumber
            })
            .Where(g => g.Count() > 1)
            .Select(g => new DuplicatePurchasePriceItemDto
            {
                VendorNumber = g.Key.VendorNumber,
                ItemNumber = g.Key.ItemNumber,
                DuplicateCount = g.Count(),
                DuplicatedItems = g.ToList()
            }).ToList();
        return validateDtos;
    }
    public static int GetIntValue(DataRow row, int index)
    {
        return row.ItemArray.Length > index && int.TryParse(row.ItemArray[index]?.ToString(), out var value) ? value : 0;
    }

    public static string GetStringValue(DataRow row, int index)
    {
        return row.ItemArray.Length > index && row.ItemArray[index] != null
            ? row.ItemArray[index].ToString()?.Trim() : string.Empty;
    }
    public static decimal GetDecimalValue(DataRow row, int index)
    {
        return row.ItemArray.Length > index && decimal.TryParse(row.ItemArray[index]?.ToString(), out var value) ? value : 0;
    }
}
