namespace PurchaseManager.Shared.Dto.Db;

public class SalesPriceDto : BaseDto
{
    public string ItemNumber { get; set; } = null!;
    public DateTime StartingDate { get; set; }
    public string UnitOfMeasureCode { get; set; } = null!;
    public decimal UnitPrice { get; set; }
    public DateTime EndingDate { get; set; }
    public string? SourceCode { get; set; }
    public decimal? Quantity { get; set; }
    public decimal? QuantityPerUnitOfMeasure { get; set; }
    public string? Description { get; set; }
    public List<UnitOfMeasureDto> UnitOfMeasureDtos { get; set; }


}
