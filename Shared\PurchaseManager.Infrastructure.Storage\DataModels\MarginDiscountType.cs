﻿namespace PurchaseManager.Infrastructure.Storage.DataModels;

public class MarginDiscountType
{
    public string Code { get; set; }

    public string DiscountTypeName { get; set; }

    public int RowId { get; set; }

    public int Status { get; set; }

    public bool IsBackMargin { get; set; }

    public string CreateBy { get; set; }

    public DateTime CreateDate { get; set; }

    public DateTime? LastDateModified { get; set; }

    public string LastModifiedBy { get; set; }
}
