﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Http;

using MudBlazor;
using MudBlazor.Extensions.Components;

using PurchaseManager.Shared.Dto.Email;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.UI.Base.Pages.Admin.Settings;
namespace PurchaseManager.Theme.Material.Admin.Pages.Admin.Settings
{
    public partial class EmailTemplateBase : SettingsBase
    {
        [Parameter] public int EmailTemplateId { get; set; }
        protected string value { get; set; } = "";
        protected MudExHtmlEdit mudExHtmlEditRef { get; set; }
        protected string inputHtml { get; set; } = "";
        protected bool isShowPreview { get; set; } = false;
        protected bool isLoading { get; set; }
        protected bool isCreate { get; set; }
        protected bool success;
        protected string templateName { get; set; }
        protected string subject { get; set; }
        protected string body { get; set; }
        protected string[] errors = { };
        protected MudForm form;
        [Inject] protected NavigationManager navigation { get; set; }
        protected CreateEmailTemplateDto createEmailTemplateDto { get; set; } = new CreateEmailTemplateDto();
        protected DetailEmailTemplate detailEmailTemplate { get; set; } = new DetailEmailTemplate();
        // protected override async Task OnInitializedAsync()
        // {
        //     isLoading = true;
        //     var uri = new Uri(navigation.Uri);
        //     var path = uri.LocalPath;
        //     isCreate = path.Contains("create");
        //     if (!isCreate)
        //     {
        //         await LoadEmailTemplateById();
        //     }
        //     base.OnInitialized();
        //     isLoading = false;
        // }
        protected override async Task OnParametersSetAsync()
        {
            isLoading = true;
            var uri = new Uri(navigation.Uri);
            var path = uri.LocalPath;
            isCreate = path.Contains("create");
            if (!isCreate)
            {
                await LoadEmailTemplateById();
            }
            base.OnInitialized();
            isLoading = false;
            await base.OnParametersSetAsync();
        }
        protected async Task CreateEmailTemplate()
        {
            try
            {
                await form.Validate();
                if (!form.IsValid) return;
                if (isCreate)
                {
                    createEmailTemplateDto.Body = inputHtml;
                    createEmailTemplateDto.Subject = subject;
                    createEmailTemplateDto.TemplateName = templateName;
                    var resp = await apiClient.CreateEmailTemplate(createEmailTemplateDto);
                    if (!resp.IsSuccessStatusCode) viewNotifier.Show(L[resp.Message], ViewNotifierType.Error, L["Operation Failed"]);
                    else if (resp.StatusCode == StatusCodes.Status201Created)
                    {
                        viewNotifier.Show(L["Create Success"], ViewNotifierType.Success, L["Operation Successful"]);
                    }
                }
                else
                {
                    UpdateEmailTemplateDto dto = new UpdateEmailTemplateDto() { Body = inputHtml, Hidden = false, Subject = subject, TemplateName = templateName };
                    var resp = await apiClient.UpdateEmailTemplate(dto, EmailTemplateId);
                    if (!resp.IsSuccessStatusCode) viewNotifier.Show(L[resp.Message], ViewNotifierType.Error, L["Operation Failed"]);
                    else if (resp.StatusCode == StatusCodes.Status200OK)
                    {
                        viewNotifier.Show(L["Update Success"], ViewNotifierType.Success, L["Operation Successful"]);
                    }
                }
            }
            catch (Exception ex)
            {
                viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
        }
        protected async Task UpdateEmailTemplate()
        {
            try
            {
                await form.Validate();
                if (!form.IsValid) return;
                createEmailTemplateDto.Subject = subject;
                createEmailTemplateDto.TemplateName = templateName;
                createEmailTemplateDto.Body = value;
                var resp = await apiClient.CreateEmailTemplate(createEmailTemplateDto);
                if (!resp.IsSuccessStatusCode) viewNotifier.Show(L[resp.Message], ViewNotifierType.Error, L["Operation Failed"]);
                else if (resp.StatusCode == StatusCodes.Status201Created)
                {
                    viewNotifier.Show(L["Create Success"], ViewNotifierType.Success, L["Operation Successful"]);
                }
            }
            catch (Exception ex)
            {
                viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
        }
        protected async Task LoadEmailTemplateById()
        {
            try
            {
                var resp = await apiClient.GetEmailTemplateById(EmailTemplateId);
                if (!resp.IsSuccessStatusCode) viewNotifier.Show(L[resp.Message], ViewNotifierType.Error, L["Operation Failed"]);
                else
                {
                    detailEmailTemplate = resp.Result;
                    templateName = detailEmailTemplate.TemplateName;
                    body = detailEmailTemplate.Body;
                    subject = detailEmailTemplate.Subject;
                    value = body;
                    inputHtml = body;
                }
            }
            catch (Exception ex)
            {
                viewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
        }
    }
}
