﻿using Microsoft.AspNetCore.Mvc;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
namespace PurchaseManager.Server.Controllers;

public partial class PurchaseSuggestedPaymentController
{
    [HttpPost("insert-demand-reason")]
    public async Task<ApiResponse> InsertItem(CreateDemandReasonDto item)
    {
        return await _purchaseSuggestedPaymentManager.CreateDemandReasonAsync(item);
    }

    [HttpGet("gets-demand-reason")]
    public async Task<ApiResponse> GetsAsync()
    {
        return await _purchaseSuggestedPaymentManager.GetsDemandReasonAsync();
    }

    [HttpPost("update-demand-reason")]
    public async Task<ApiResponse> UpdateItem(UpdateDemandReasonDto item)
    {
        return await _purchaseSuggestedPaymentManager.UpdateDemandReasonAsync(item);
    }

    [HttpPost("block-demand-reason/{reasonCode}")]
    public async Task<ApiResponse> BlockItem(string reasonCode)
    {
        return await _purchaseSuggestedPaymentManager.BlockDemandReasonAsync(reasonCode);
    }
}
